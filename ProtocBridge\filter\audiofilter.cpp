#include "stdafx.h"
#include "audiofilter.h"

namespace LS
{
AudioFilter::RequestList AudioFilter::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<AudioFilter::Create>());
    list.push_back(std::make_unique<AudioFilter::Remove>());

    list.push_back(std::make_unique<AudioFilter::SetAudioFilter>());
    list.push_back(std::make_unique<AudioFilter::GetAudioFilter>());
    return list;
}

void AudioFilter::SetAudioFilterInfo(AUDIO_FILTER& audioFilterInfo, const ls_audiofilter::AudioFilter& audio_filter, UINT64* cmd /* = NULL */)
{
    if (audioFilterInfo.filterType == AUDIO_FILTER_SPEEX_NOISE_SUPPRESS && audio_filter.has_speex_noise_suppress_filter())
    {
        const ls_audiofilter::SpeexNoiseSuppressFilter& speex_noise_suppress_filter = audio_filter.speex_noise_suppress_filter();
        if (speex_noise_suppress_filter.has_suppress_level())
        {
            audioFilterInfo.speexNoiseSuppressFilter.suppressLevel = speex_noise_suppress_filter.suppress_level();
            if (cmd) *cmd |= FILTER_CONTROL_SET_SUPPRESS_LEVEL;
        }
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_NOISE_SUPPRESS && audio_filter.has_sami_noise_suppress_filter())
    {
        const ls_audiofilter::SamiNoiseSuppressFilter& sami_noise_suppress_filter = audio_filter.sami_noise_suppress_filter();
        if (sami_noise_suppress_filter.has_config_file())
        {
            audioFilterInfo.samiNoiseSuppressFilter.configFile = sami_noise_suppress_filter.config_file();
            if (cmd) *cmd |= FILTER_CONTROL_SET_MODEL;
        }
        if (sami_noise_suppress_filter.has_speech_ratio())
        {
            audioFilterInfo.samiNoiseSuppressFilter.speechRatio = sami_noise_suppress_filter.speech_ratio();
            if (cmd) *cmd |= FILTER_CONTROL_SET_SPEECH_RATIO;
        }
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_COMMON_METRICS && audio_filter.has_sami_common_metrics_filter())
    {
        const ls_audiofilter::SamiCommonMetricsFilter& sami_common_metrics_filter = audio_filter.sami_common_metrics_filter();
        if (sami_common_metrics_filter.has_config_file())
        {
            audioFilterInfo.samiCommonMetricsFilter.configFile = sami_common_metrics_filter.config_file();
        }
        if (sami_common_metrics_filter.has_config_json())
        {
            audioFilterInfo.samiCommonMetricsFilter.configJson = sami_common_metrics_filter.config_json();
        }
        if (sami_common_metrics_filter.has_reset_common_metrics() && sami_common_metrics_filter.reset_common_metrics())
        {
            if (cmd) *cmd |= FILTER_CONTROL_RESET_COMMON_METRICS;
        }
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_MDSP_EFFECT && audio_filter.has_sami_mdsp_effect_filter())
    {
        const ls_audiofilter::SamiMdspEffectFilter& sami_mdsp_effect_filter = audio_filter.sami_mdsp_effect_filter();
        if (sami_mdsp_effect_filter.has_mdsp_param())
        {
            audioFilterInfo.samiMdspEffectFilter.mdspParam = sami_mdsp_effect_filter.mdsp_param();
            if (cmd) *cmd |= FILTER_CONTROL_SET_MDSP_PARAM;
        }
        
        if (sami_mdsp_effect_filter.has_model_file())
        {
            audioFilterInfo.samiMdspEffectFilter.modelFile = sami_mdsp_effect_filter.model_file();
        }
        
        if (sami_mdsp_effect_filter.has_res_file_path())
        {
            audioFilterInfo.samiMdspEffectFilter.resFilePath = sami_mdsp_effect_filter.res_file_path();
        }
    }
}

bool AudioFilter::Create::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    FILTER filterInfo{};
    filterInfo.type = FILTER_AUDIO;

    AUDIO_FILTER audioFilterInfo{};
    audioFilterInfo.filterType = (AUDIO_FILTER_TYPE)req.filter_type();

    if (req.has_audio_filter())
    {
        SetAudioFilterInfo(audioFilterInfo, req.audio_filter());
        if (req.audio_filter().has_enable())
        {
            filterInfo.enable = req.audio_filter().enable();
        }
    }
    filterInfo.filter = audioFilterInfo;

    UINT64 filterID = controller->CreateFilter(&filterInfo);
    std::string filter_id = "";
    Util::NumToString(filterID, &filter_id);
    rsp.set_filter_id(filter_id);

    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[AudioFilter::Create] CreateFilter failed, filterID: " << filterID;
        return false;
    }
    return true;
}

bool AudioFilter::Remove::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[AudioFilter::Remove] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->DeleteFilter(filterID);

    return true;
}

bool AudioFilter::SetAudioFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[AudioFilter::SetAudioFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    if (filterInfo.type == FILTER_AUDIO)
    {
        UINT64       cmd = FILTER_CONTROL_NONE;
        AUDIO_FILTER audioFilterInfo = std::get<AUDIO_FILTER>(filterInfo.filter);
        if (req.has_audio_filter())
        {
            SetAudioFilterInfo(audioFilterInfo, req.audio_filter(), &cmd);
            if (req.audio_filter().has_enable())
            {
                filterInfo.enable = req.audio_filter().enable();
                cmd |= FILTER_CONTROL_SET_FILTER_ENABLE;
            }
        }
        filterInfo.filter = audioFilterInfo;
        controller->ControlFilter(filterID, filterInfo, static_cast<FILTER_CONTROL_CMD>(cmd));
    }

    return true;
}

bool AudioFilter::GetAudioFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[AudioFilter::GetAudioFilter] filter not exist, filterID: " << filterID;
        return false;
    }

	FILTER filterInfo{};
	controller->GetFilterInfo(filterID, &filterInfo);

    AUDIO_FILTER audioFilterInfo = std::get<AUDIO_FILTER>(filterInfo.filter);
    ls_audiofilter::AudioFilter audio_filter{};

    if (audioFilterInfo.filterType == AUDIO_FILTER_SPEEX_NOISE_SUPPRESS)
    {
        FILTER_INFO_CMD cmd = static_cast<FILTER_INFO_CMD>(FILTER_INFO_SUPPRESS_LEVEL);
        controller->GetFilterInfo(filterID, &filterInfo, cmd);
        
        ls_audiofilter::SpeexNoiseSuppressFilter speex_noise_suppress_filter{};
        speex_noise_suppress_filter.set_suppress_level(audioFilterInfo.speexNoiseSuppressFilter.suppressLevel);
        audio_filter.mutable_speex_noise_suppress_filter()->CopyFrom(speex_noise_suppress_filter);
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_NOISE_SUPPRESS)
    {
        ls_audiofilter::SamiNoiseSuppressFilter sami_noise_suppress_filter{};
        sami_noise_suppress_filter.set_config_file(audioFilterInfo.samiNoiseSuppressFilter.configFile);
        sami_noise_suppress_filter.set_speech_ratio(audioFilterInfo.samiNoiseSuppressFilter.speechRatio);
        audio_filter.mutable_sami_noise_suppress_filter()->CopyFrom(sami_noise_suppress_filter);
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_COMMON_METRICS)
    {
        ls_audiofilter::SamiCommonMetricsFilter sami_common_metrics_filter{};
        sami_common_metrics_filter.set_config_file(audioFilterInfo.samiCommonMetricsFilter.configFile);
        sami_common_metrics_filter.set_config_json(audioFilterInfo.samiCommonMetricsFilter.configJson);
        audio_filter.mutable_sami_common_metrics_filter()->CopyFrom(sami_common_metrics_filter);
    }
    else if (audioFilterInfo.filterType == AUDIO_FILTER_SAMI_MDSP_EFFECT)
    {
        ls_audiofilter::SamiMdspEffectFilter sami_mdsp_effect_filter{};
        sami_mdsp_effect_filter.set_model_file(audioFilterInfo.samiMdspEffectFilter.modelFile);
        sami_mdsp_effect_filter.set_res_file_path(audioFilterInfo.samiMdspEffectFilter.resFilePath);
        sami_mdsp_effect_filter.set_mdsp_param(audioFilterInfo.samiMdspEffectFilter.mdspParam);
        audio_filter.mutable_sami_mdsp_effect_filter()->CopyFrom(sami_mdsp_effect_filter);
    }

    if (filterInfo.enable.has_value())
    {
        audio_filter.set_enable(filterInfo.enable.value());
    }
    rsp.mutable_audio_filter()->CopyFrom(audio_filter);
    rsp.set_type((ls_audiofilter::AUDIO_FILTER_TYPE)audioFilterInfo.filterType);

    return true;
}
} // namespace MediaSDK