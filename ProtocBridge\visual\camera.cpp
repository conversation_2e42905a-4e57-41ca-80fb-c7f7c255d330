#include "stdafx.h"
#include "camera.h"
#include "visual.h"
#include "audio/audio.h"
#include "../BaseLib/event_bus.h"

namespace LS
{
Camera::RequestList Camera::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Camera::EnumInputs>());
    list.push_back(std::make_unique<Camera::EnumFormats>());

    list.push_back(std::make_unique<Camera::Create>());
    list.push_back(std::make_unique<Camera::Reopen>());

    list.push_back(std::make_unique<Camera::GetCapProcAMP>());
    list.push_back(std::make_unique<Camera::SetCapProcAMP>());

    list.push_back(std::make_unique<Camera::GetCameraControl>());
    list.push_back(std::make_unique<Camera::SetCameraControl>());

    list.push_back(std::make_unique<Camera::SetDetectColorRangeParam>());
    list.push_back(std::make_unique<Camera::StopDetectColorRange>());

    list.push_back(std::make_unique<Camera::SetCaptureMaxRate>());

    list.push_back(std::make_unique<Camera::FilterState>());
    list.push_back(std::make_unique<Camera::GetCameraInfo>());
    list.push_back(std::make_unique<Camera::IsUsingRealCamera>());
    return list;
}

bool Camera::EnumInputs::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> oDevices{};
    controller->EnumVideoDevices(&oDevices);
    for (int i = 0; i < oDevices.size(); ++i)
    {
        DSHOW device = oDevices[i];
        auto  ref = rsp.add_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
    }

    return true;
}

bool Camera::EnumFormats::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    DSHOW device{};
    device.id = req.device().id();
    device.name = req.device().name();
    std::vector<VIDEO_CAPTURE_FORMAT> oVideoFormats{};
    controller->EnumCaptureFormats(device, &oVideoFormats, nullptr);
    for (int i = 0; i < oVideoFormats.size(); ++i)
    {
        VIDEO_CAPTURE_FORMAT captureFormat = oVideoFormats[i];
        auto                 ref = rsp.add_video_cap_formats();
        ref->set_format(static_cast<ls_basicenum::VIDEO_PIXEL_FORMAT>(captureFormat.format));
        ls_base::SizeF size;
        size.set_x(captureFormat.width);
        size.set_y(captureFormat.height);
        ref->mutable_size()->CopyFrom(size);
        ref->set_rate(captureFormat.rate);
        ref->set_max_rate(captureFormat.maxRate);
        ref->set_min_rate(captureFormat.minRate);
    }

    return true;
}

void Camera::GetCameraSource(CAMERA_SOURCE& cameraSource, ls_camera::CreateParam create_param)
{
    VIDEO_CAPTURE_FORMAT        captureFormat{};
    ls_base::VideoCaptureFormat video_cap_format = create_param.video_cap_format();
    captureFormat.format = static_cast<VIDEO_PIXEL_FORMAT>(video_cap_format.format());
    captureFormat.width = video_cap_format.size().x();
    captureFormat.height = video_cap_format.size().y();
    captureFormat.rate = video_cap_format.rate();
    captureFormat.maxRate = video_cap_format.max_rate();
    captureFormat.minRate = video_cap_format.min_rate();

    DSHOW dshow{};
    dshow.id = create_param.video_device().id();
    dshow.name = create_param.video_device().name();

    cameraSource.captureFormat = captureFormat;
    cameraSource.dshow = dshow;
    if (create_param.has_time_out())
    {
        cameraSource.timeOut = create_param.time_out();
    }
    if (create_param.has_version())
    {
        cameraSource.version = create_param.version();
    }
    if (create_param.has_file_path())
    {
        cameraSource.placeholder = create_param.file_path();
    }
    if (create_param.has_limit_fps())
    {
        cameraSource.maxRate = create_param.limit_fps();
    }

    if (create_param.has_color_config())
    {
        cameraSource.colorConfig.colorSpace = static_cast<COLOR_SPACE>(create_param.color_config().color_space());
        cameraSource.colorConfig.colorTransfer = static_cast<COLOR_TRANSFER>(create_param.color_config().color_transfer());
        cameraSource.colorConfig.colorRange = static_cast<COLOR_RANGE>(create_param.color_config().color_range());
    }
    
    if (create_param.has_audio_track())
    {
        cameraSource.audioTrack = create_param.audio_track();
    }

    if (create_param.has_audio_device())
    {
        DSHOW audio_device{};
        audio_device.id = create_param.audio_device().id();
        audio_device.name = create_param.audio_device().name();
        cameraSource.audioDevice = audio_device;
    }

    if (create_param.has_audio_cap_format())
    {
        ls_base::AudioCaptureFormat audio_cap_format = create_param.audio_cap_format();
        AUDIO_CAPTURE_FORMAT        audioCapFormat{};
        audioCapFormat.renderType = (ANALOG_RENDER_TYPE)audio_cap_format.render_type();
        audioCapFormat.sampleRate = audio_cap_format.sample_rate();
        audioCapFormat.channels = audio_cap_format.channels();
        audioCapFormat.bitsPerSample = audio_cap_format.bits_per_sample();
        cameraSource.audioCapFormat = audioCapFormat;
    }

    if (create_param.has_audio_setting())
    {
        AUDIO_SETTING&                     audioSetting = cameraSource.audioSetting;
        const ls_audio::AudioSettingParam& audio_setting_param = create_param.audio_setting();
        Audio::SetVISAudioInfo(audioSetting, audio_setting_param);
    }
}

bool Camera::Create::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    LAYER_INFO iLayerInfo{};
    SOURCE_INFO iSourceInfo{};
    Visual::SetVisualUniformInfo(iLayerInfo, iSourceInfo, req.visual_uni_attr());

    CAMERA_SOURCE iCameraSource{};
    Camera::GetCameraSource(iCameraSource, req.create_param());

    iSourceInfo.type = VISUAL_TYPE::VISUAL_CAMERA;
    iSourceInfo.source = iCameraSource;

    if (!controller->CreateSource(iSourceInfo))
    {
        LOG(ERROR) << "[Camera::Create] CreateSource failed, sourceID: " << iSourceInfo.id;
        return false;
    }

	std::string source_id = "";
	Util::NumToString(iSourceInfo.id, &source_id);
	rsp.set_source_id(source_id);

    iLayerInfo.sourceID = iSourceInfo.id;
    controller->CreateLayer(&iLayerInfo);

    iSourceInfo.layerIDs.push_back(iLayerInfo.id);
    controller->SetSourceInfo(iSourceInfo.id, &iSourceInfo);

    std::string visual_id = "";
    Util::NumToString(iLayerInfo.id, &visual_id);
    rsp.set_visual_id(visual_id);

    if (controller->FindLayerByID(iLayerInfo.id))
    {
        SOURCE_INFO oSourceInfo{};
        controller->GetSourceInfo(iSourceInfo.id, &oSourceInfo);
        rsp.set_reason(static_cast<ls_basicenum::CREATE_SOURCE_FAILED_REASON>(oSourceInfo.result.reason));
        rsp.set_error_code(iSourceInfo.result.errorCode);
    }
    else
    {
        LOG(ERROR) << "[Camera::Create] AddLayer failed, visualID: " << iLayerInfo.id;
        return false;
    }

    return true;
}

bool Camera::Reopen::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::Reopen] visual not exist, visualID: " << visualID;
        return false;
    }

	LAYER_INFO iLayerInfo{};
	controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    auto& compositeMetas = controller->GetCompositeMetas();
    auto& iter = compositeMetas.find(iSourceInfo.id);
    if (iter == compositeMetas.end())
    {
        LOG(ERROR) << "[Camera::Reopen] camera source_id not exist in compositeMetas, source_id: " << iSourceInfo.id;
        return false;
    }

    CAMERA_SOURCE iCameraSource{};
    if (iSourceInfo.type == VISUAL_CAMERA)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iSourceInfo.source);
    }
    else if (iSourceInfo.type == VISUAL_IMAGE)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iter->second.source);
    }
    Camera::GetCameraSource(iCameraSource, req.create_param());

    iter->second.source = iCameraSource;
    controller->SetCompositeMetas(compositeMetas);
    iSourceInfo.source = iCameraSource;
    
    bool isMatchedSource = false;
	if (controller->IsMatchedSource(iSourceInfo))
	{
        isMatchedSource = true;
		controller->GetSourceInfo(iSourceInfo.id, &iSourceInfo);
        controller->UpdateLayerSource(visualID, iSourceInfo.id);
        controller->DestroySource(iLayerInfo.sourceID);
		LOG(INFO) << "[Camera::Reopen] reopen camera is matched origin camera source, sourceID: " << iSourceInfo.id;
	}
    else
    {
        return controller->ReopenSource(iSourceInfo.id, iSourceInfo);
    }

    if (isMatchedSource)
    {
        controller->GetSourceInfo(iSourceInfo.id, &iSourceInfo);
        for (const auto& layerID : iSourceInfo.layerIDs)
        {
            controller->HandleLayerSizeChange(layerID);
        }
    }

    return true;
}

bool Camera::GetCapProcAMP::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::GetCapProcAMP] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo);

    auto& compositeMetas = controller->GetCompositeMetas();
    auto& iter = compositeMetas.find(oSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    CAMERA_SOURCE oCameraSource{};
    if (oSourceInfo.type == VISUAL_CAMERA)
    {
        oCameraSource = std::get<CAMERA_SOURCE>(oSourceInfo.source);

        oCameraSource.hwnd = (HWND)req.hwnd();
        oSourceInfo.source = oCameraSource;
        controller->SetSourceInfo(oSourceInfo.id, &oSourceInfo);
        iter->second.source = oCameraSource;
        controller->SetCompositeMetas(compositeMetas);

        SOURCE_INFO_CMD cmd = SOURCE_INFO_CAP_PROC_AMP;
        controller->GetSourceInfo(oSourceInfo.id, &oSourceInfo, cmd);
        oCameraSource = std::get<CAMERA_SOURCE>(oSourceInfo.source);
    }
    else if (oSourceInfo.type == VISUAL_IMAGE)
    {
        oCameraSource = std::get<CAMERA_SOURCE>(iter->second.source);
    }

    std::vector<VIDEO_PROC_AMP> videoProcAMPs = oCameraSource.procAmps;
    for (const auto& procAmp : videoProcAMPs)
    {
        auto ref = rsp.add_proc_amp();
        ref->set_type(static_cast<ls_camera::VIDEO_PROCAMP_TYPE>(procAmp.type));
        ls_camera::CaptureAttr attr;
        attr.set_min(procAmp.attr.min);
        attr.set_max(procAmp.attr.max);
        attr.set_step(procAmp.attr.step);
        attr.set_default_val(procAmp.attr.defVal);
        attr.set_flag(procAmp.attr.flag);
        attr.set_value(procAmp.attr.value);
        attr.set_current_flag(procAmp.attr.currentFlag);
        ref->mutable_attr()->CopyFrom(attr);
    }

    return true;
}

bool Camera::SetCapProcAMP::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::SetCapProcAMP] visual not exist, visualID: " << visualID;
        return false;
    }

    CAPTURE_ATTR            captureAttr{};
    ls_camera::VideoProcAMP proc_amp = req.proc_amp();
    ls_camera::CaptureAttr  attr = proc_amp.attr();
    captureAttr.value = attr.value();
    captureAttr.flag = attr.flag();

    VIDEO_PROC_AMP procAmp{};
    procAmp.type = static_cast<VIDEO_PROCAMP_TYPE>(proc_amp.type());
    procAmp.attr = captureAttr;

	LAYER_INFO iLayerInfo{};
	controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    auto& compositeMetas = controller->GetCompositeMetas();
    auto&  iter = compositeMetas.find(iSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    CAMERA_SOURCE iCameraSource{};
    bool isCompositeFallback = false;
    if (iSourceInfo.type == VISUAL_CAMERA)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iSourceInfo.source);
        iCameraSource.procAmps.clear();
        iCameraSource.procAmps.push_back(procAmp);
        iCameraSource.hwnd = (HWND)req.hwnd();
    }
    else if (iSourceInfo.type == VISUAL_IMAGE)
    {
        isCompositeFallback = true;
    }

    iter->second.source = iCameraSource;
    controller->SetCompositeMetas(compositeMetas);

    if (isCompositeFallback)
        return true;

    iSourceInfo.source = iCameraSource;
    SOURCE_CONTROL_CMD cmd = SOURCE_CONTROL_SET_VIDEO_PROCAMP;
    controller->ControlSource(iSourceInfo.id, iSourceInfo, cmd);
    return true;
}

bool Camera::GetCameraControl::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::GetCameraControl] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo);

    auto& compositeMetas = controller->GetCompositeMetas();
    auto  iter = compositeMetas.find(oSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    CAMERA_SOURCE oCameraSource{};
    if (oSourceInfo.type == VISUAL_CAMERA)
    {
        oCameraSource = std::get<CAMERA_SOURCE>(oSourceInfo.source);

        oCameraSource.hwnd = (HWND)req.hwnd();
        oSourceInfo.source = oCameraSource;
        controller->SetSourceInfo(oSourceInfo.id, &oSourceInfo);
        iter->second.source = oCameraSource;
        controller->SetCompositeMetas(compositeMetas);

        SOURCE_INFO_CMD cmd = SOURCE_INFO_CAMERA_CONTROL;
        controller->GetSourceInfo(oSourceInfo.id, &oSourceInfo, cmd);
        oCameraSource = std::get<CAMERA_SOURCE>(oSourceInfo.source);
    }
    else if (oSourceInfo.type == VISUAL_IMAGE)
    {
        oCameraSource = std::get<CAMERA_SOURCE>(iter->second.source);
    }

    std::vector<CAMERA_CONTROL> cameraControls = oCameraSource.controls;
    for (const auto& control : cameraControls)
    {
        auto ref = rsp.add_control();
        ref->set_type(static_cast<ls_camera::CAMERA_CONTROL_TYPE>(control.type));
        ls_camera::CaptureAttr attr;
        attr.set_min(control.attr.min);
        attr.set_max(control.attr.max);
        attr.set_step(control.attr.step);
        attr.set_default_val(control.attr.defVal);
        attr.set_flag(control.attr.flag);
        attr.set_value(control.attr.value);
        attr.set_current_flag(control.attr.currentFlag);
        ref->mutable_attr()->CopyFrom(attr);
    }

    return true;
}

bool Camera::SetCameraControl::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::SetCameraControl] visual not exist, visualID: " << visualID;
        return false;
    }

    CAPTURE_ATTR             captureAttr{};
    ls_camera::CameraControl control = req.control();
    ls_camera::CaptureAttr   attr = control.attr();
    captureAttr.min = attr.min();
    captureAttr.max = attr.max();
    captureAttr.step = attr.step();
    captureAttr.defVal = attr.default_val();
    captureAttr.flag = attr.flag();
    captureAttr.value = attr.value();
    captureAttr.currentFlag = attr.current_flag();

    CAMERA_CONTROL cameraControl{};
    cameraControl.type = static_cast<CAMERA_CONTROL_TYPE>(control.type());
    cameraControl.attr = captureAttr;

	LAYER_INFO iLayerInfo{};
	controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    CAMERA_SOURCE iCameraSource{};
    bool          isCompositeFallback = false;

    auto& compositeMetas = controller->GetCompositeMetas();
    auto& iter = compositeMetas.find(iSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    if (iSourceInfo.type == VISUAL_CAMERA)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iSourceInfo.source);
        iCameraSource.controls.clear();
        iCameraSource.controls.push_back(cameraControl);
        iCameraSource.hwnd = (HWND)req.hwnd();
    }
    else if (iSourceInfo.type == VISUAL_IMAGE)
    {
        isCompositeFallback = true;
    }
    iter->second.source = iCameraSource;
    controller->SetCompositeMetas(compositeMetas);

    if (isCompositeFallback)
        return true;

    iSourceInfo.source = iCameraSource;
    SOURCE_CONTROL_CMD cmd = SOURCE_CONTROL_SET_CAMERA_CONTROL;
    controller->ControlSource(iSourceInfo.id, iSourceInfo, cmd);
    return true;
}

bool Camera::SetDetectColorRangeParam::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::SetVideoRange] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    CAMERA_SOURCE iCameraSource{};
    bool          isCompositeFallback = false;

    auto& compositeMetas = controller->GetCompositeMetas();
    auto& iter = compositeMetas.find(iSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    if (iSourceInfo.type == VISUAL_CAMERA)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iSourceInfo.source);
        if (req.has_detect_param())
        {
            ls_camera::DetectColorRange detect_param = req.detect_param();
            if (detect_param.has_detect_interval())
            {
                iCameraSource.detectColorRange.interval = detect_param.detect_interval();
            }
            if (detect_param.has_limited_detect_count())
            {
                iCameraSource.detectColorRange.limitedCnt = detect_param.limited_detect_count();
            }
        }

        iter->second.source = iCameraSource;
        controller->SetCompositeMetas(compositeMetas);
    }
    else if (iSourceInfo.type == VISUAL_IMAGE)
    {
        isCompositeFallback = true;
    }

    if (isCompositeFallback)
        return true;

    iSourceInfo.source = iCameraSource;
    SOURCE_CONTROL_CMD cmd = SOURCE_CONTROL_SET_DETECT_COLOR_RANGE;
    controller->ControlSource(iSourceInfo.id, iSourceInfo, cmd);
    return true;
}

bool Camera::StopDetectColorRange::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::SetVideoRange] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(visualID, &iLayerInfo);
    
    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    SOURCE_CONTROL_CMD cmd = SOURCE_CONTROL_STOP_DETECT_COLOR_RANGE;
    controller->ControlSource(iSourceInfo.id, iSourceInfo, cmd);
    return true;
}

bool Camera::SetCaptureMaxRate::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 visualID = 0;
	Util::StringToNum(req.visual_id(), &visualID);
	if (!controller->FindLayerByID(visualID))
	{
		LOG(ERROR) << "[Camera::SetVideoRange] visual not exist, visualID: " << visualID;
		return false;
	}

	LAYER_INFO iLayerInfo{};
	controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.id, &iSourceInfo);

    CAMERA_SOURCE iCameraSource{};
    bool          isCompositeFallback = false;

    auto& compositeMetas = controller->GetCompositeMetas();
    auto& iter = compositeMetas.find(iSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    if (iSourceInfo.type == VISUAL_CAMERA)
    {
        iCameraSource = std::get<CAMERA_SOURCE>(iSourceInfo.source);
        iCameraSource.maxRate = req.max_rate();
    }
    else if (iSourceInfo.type == VISUAL_IMAGE)
    {
        isCompositeFallback = true;
    }

    iter->second.source = iCameraSource;
    controller->SetCompositeMetas(compositeMetas);

    if (isCompositeFallback)
        return true;

    iSourceInfo.source = iCameraSource;
    SOURCE_CONTROL_CMD cmd = SOURCE_CONTROL_SET_CAPTURE_MAX_RATE;
    controller->ControlSource(iSourceInfo.id, iSourceInfo, cmd);
	return true;
}

bool Camera::FilterState::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::FilterState] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo);

    CAMERA_FILTER_STATE filterState = CAMERA_FILTER_STATE::CAMERA_FILTER_STATE_STOPPED;
    if (oSourceInfo.type == VISUAL_CAMERA)
    {
        SOURCE_INFO_CMD cmd = SOURCE_INFO_FILTER_STATE;
        controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo, cmd);
        filterState = std::get<CAMERA_SOURCE>(oSourceInfo.source).filterState;
    }

    rsp.set_filter_state(static_cast<ls_camera::CAMERA_FILTER_STATE>(filterState));
    return true;
}

bool Camera::GetCameraInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::GetCameraInfo] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo);

    auto& compositeMetas = controller->GetCompositeMetas();
    auto  iter = compositeMetas.find(oSourceInfo.id);
    if (iter == compositeMetas.end())
        return true;

    CAMERA_SOURCE oCameraSource{};
    if (oSourceInfo.type == VISUAL_CAMERA)
    {
        oCameraSource = std::get<CAMERA_SOURCE>(oSourceInfo.source);
    }
    else if (oSourceInfo.type == VISUAL_IMAGE)
    {
        const UINT64 primarySourceID = iter->second.primarySourceID;
        if (primarySourceID == 0)
            return false;

        oCameraSource = std::get<CAMERA_SOURCE>(iter->second.source);
    }

    ls_camera::CreateParam camera_info{};
    ls_base::DShow dshow{};
    dshow.set_id(oCameraSource.dshow.id);
    dshow.set_name(oCameraSource.dshow.name);
    camera_info.mutable_video_device()->CopyFrom(dshow);

    ls_base::VideoCaptureFormat video_cap_foramt{};
    ls_base::SizeF size{};
    size.set_x(oCameraSource.captureFormat.width);
    size.set_y(oCameraSource.captureFormat.height);
    video_cap_foramt.mutable_size()->CopyFrom(size);

    video_cap_foramt.set_format(static_cast<ls_basicenum::VIDEO_PIXEL_FORMAT>(oCameraSource.captureFormat.format));
    video_cap_foramt.set_rate(oCameraSource.captureFormat.rate);
    video_cap_foramt.set_min_rate(oCameraSource.captureFormat.minRate);
    video_cap_foramt.set_max_rate(oCameraSource.captureFormat.maxRate);
    camera_info.mutable_video_cap_format()->CopyFrom(video_cap_foramt);

    ls_base::ColorConfig color_config{};
    color_config.set_color_space(static_cast<ls_basicenum::COLOR_SPACE>(oCameraSource.colorConfig.colorSpace));
    color_config.set_color_transfer(static_cast<ls_basicenum::COLOR_TRANSFER>(oCameraSource.colorConfig.colorTransfer));
    color_config.set_color_range(static_cast<ls_basicenum::VIDEO_RANGE>(oCameraSource.colorConfig.colorRange));
    camera_info.mutable_color_config()->CopyFrom(color_config);

    /*
    ls_base::AudioCaptureFormat audio_cap_format{};
    audio_cap_format.set_render_type((ls_basicenum::ANALOG_RENDER_TYPE)oCameraSource.audioCapFormat.renderType);
    audio_cap_format.set_channels(cameraSource.audioCapFormat.channels);
    audio_cap_format.set_sample_rate(oCameraSource.audioCapFormat.sampleRate);
    audio_cap_format.set_bits_per_sample(oCameraSource.audioCapFormat.bitsPerSample);
    camera_info.mutable_audio_cap_format()->CopyFrom(audio_cap_format);

    ls_base::DShow audio_device{};
    audio_device.set_id(oCameraSource.audioDevice.id);
    audio_device.set_name(oCameraSource.audioDevice.name);
    camera_info.mutable_audio_device()->CopyFrom(audio_device);

    camera_info.set_audio_track(oCameraSource.audioTrack);
    ls_audio::AudioSettingParam setting_param{};
    setting_param.set_balanceing(oCameraSource.audioSetting.balanceing);
    setting_param.set_down_mix_mono(oCameraSource.audioSetting.downMixMono);
    setting_param.set_interval(oCameraSource.audioSetting.interval);
    setting_param.set_monitor_type((ls_audio::AUDIO_MONITOR_TYPE)oCameraSource.audioSetting.monitorType);
    setting_param.set_mute(oCameraSource.audioSetting.mute);
    setting_param.set_sync_offset(oCameraSource.audioSetting.syncOffset);
    setting_param.set_volume(oCameraSource.audioSetting.volume);
    camera_info.mutable_audio_setting()->CopyFrom(setting_param);
    */

    rsp.mutable_source_info()->CopyFrom(camera_info);
    return true;
}

bool Camera::IsUsingRealCamera::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Camera::IsUsingRealCamera] visual not exist, visualID: " << visualID;
        return false;
    }

    bool isUsingRealCamera = false;
    controller->CameraSourceIsUsingRealCamera(visualID, &isUsingRealCamera);

    rsp.set_using_real_camera(isUsingRealCamera);
    return true;
}
} // namespace MediaSDK