#include "FilterMgr.h"
#include "ModeSceneMgr.h"
#include "AudioMgr.h"

#define MAKETYPEID(type) (((type)&0x7f)<<56)
#define MAKEFILTERID(filter) ((filter)&0xffffffffffffff)

FilterMgr g_filterMgr;

FilterMgr* FilterMgr::GetInstance()
{
	return &g_filterMgr;
}

FilterMgr::FilterMgr(){}

FilterMgr::~FilterMgr(){}

UINT64 FilterMgr::AllocFilterID(UINT64 type)
{
    while (1)
    {
        USHORT filterCounter = ::InterlockedIncrement16((SHORT*)&m_filterCounter);
        if (filterCounter == 0)
            filterCounter = ::InterlockedIncrement16((SHORT*)&m_filterCounter);

        UINT64 filterID = MAKETYPEID(type) | MAKEFILTERID((UINT64)filterCounter);
        std::map<UINT64, void*>::iterator it = m_bindObject.find(filterID);
        if (it == m_bindObject.end())
            return filterID;
    }
    return -1;
}

void FilterMgr::BindObject(UINT64 id, void* object)
{
    m_bindObject.insert(std::pair<UINT64, void*>(id, object));
}

void FilterMgr::UnBindObject(UINT64 id)
{
    m_bindObject.erase(id);
}

Filter* FilterMgr::GetFilterByID(UINT64 id)
{
    std::map<UINT64, void*>::iterator it = m_bindObject.find(id);
    if (it != m_bindObject.end())
        return (Filter*)it->second;

    return NULL;
}

UINT64 FilterMgr::AddFilter(const FILTER* info, const UINT64* id /*= NULL*/)
{
    Filter* pFilter = new Filter();
    FILTER  filterInfo = *info;
    filterInfo.id = id != NULL ? *id : AllocFilterID(info->type);
    pFilter->SetFilterInfo(&filterInfo);

    m_filters.push_back(pFilter);
    BindObject(filterInfo.id, pFilter);

    return filterInfo.id;
}

void FilterMgr::DeleteFilter(Filter* filter)
{
    auto it = std::find(m_filters.begin(), m_filters.end(), filter);
    if (it != m_filters.end())
    {
        m_filters.erase(it);

        FILTER filterInfo = {};
        filter->GetFilterInfo(&filterInfo);
        UnBindObject(filterInfo.id);

        for (const auto& mediaID : filterInfo.mediaIDs)
        {
            if (filterInfo.type == FILTER_VISUAL || filterInfo.type == FILTER_EFFECT)
            {
                Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
                if (pLayer)
                    pLayer->RemoveFilter(mediaID, &filterInfo);
            }
            else if (filterInfo.type == FILTER_AUDIO)
            {
                Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
                if (pAudio)
                    pAudio->RemoveFilter(mediaID, &filterInfo);
            }
            else if (filterInfo.type == FILTER_CANVAS)
            {
                Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
                if (pCanvas)
                    pCanvas->RemoveFilter(mediaID, &filterInfo);
            }
        }

        delete filter;
    }
}

void FilterMgr::SetFilterInfo(FILTER* info)
{
    Filter* pFilter = GetFilterByID(info->id);
    if (pFilter)
        pFilter->SetFilterInfo(info);
}
