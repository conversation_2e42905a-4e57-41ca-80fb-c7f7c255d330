﻿#include <fstream>
#include <string>
#include <deque>
#include <filesystem>
#include "MediaMgr.h"
#include "stringutil.h"
#include "ciefhelper.h"
#include "EnumConverter.h"
#include "LogDumpMgr.h"
#include "MonitorMgr.h"
#include "NvidiaDriverHangRecover.h"
#include "SourceMgr.h"
#include "ModeSceneMgr.h"
#include "MediaMgr.h"
#include "PhoneCameraMgr.h"
extern media_mgr::MediaMgr* g_mediaMgr;

extern "C"
{
    __declspec(dllexport) DWORD NvOptimusEnablement = 1;
    __declspec(dllexport) int AmdPowerXpressRequestHighPerformance = 1;
}

const char* DshowEnumBlackList[] = {"LSVCam", "WebcastMate VirtualCamera"};
const char* DshowCreateBlackList[] = {"WebcastMate VirtualCamera"};

namespace media_mgr
{

    MediaMgr::MediaMgr() {}
    MediaMgr::~MediaMgr() {}

    void MediaMgr::LogMessageHandler(int32_t severity, const char* szFile, int32_t line, const char* szText)
    {
        ParfaitLogHandler(severity, szFile, line, szText);
    }

    bool MediaMgr::Initialize(INITIALIZE_INFO info)
    {
        task_mgr_.Init();
        full_screen_detector_.Init(std::bind(&MediaMgr::OnFullScreenDetectorCallback, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

        std::string sdk_log_file = "";
        try
        {
            std::wstring          serverLogFile = LogDumpMgr::GetInstance()->m_serverLogFile;
            std::filesystem::path p(serverLogFile);
            std::wstring          ext;
            if (p.has_extension())
                ext = p.extension();
            std::filesystem::path p2(ext);

            p.replace_extension(L"");
            p.replace_filename(p.filename().wstring() + (L"_sdkv2"));
            sdk_log_file = p.u8string() + p2.u8string();
        }
        catch (...)
        {
        }

        {
            char verinfo[32];
            sprintf_s(verinfo, "%u", 2);
            LogDumpMgr::GetInstance()->SetParfaitContextInfo("sdk_impl_ver", verinfo, true);
        }

        PhoneCameraMgr::getInstance().SetUidDid(info.uid, info.did);
        bool success = false;
        do
        {
            sdk_controller_ = sdk_helper::MediaSDKControllerV2ImplInit();
            success = !!sdk_controller_;
            if (!success)
                break;
            init_sdk_ = true;

            sdk_controller_->SetHost(info.host);
            sdk_controller_->SetLogMessageHandler(std::bind(&MediaMgr::LogMessageHandler, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4), info.asyncLog);
            sdk_controller_->SetThreadMonitorEventHandler(std::bind(&MediaMgr::OnThreadMonitorEvent, this, std::placeholders::_1, std::placeholders::_2));

            init_param_ = info;
            init_param_.workDir = "D:\\MediaSDK_V2";
            init_param_.resDir = "D:\\MediaSDK_V2";
            init_param_.sdkLogFile = sdk_log_file;
            app_work_dir_ = info.workDir;

            SourceMgr::GetInstance()->Init();
            g_cief->GetThreadMgr()->AddTaskToThreadPool(new LS::MemberTask<MediaMgr>(this, &MediaMgr::StartTimer, 0));

            bool ok = sdk_controller_->Init(init_param_);
            if (ok)
            {
                init_api_ = true;
                ::SetWindowLongPtr((HWND)init_param_.topWindow, GWLP_HWNDPARENT, (LONG_PTR)init_param_.bottomWnd);

                {
                    InitEvent event{};
                    event.success = true;
                    event.dxError = 0;
                    event.pid = static_cast<UINT32>(::GetCurrentProcessId());
                    eventbus::EventBus::PostEvent(event);
                }
            }
            else
            {
                LOG(ERROR) << "[MediaMgr::Initialize] async sdk_controller_->Init failed";
            }


            sdk_controller_->GetVideoFrameProcessor()->SetGrabFrameResultCallback(
                [this](const std::string& frame_id, int32_t width, int32_t height, int32_t errcode, std::string& errmsg) {
                    
                    CreateFrameEvent evt{};
                    evt.frameID = frame_id;
                    evt.frameSize.Width = width;
                    evt.frameSize.Height = height;
                    evt.errCode = errcode;
                    evt.errMsg = errmsg;

                    auto task = task_mgr_.CreateThreadTask([evt]() {
                        eventbus::EventBus::PostEvent(evt);
                    });

                    g_cief->GetThreadMgr()->AddTaskToBackThread(task);
                });

            init_finish_ = true;
        } while (0);

        auto gpu_info_list = MonitorMgr::GetInstance()->GetGPUInfo();
        if (gpu_info_list.size() > 0)
        {

            auto mainGPU = gpu_info_list[0];
            if (mainGPU.vendorId == 0x10DE)
            {
                // NVIDIA Video Card
                enable_nvidia_driver_hang_recover_ = true;
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Enable(true);
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Init();
            }
        }
        else
        {
            LOG(ERROR) << "[MediaMgr::Initialize] Get default GPU Name error!";
        }

        return success;
    }

    void MediaMgr::Uninitialize()
    {
        full_screen_detector_.Uninit();
        StopTimer();
        task_mgr_.Uninit();

        if (sdk_controller_)
        {
            sdk_controller_->GetVideoFrameProcessor()->SetGrabFrameResultCallback(NULL);
            sdk_controller_->SetLogMessageHandler(NULL);
            sdk_controller_->Uninit();
        }

        sdk_helper::MediaSDKControllerV2ImplUninit();
        sdk_controller_ = NULL;
        init_sdk_ = false;

        if (enable_nvidia_driver_hang_recover_)
        {
            enable_nvidia_driver_hang_recover_ = false;
            nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Uninit();
        }
    }

    bool MediaMgr::SetDisplay(uint32_t video_model, bool show)
    {
        LOG(INFO) << "[MediaMgr::SetDisplay] model: " << video_model << ", show_view: " << show;
        if (!init_sdk_ || !init_api_ || !sdk_controller_)
        {
            LOG(ERROR) << "[MediaMgr::ShowModel] init_sdk_ or init_api_ or sdk_ is nullptr";
            cache_video_model_display_map_[video_model] = show;
            return false;
        }

        auto video_model_ctx = GetModelCtx(video_model);
        if (!video_model_ctx)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] video_model_ctx is nullptr";
            cache_video_model_display_map_[video_model] = show;
            return false;
        }

        bool success = sdk_controller_->PreviewWindowEnableShow(video_model, show);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] PreviewWindowEnableShow failed";
            return false;
        }

        bool enable_canvas_preview = all_canvas_enable_preview_ && show;
        success = sdk_controller_->PreviewWindowEnableDraw(video_model, enable_canvas_preview);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] PreviewWindowEnableDraw failed";
            return false;
        }

        video_model_ctx->show_canvas = show;
        return success;
    }

    bool MediaMgr::GetDisplay(uint32_t model)
    {
        auto canvas_ctx = GetModelCtx(model);
        if (!canvas_ctx)
        {
            LOG(ERROR) << "[MediaMgr::GetDisplay] canvas_ctx is nullptr";
            return false;
        }

        LOG(INFO) << "[MediaMgr::GetDisplay] canvas_id: " << model;
        return canvas_ctx->show_canvas && all_canvas_enable_preview_;
    }

    bool MediaMgr::CreateFullScreenDetector(const std::string& detector_id)
    {
        LOG(INFO) << "[MediaMgr::CreateFullScreenDetector] detector_id: " << detector_id;
        bool success = full_screen_detector_.CreateDetector(detector_id);
        if (!success)
            LOG(ERROR) << "[MediaMgr::CreateFullScreenDetector] CreateDetector failed";

        return success;
    }

    bool MediaMgr::DestroyFullScreenDetector(const std::string& detector_id)
    {
        LOG(INFO) << "[MediaMgr::DestroyFullScreenDetector] detector_id: " << detector_id;
        full_screen_detector_.DestroyDetector(detector_id);
        return true;
    }

    bool MediaMgr::SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exe_names)
    {
        full_screen_detector_.SetIgnoreProcessList(exe_names);
        return true;
    }

    bool MediaMgr::EnableAllPreview(bool enable)
    {
        std::mutex model_ctx_map_mutex_;
        all_canvas_enable_preview_ = enable;
        for (auto it : model_ctx_map_)
        {
            auto video_model_ctx = it.second;
            bool enable_canvas_preview = enable && video_model_ctx->show_canvas;
            bool success = sdk_controller_->PreviewWindowEnableDraw(video_model_ctx->video_model, enable_canvas_preview);
            if (!success)
                LOG(ERROR) << "[MediaMgr::EnablePreview] PreviewWindowEnableDraw failed";

            LOG(INFO) << "[MediaMgr::EnablePreview] video_model: " << video_model_ctx->video_model << ", enable: " << enable << ", show_canvas: " << video_model_ctx->show_canvas << ", enable_canvas_preview: " << enable_canvas_preview;
        }

        return true;
    }

    bool MediaMgr::EnablePreviewByVideoModel(UINT32 video_model, bool enable)
    {
        std::mutex model_ctx_map_mutex_;
        for (auto it : model_ctx_map_)
        {
            auto video_model_ctx = it.second;
            if (video_model_ctx->video_model == video_model)
            {
                bool enable_canvas_preview = enable && video_model_ctx->show_canvas;
                bool success = sdk_controller_->PreviewWindowEnableShow(video_model_ctx->video_model, enable_canvas_preview);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::EnablePreview] PreviewWindowEnableDraw failed, videoModel: " << video_model;
                break;
            }
        }

        return true;
    }

    bool MediaMgr::StartColorPicker(HWND hwnd)
    {
        color_picker_watch_.StartColorPickerWatch(StringToWString(app_work_dir_, CP_UTF8), hwnd);
        return true;
    }

    bool MediaMgr::InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect)
    {
        bool success = sdk_controller_->EffectPlatformInit(initEffect);
        if (!success)
            LOG(ERROR) << "[MediaMgr::TryInitEffectPlatform] EffectPlatformInit failed";
        return success;
    }

    bool MediaMgr::UpdateEffectConfig(const std::string& user_id, const std::string& ttls_hardware_level)
    {
        bool success = sdk_controller_->EffectPlatformUpdateConfig(user_id, ttls_hardware_level);
        if (!success)
            LOG(ERROR) << "[MediaMgr::UpdateConfig] EffectPlatformUpdateConfig failed";
        return success;
    }

    bool MediaMgr::AddAudio(const AUDIO_INFO& info)
    {
        std::string audio_id = "";
        bool success = Util::NumToString(info.id, &audio_id);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::AddAudio] Util::NumToString failed, audio_id in: " << info.id << ", out: " << audio_id;
            return false;
        }

        if (info.type == AUDIO_WAS)
        {
            WAS_AUDIO   was = std::get<WAS_AUDIO>(info.audio);
            if (was.isLyrax)
            {
                success = sdk_controller_->LyraxEngineCreateAudioSource(info);
            }
            else
            {
                success = sdk_controller_->CreateWASAPIAudioSource(info);
            }

            if (success)
            {
                AUDIO_INFO& new_audio = const_cast<AUDIO_INFO&>(info);
                new_audio.audio = was;
            }
            else
            {
                LOG(ERROR) << "[MediaMgr::AddAudio] CreateWASAPIAudioSource failed";
            }
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio device: " << info.device.toString() << ", was audio info: " << was.toString();
        }
        else if (info.type == AUDIO_APP)
        {
            success = sdk_controller_->CreateAppAudioSource(info);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddAudio] CreateAppAudioSource failed";
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio device: " << info.device.toString() << ", app audio info: " << std::get<APP_AUDIO>(info.audio).excludePID;
        }
        else if (info.type == AUDIO_PCM)
        {
            success = sdk_controller_->CreatePCMAudioSource(info);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddAudio] CreatePCMAudioSource failed";
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio_capture: " << info.audioCapture.toString();
        }

        return success;
    }

    bool MediaMgr::RemoveAudio(const uint64_t audio_id)
    {
        LOG(INFO) << "[MediaMgr::RemoveAudio] audio_id: " << audio_id;
        bool success = false;
        do
        {
            std::string audio_id_str = "";
            Util::NumToString(audio_id, &audio_id_str);

            success = sdk_controller_->AudioSourceDestroy(audio_id_str);
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveAudio] AudioSourceDestroy failed";
        } while (0);

        return success;
    }

    bool MediaMgr::ControlAudio(const AUDIO_CONTROL_INFO& info, bool isEraseAudio /* = false */)
    {
		std::string audio_id = "";
        UINT32      audioTrack = info.audioInfo.audioTrack;
		Util::NumToString(info.audioInfo.id, &audio_id);
        AUDIO_SETTING audioSetting = info.audioInfo.audioSetting;

        if (isEraseAudio)
        {
            Util::NumToString(info.eraseAudioInfo.id, &audio_id);
            audioSetting = info.eraseAudioInfo.audioSetting;
            audioTrack = info.eraseAudioInfo.audioTrack;
        }

        bool success = false;
		if (info.cmd & AUDIO_CONTROL_SET_ALL_SETTING)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetProperty audio_id: " << audio_id << ", audioSetting: " << audioSetting.toString();
			success = sdk_controller_->AudioSourceSetProperty(audio_id, audioSetting);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetProperty failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_VOLUME)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetVolumn audio_id: " << audio_id << ", volume: " << audioSetting.volume;
			success = sdk_controller_->AudioSourceSetVolumn(audio_id, audioSetting.volume);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetVolumn failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_BALANCEING)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetBalance audio_id: " << audio_id << ", balanceing: " << audioSetting.balanceing;
			success = sdk_controller_->AudioSourceSetBalance(audio_id, audioSetting.balanceing);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetBalance failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_SYNC_OFFSET)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetSyncOffset audio_id: " << audio_id << ", sync_offset: " << audioSetting.syncOffset;
			success = sdk_controller_->AudioSourceSetSyncOffset(audio_id, audioSetting.syncOffset);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetSyncOffset failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_INTERVAL)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetInterval audio_id: " << audio_id << ", interval: " << audioSetting.interval;
			success = sdk_controller_->AudioSourceSetInterval(audio_id, audioSetting.interval);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetInterval failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_MONITOR_TYPE)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetMonitorType audio_id: " << audio_id << ", monitor_type: " << audioSetting.monitorType;
			success = sdk_controller_->AudioSourceSetMonitorType(audio_id, audioSetting.monitorType);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetMonitorType failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_DOWN_MIX_MONO)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceMixToMono audio_id: " << audio_id << ", down_mix_mono: " << audioSetting.downMixMono;
			success = sdk_controller_->AudioSourceMixToMono(audio_id, audioSetting.downMixMono);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceMixToMono failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_MUTE)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetMute audio_id: " << audio_id << ", mute: " << audioSetting.mute;
			success = sdk_controller_->AudioSourceSetMute(audio_id, audioSetting.mute);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetMute failed";
		}
        if (info.cmd & AUDIO_CONTROL_SET_AUDIO_TRACK)
        {
            LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetTracks audio_id: " << audio_id << ", audio_track: " << audioTrack;
            success = sdk_controller_->AudioSourceSetTracks(audio_id, audioTrack);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetTracks failed";
        }
		if (info.cmd & AUDIO_CONTROL_UPDATE_PCM)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] PCMAudioSourceUpdate audio_id: " << audio_id << ", audio_capture: " << info.audioInfo.audioCapture.toString();
			success = sdk_controller_->PCMAudioSourceUpdate(audio_id, info.audioInfo);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] PCMAudioSourceUpdate failed";
		}
		if (info.cmd & AUDIO_CONTROL_ENABLE_AEC)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetAECOption audio_id: " << audio_id << ", enable_aec: " << info.audioInfo.enableAec;
            success = sdk_controller_->LyraxEngineSetAECOption(audio_id, info.audioInfo.enableAec);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAECOption failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_AEC_REF_ID)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetAECConf audio_id: " << audio_id << ", aec_ref_id: " << info.audioInfo.aecRefID;
            WAS_AUDIO was = std::get<WAS_AUDIO>(info.audioInfo.audio);
			if (was.isLyrax)
			{
                success = sdk_controller_->LyraxEngineSetAudioRefId(audio_id, info.audioInfo.aecRefID);
				if (!success)
					LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAudioRefId failed";
			}
		}
		if (info.cmd & AUDIO_CONTROL_SET_AGC_OPTION)
        {
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetAGCOption audio_id: " << audio_id << ", agc_option: " << info.audioInfo.agcOption;
            success = sdk_controller_->LyraxEngineSetAGCOption(audio_id, info.audioInfo.agcOption);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAGCOption failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_ANS_OPTION)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetANSOption audio_id: " << audio_id << ", ans_option: " << info.audioInfo.ansOption;
            success = sdk_controller_->LyraxEngineSetANSOption(audio_id, info.audioInfo.ansOption);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetANSOption failed";
		}
        return success;
    }

    bool MediaMgr::GetAudioInfo(const std::string& audio_id, AUDIO_INFO* info, AUDIO_INFO_CMD cmd)
    {
        bool success = false;
        if (cmd & AUDIO_INFO_SETTING && info)
        {
            success = sdk_controller_->AudioSourceIsMute(audio_id, &info->audioSetting.mute);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceIsMute failed";

            success = sdk_controller_->AudioSourceIsEnableMixToMono(audio_id, &info->audioSetting.downMixMono);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceIsEnableMixToMono failed";

            success = sdk_controller_->AudioSourceGetBalance(audio_id, &info->audioSetting.balanceing);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetBalance failed";

            success = sdk_controller_->AudioSourceGetVolumn(audio_id, &info->audioSetting.volume);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetVolumn failed";

            success = sdk_controller_->AudioSourceGetSyncOffset(audio_id, &(info->audioSetting.syncOffset));
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetSyncOffset failed";

            success = sdk_controller_->AudioSourceGetMonitorType(audio_id, &info->audioSetting.monitorType);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetMonitorType failed audio_id: " << audio_id << ", monitor_type: " << static_cast<uint32_t>(info->audioSetting.monitorType);

            LOG(INFO) << "[MediaMgr::GetAudioInfo] audio_setting: " << info->audioSetting.toString();
        }

        return success;
    }

    bool MediaMgr::AddFilter(const UINT64 media_id, const FILTER& info)
    {
        std::string filter_id = "";
        Util::NumToString(info.id, &filter_id);

        bool success = false;
        if (info.type == FILTER_AUDIO)
        {
            std::string audio_id = "";
            Util::NumToString(media_id, &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(info.filter);
            switch (filter.filterType)
            {
            case AUDIO_FILTER_SPEEX_NOISE_SUPPRESS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSpeexNoiseSuppressAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter suppressLevel: " << filter.speexNoiseSuppressFilter.suppressLevel;
                success = sdk_controller_->CreateSpeexNoiseSuppressAudioFilter(audio_id, filter_id);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSpeexNoiseSuppressAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SpeexNoiseSuppressAudioFilterSetSuppressLevel(audio_id, filter_id, filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel failed";
            }
            break;
            case AUDIO_FILTER_SAMI_NOISE_SUPPRESS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiNoiseSuppressAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter speechRatio: " << filter.samiNoiseSuppressFilter.speechRatio;
                success = sdk_controller_->CreateSamiNoiseSuppressAudioFilter(audio_id, filter_id);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiNoiseSuppressAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetModel(audio_id, filter_id, filter.samiNoiseSuppressFilter.configFile);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiNoiseSuppressAudioFilterSetModel failed";
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetSpeechRatio(audio_id, filter_id, filter.samiNoiseSuppressFilter.speechRatio);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio failed";
            }
            break;
            case AUDIO_FILTER_SAMI_COMMON_METRICS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiCommonMetricsAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->CreateSamiCommonMetricsAudioFilter(audio_id, filter_id, filter.samiCommonMetricsFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiCommonMetricsAudioFilter failed";
            }
            break;
            case AUDIO_FILTER_SAMI_MDSP_EFFECT:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiMdspEffectAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->CreateSamiMdspEffectAudioFilter(audio_id, filter_id, info.enable.has_value() ? info.enable.value() : false, filter.samiMdspEffectFilter);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiMdspEffectAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SamiMdspEffectAudioFilterSetParam(audio_id, filter_id, filter.samiMdspEffectFilter.mdspParam);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiMdspEffectAudioFilterSetParam failed";
            }
            break;
            default:
                break;
            }
        }
        else if (info.type == FILTER_VISUAL)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

			VISUAL_FILTER filter = std::get<VISUAL_FILTER>(info.filter);
			switch (filter.filterType)
			{
			case VISUAL_FILTER_CORNER:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateCornerVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", corner filter info: " << filter.cornerFilter.toString();
				success = sdk_controller_->CreateCornerVisualFilter(layer_id, filter_id, filter.cornerFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateCornerVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_EDGE:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateEdgeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", edge filter info: " << filter.edgeFilter.toString();
				success = sdk_controller_->CreateEdgeVisualFilter(layer_id, filter_id, filter.edgeFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateEdgeVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_COLOR_ADJUST:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateColorAdjustVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", colorAdjust filter info: " << filter.colorAdjustFilter.toString();
				success = sdk_controller_->CreateColorAdjustVisualFilter(layer_id, filter_id, filter.colorAdjustFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateColorAdjustVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_CHROMA_KEY:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateChromaKeyVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", chromaKey filter info: " << filter.chromaKeyFilter.toString();
				success = sdk_controller_->CreateChromaKeyVisualFilter(layer_id, filter_id, filter.chromaKeyFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateChromaKeyVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_OVERLAY:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateOverlayVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", overlay filter info: " << filter.overlayFilter.toString();
				success = sdk_controller_->CreateOverlayVisualFilter(layer_id, filter_id, filter.overlayFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateOverlayVisualFilter failed";
			}
			break;
			case VISUAL_FILTER_HINT:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateHintVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", hint visual filter info: " << filter.hintFilter.toString();
				success = sdk_controller_->CreateHintVisualFilter(layer_id, filter_id, filter.hintFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateHintVisualFilter failed!";
			}
			break;
            case VISUAL_FILTER_SCALE:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateScaleVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", scale visual filter info: " << filter.scaleFilter.toString();
                success = sdk_controller_->CreateScaleVisualFilter(layer_id, filter_id, filter.scaleFilter);
                if (!success) {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateScaleVisualFilter failed";
                }
            }
            break;
            case VISUAL_FILTER_SHAPE:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateShapeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", shape visual filter info: " << filter.shapeFilter.toString();
                success = sdk_controller_->CreateShapeVisualFilter(layer_id, filter_id, filter.shapeFilter);
                if (!success) {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateShapeVisualFilter failed";
                }
            }
            break;
			case VISUAL_FILTER_COLOR_LUT:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateColorLutVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", lut visual filter info: " << filter.colorLutFilter.toString();
				success = sdk_controller_->CreateColorLutVisualFilter(layer_id, filter_id, filter.colorLutFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateColorLutVisualFilter failed";
			}
			break;
			case VISUAL_FILTER_SHARPNESS:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateSharpnessVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", sharpness visual filter info: " << filter.sharpnessFilter.toString();
				success = sdk_controller_->CreateSharpnessVisualFilter(layer_id, filter_id, filter.sharpnessFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateSharpnessVisualFilter failed";
			}
			break;
			default:
				break;
			}

			if (info.enable.has_value())
			{
				LOG(INFO) << "[MediaMgr::AddFilter] VisualFilterSetActive layer_id: " << layer_id << ", filter_id: " << filter_id << ", active: " << info.enable.value();
				if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
				{
					success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, info.enable.value(), "color_adjust");
				}
				else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
				{
					success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, info.enable.value(), "chroma_key");
				}
				else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
				{
					success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, info.enable.value(), "color_lut");
				}
				else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
				{
					success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, info.enable.value(), "sharpness");
				}
				else
				{
					success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, info.enable.value());
				}

				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] VisualFilterSetActive chromaKey failed";
			}
        }
        else if (info.type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            LAYER_INFO layerInfo{};
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(media_id, &layerInfo);
            
            SOURCE_INFO sourceInfo{};
            SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

			EFFECT_FILTER filter = std::get<EFFECT_FILTER>(info.filter);
            if (!filter.composers.empty() || (!filter.keyPath.key.empty() && !filter.keyPath.val.empty()))
            {
                success = EnableEffect(layer_id, true);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] EffectEnable failed";
                    return false;
                }

                if (!filter.composers.empty())
                {
                    std::stringstream        ss;
                    std::vector<std::string> composers{};
                    for (const auto& comp : filter.composers)
                    {
                        composers.push_back(sdk_controller_->ComposerToStr(comp));
                        ss << "\n"
                           << comp.toString();
                    }
                    std::vector<std::string> tags{};
                    for (const auto& comp : filter.composers)
                    {
                        tags.push_back(comp.composerTag);
                    }

                    LOG(INFO) << "[MediaMgr::AddFilter] EffectComposerSet layer_id: " << layer_id << ", composers: " << ss.str();
                    success = sdk_controller_->EffectComposerSet(layer_id, composers, tags);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::AddFilter] EffectComposerAdd failed";
                }

                if (!filter.keyPath.key.empty() && !filter.keyPath.val.empty())
                {
                    LOG(INFO) << "[MediaMgr::AddFilter] EffectSetBkImage layer_id: " << layer_id << ", filter key: " << filter.keyPath.key << ", path: " << filter.keyPath.val;
                    success = sdk_controller_->EffectSetBkImage(layer_id, filter.keyPath.key, filter.keyPath.val);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::AddFilter] EffectSetBkImage failed";
                }
            }
        }
        else if (info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(info.filter);
            if (filter.filterType = CANVAS_FILTER_TYPE::CANVAS_FILTER_TRANSITION)
            {
                success = sdk_controller_->CreateTransition(canvas_id, filter_id, filter.transitionFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateTransition failed";
            }
            else if (filter.filterType == CANVAS_FILTER_TYPE::CANVAS_FILTER_COLOR_ADJUST)
            {
                // TODO: @xuwanhui
            }
        }
        return success;
    }

    bool MediaMgr::RemoveFilter(const UINT64 media_id, const FILTER& info)
    {
        LOG(INFO) << "[MediaMgr::RemoveFilter] media_id: " << media_id << ", filter_id: " << info.id;
        std::string filter_id = "";
        Util::NumToString(info.id, &filter_id);

        bool success = false;
        if (info.type == FILTER_AUDIO)
        {
			std::string audio_id = "";
			Util::NumToString(media_id, &audio_id);
            success = sdk_controller_->AudioFilterDestroy(audio_id, filter_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveFilter] AudioFilterDestroy failed";
        }
        else if (info.type == FILTER_VISUAL)
        {
			std::string layer_id = "";
			Util::NumToString(media_id, &layer_id);

            VISUAL_FILTER filter = std::get<VISUAL_FILTER>(info.filter);
            if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
            {
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id, "color_adjust");
            }
            else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
            {
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id, "chroma_key");
            }
            else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
            {
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id, "color_lut");
            }
            else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
            {
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id, "sharpness");
            }
            else
            {
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id);
            }
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveFilter] VisualFilterDestroy failed";
        }
        else if (info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(info.filter);
            if (filter.filterType == CANVAS_FILTER_TRANSITION)
            {
                success = sdk_controller_->DestroyTransition(canvas_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::RemoveFilter] DestroyTransition failed, canvasID: " << canvas_id << ", filter_id: " << filter_id;
            }
            else if (filter.filterType == CANVAS_FILTER_COLOR_ADJUST)
            {
                // TODO: @xuwanhui
            }
        }

        return success;
    }

    bool MediaMgr::ControlFilter(const UINT64 media_id, const FILTER& filter_info, FILTER_CONTROL_CMD cmd)
    {
        std::string filter_id = "";
        Util::NumToString(filter_info.id, &filter_id);

        bool success = false;
        if (filter_info.type == FILTER_AUDIO)
        {
			std::string audio_id = "";
			Util::NumToString(media_id, &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_SET_SUPPRESS_LEVEL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter suppressLevel: " << filter.speexNoiseSuppressFilter.suppressLevel;
                success = sdk_controller_->SpeexNoiseSuppressAudioFilterSetSuppressLevel(audio_id, filter_id, filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel failed";
            }

            if (cmd & FILTER_CONTROL_SET_SPEECH_RATIO)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter speechRatio: " << filter.samiNoiseSuppressFilter.speechRatio;
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetSpeechRatio(audio_id, filter_id, filter.samiNoiseSuppressFilter.speechRatio);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio failed";
            }

            if (cmd & FILTER_CONTROL_SET_MODEL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetModel audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetModel(audio_id, filter_id, filter.samiNoiseSuppressFilter.configFile);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetModel failed";
            }

            if (cmd & FILTER_CONTROL_RESET_COMMON_METRICS)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiCommonMetricsAudioFilterReset audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SamiCommonMetricsAudioFilterReset(audio_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiCommonMetricsAudioFilterReset failed";
            }

            if (cmd & FILTER_CONTROL_SET_MDSP_PARAM)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiMdspEffectAudioFilterSetParam audio_id: " << audio_id << ", filter_id: " << filter_id << ", mdsp_param: " << filter.samiMdspEffectFilter.mdspParam;
                success = sdk_controller_->SamiMdspEffectAudioFilterSetParam(audio_id, filter_id, filter.samiMdspEffectFilter.mdspParam);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiMdspEffectAudioFilterSetParam failed";
            }

            if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] AudioFilterSetEnable audio_id: " << audio_id << ", filter_id: " << filter_id << " enable: " << filter_info.enable.value();
                success = sdk_controller_->AudioFilterSetEnable(audio_id, filter_id, filter_info.enable.value());
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] AudioFilterSetEnable failed";
            }
        }
        else if (filter_info.type == FILTER_VISUAL)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            VISUAL_FILTER filter = std::get<VISUAL_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_SET_EDGE)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] CreateEdgeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", edge filter info: " << filter.edgeFilter.toString();
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterDestroy failed";
                success = sdk_controller_->CreateEdgeVisualFilter(layer_id, filter_id, filter.edgeFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] CreateEdgeVisualFilter failed";
            }
            if (cmd & FILTER_CONTROL_SET_CORNER)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] CreateCornerVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", corner filter info: " << filter.cornerFilter.toString();
                success = sdk_controller_->VisualFilterDestroy(layer_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterDestroy failed";
                success = sdk_controller_->CreateCornerVisualFilter(layer_id, filter_id, filter.cornerFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] CreateCornerVisualFilter failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SATURATION)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSaturation layer_id: " << layer_id << ", filter_id: " << filter_id << ", saturation: " << filter.colorAdjustFilter.saturation;
                success = sdk_controller_->SetColorFilterSaturation(layer_id, filter_id, filter.colorAdjustFilter.saturation);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSaturation failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_HUE_SHIFT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterHueShift layer_id: " << layer_id << ", filter_id: " << filter_id << " hueShift: " << filter.colorAdjustFilter.hueShift;
                success = sdk_controller_->SetColorFilterHueShift(layer_id, filter_id, filter.colorAdjustFilter.hueShift);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterHueShift failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_ADD_COLOR)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterAddColor layer_id: " << layer_id << ", filter_id: " << filter_id << " addColor: " << filter.colorAdjustFilter.addColor;
                success = sdk_controller_->SetColorFilterAddColor(layer_id, filter_id, filter.colorAdjustFilter.addColor);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterAddColor failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_MUL_COLOR)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterMulColor layer_id: " << layer_id << ", filter_id: " << filter_id << " mulColor: " << filter.colorAdjustFilter.mulColor;
                success = sdk_controller_->SetColorFilterMulColor(layer_id, filter_id, filter.colorAdjustFilter.mulColor);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterMulColor failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_BRIGHTNESS)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightness: " << filter.colorAdjustFilter.brightness;
                    success = sdk_controller_->SetColorFilterBrightness(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.brightness);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightness: " << filter.chromaKeyFilter.brightness;
                    success = sdk_controller_->SetColorFilterBrightness(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.brightness);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterBrightness failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_CONTRAST)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", contrast: " << filter.colorAdjustFilter.contrast;
                    success = sdk_controller_->SetColorFilterContrast(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.contrast);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", contrast: " << filter.chromaKeyFilter.contrast;
                    success = sdk_controller_->SetColorFilterContrast(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.contrast);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterContrast failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_GAMMA)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma colorAdjust visual_id: " << layer_id << ", filter_id: " << filter_id << " gamma: " << filter.colorAdjustFilter.gamma;
                    success = sdk_controller_->SetColorFilterGamma(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.gamma);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << " gamma: " << filter.chromaKeyFilter.gamma;
                    success = sdk_controller_->SetColorFilterGamma(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.gamma);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterGamma failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_OPACITY)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", opacity: " << filter.colorAdjustFilter.opacity;
                    success = sdk_controller_->SetColorFilterOpacity(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.opacity);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", opacity: " << filter.chromaKeyFilter.opacity;
                    success = sdk_controller_->SetColorFilterOpacity(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.opacity);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterOpacity failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_CHROMA)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterChroma layer_id: " << layer_id << ", filter_id: " << filter_id << ", chroma: " << filter.chromaKeyFilter.chroma;
                success = sdk_controller_->SetColorFilterChroma(layer_id, filter_id, filter.chromaKeyFilter.chroma);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterChroma failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SIMILARITY)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSimilarity layer_id: " << layer_id << ", filter_id: " << filter_id << ", similarity: " << filter.chromaKeyFilter.similarity;
                success = sdk_controller_->SetColorFilterSimilarity(layer_id, filter_id, filter.chromaKeyFilter.similarity);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSimilarity failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SMOOTHNESS)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSmoothness layer_id: " << layer_id << ", filter_id: " << filter_id << ", smoothness: " << filter.chromaKeyFilter.smoothness;
                success = sdk_controller_->SetColorFilterSmoothness(layer_id, filter_id, filter.chromaKeyFilter.smoothness);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSmoothness failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SPILL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSpill layer_id: " << layer_id << ", filter_id: " << filter_id << ", spill: " << filter.chromaKeyFilter.spill;
                success = sdk_controller_->SetColorFilterSpill(layer_id, filter_id, filter.chromaKeyFilter.spill);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSpill failed";
            }
            if (cmd & FILTER_CONTROL_SET_OVERLAY)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] VisualSetBackground layer_id: " << layer_id << ", filter_id: " << filter_id << ", overlay filter info: " << filter.overlayFilter.toString();
                success = sdk_controller_->SetOverlayFilterProperty(layer_id, filter_id, filter.overlayFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetOverlayFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_HINT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetHintFilterProperty layer_id: " << layer_id << ", filter_id: " << filter_id << ", hint visual filter info: " << filter.hintFilter.toString();
                success = sdk_controller_->SetHintFilterProperty(layer_id, filter_id, filter.hintFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetHintFilterProperty failed!";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_LUT)
            {
                success = sdk_controller_->SetColorLutFilterProperty(layer_id, filter_id, filter.colorLutFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorLutFilterProperty failed";

            }
            if (cmd & FILTER_CONTROL_SET_SHARPNESS)
            {
                success = sdk_controller_->SetSharpnessFilterProperty(layer_id, filter_id, filter.sharpnessFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetSharpnessFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_SCALE)
            {
                success = sdk_controller_->SetScaleFilterProperty(layer_id, filter_id, filter.scaleFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetScaleFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_SHAPE)
            {
                success = sdk_controller_->SetShapeFilterProperty(layer_id, filter_id, filter.shapeFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetShapeFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] VisualFilterSetActive layer_id: " << layer_id << ", filter_id: " << filter_id;
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, filter_info.enable.value(), "color_adjust");
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, filter_info.enable.value(), "chroma_key");
                }
                else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
                {
                    success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, filter_info.enable.value(), "color_lut");
                }
                else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
                {
                    success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, filter_info.enable.value(), "sharpness");
                }
                else
                {
                    success = sdk_controller_->VisualFilterSetActive(layer_id, filter_id, filter_info.enable.value());
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterSetActive failed";
            }
        }
        else if (filter_info.type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            EFFECT_FILTER filter = std::get<EFFECT_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_ADD_COMPOSER)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                std::stringstream        ss;
                std::vector<std::string> add_composers{};
                for (const auto& comp : filter.addComposers)
                {
                    add_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }
                std::vector<std::string> add_composers_tag{};
                for (const auto& comp : filter.addComposers)
                {
                    add_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerAdd layer_id: " << layer_id << ", filter_id: " << filter_id << ", add composers: " << ss.str();
                success = sdk_controller_->EffectComposerAdd(layer_id, add_composers, add_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerAdd failed";
            }
            if (cmd & FILTER_CONTROL_REMOVE_COMPOSER)
            {
                if (filter.composers.empty())
                {
                    success = EnableEffect(layer_id, false);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                std::stringstream        ss;
                std::vector<std::string> remove_composers{};
                for (const auto& comp : filter.removeComposers)
                {
                    remove_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerRemove layer_id: " << layer_id << ", filter_id: " << filter_id << ", remove composers: " << ss.str();
                success = sdk_controller_->EffectComposerRemove(layer_id, remove_composers);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerRemove failed";
            }
            if (cmd & FILTER_CONTROL_SET_COMPOSER)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                }
                else
                {
                    success = EnableEffect(layer_id, false);
                }

                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed, enable: " << 1;
                    return false;
                }

                std::stringstream        ss;
                std::vector<std::string> reset_composers{};
                for (const auto& comp : filter.composers)
                {
                    reset_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }
                std::vector<std::string> reset_composers_tag{};
                for (const auto& comp : filter.composers)
                {
                    reset_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSet layer_id: " << layer_id << ", filter_id: " << filter_id << ", reset composers: " << ss.str();
                success = sdk_controller_->EffectComposerSet(layer_id, reset_composers, reset_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerSet failed";
            }
            if (cmd & FILTER_CONTROL_UPDATE_COMPOSER)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSet layer_id: " << layer_id << ", filter_id: " << filter_id << ", update composer: " << filter.updateComposer.toString();
                bool val = false;
                Util::StringToNum(filter.updateComposer.keyVal.val, &val);
                string updateComposerTag = filter.updateComposer.composerTag;

                success = sdk_controller_->EffectComposerUpdate(layer_id, filter.updateComposer.effectPath, filter.updateComposer.keyVal.key, val, updateComposerTag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerUpdate failed";
            }
            if (cmd & FILTER_CONTROL_REPLACE_COMPOSERS)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                }

                std::stringstream        ss1;
                std::vector<std::string> old_composers{};
                for (const auto& comp : filter.oldComposers)
                {
                    old_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss1 << "\n"
                        << comp.toString();
                }

                std::stringstream        ss2;
                std::vector<std::string> new_composers{};
                for (const auto& comp : filter.newComposers)
                {
                    new_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss2 << "\n"
                        << comp.toString();
                }
                std::vector<std::string> new_composers_tag{};
                for (const auto& comp : filter.newComposers)
                {
                    new_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerReplace visual_id: " << media_id << ", filter_id: " << filter_id << ", old composers: " << ss1.str() << ", new composers: " << ss2.str();
                success = sdk_controller_->EffectComposerReplace(layer_id, old_composers, new_composers, new_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerReplace failed";
            }
            if (cmd & FILTER_CONTROL_SET_COMPOSER_TEXT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSetText layer_id: " << layer_id << ", filter_id: " << filter_id << ", set composer text key: " << filter.keyText.key << ", val: " << filter.keyText.val;
                success = sdk_controller_->EffectComposerSetText(layer_id, filter.keyText.key, filter.keyText.val);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerSetText failed";
            }
            if (cmd & FILTER_CONTROL_SET_BACKGROUND_IMAGE)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetBkImage layer_id: " << layer_id << ", filter_id: " << filter_id << ", set bkImage key: " << filter.keyPath.key << ", val: " << filter.keyPath.val;
                if (!filter.keyPath.key.empty() && !filter.keyPath.val.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                success = sdk_controller_->EffectSetBkImage(layer_id, filter.keyPath.key, filter.keyPath.val);
                filter.result = success;
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetBkImage failed";
            }
            if (cmd & FILTER_CONTROL_SET_BRIGHT_CONFIG)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetPicQualityBrightness layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightConfig enable: " << filter.brightConfig.enable << ", isAuto: " << filter.brightConfig.isAuto << ", assetPath: " << filter.brightConfig.assetPath << ", key: " << filter.brightConfig.keyVal.key << ", val: " << filter.brightConfig.keyVal.val;
                if (filter.brightConfig.enable)
                {
                    success = EnableEffect(layer_id, true);
                }
                else if (filter.composers.empty())
                {
                    success = EnableEffect(layer_id, false);
                }

                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                    return false;
                }

                success = sdk_controller_->EffectSetPicQualityBrightness(layer_id, filter.brightConfig);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetPicQualityBrightness failed";
            }
            if (cmd & FILTER_CONTROL_SET_EFFECT_MSG)
            {
				LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetMsg layer_id: " << layer_id << ", filter_id: " << filter_id << ", msg_id: " << filter.effectMsg.msgID << ", arg1: " << filter.effectMsg.arg1 << ", arg2: " << filter.effectMsg.arg2 << ", arg3: " << filter.effectMsg.arg3;
				success = sdk_controller_->EffectSetMsg(layer_id, filter.effectMsg);
				if (!success)
					LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetMsg failed";
            }
        }
        else if (filter_info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(filter_info.filter);
            if (filter.filterType == CANVAS_FILTER_TRANSITION)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetTransitionProperty canvas_id: " << canvas_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SetTransitionProperty(canvas_id, filter_id, filter.transitionFilter);
                if (!success)
                    LOG(ERROR) << "[[MediaMgr::ControlFilter] SetTransitionProperty failed";
            }
            else if (filter.filterType == CANVAS_FILTER_COLOR_ADJUST)
            {
                if (cmd & FILTER_CONTROL_SET_COLOR_BRIGHTNESS)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", brightness: " << filter.colorAdjustFilter.brightness;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterBrightness failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_CONTRAST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", contrast: " << filter.colorAdjustFilter.contrast;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterContrast failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_GAMMA)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << " gamma: " << filter.colorAdjustFilter.gamma;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterGamma failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_OPACITY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", opacity: " << filter.colorAdjustFilter.opacity;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterOpacity failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_SATURATION)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSaturation canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", saturation: " << filter.colorAdjustFilter.saturation;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSaturation failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_HUE_SHIFT)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterHueShift canvas_id: " << canvas_id << ", filter_id: " << filter_id << " hueShift: " << filter.colorAdjustFilter.hueShift;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterHueShift failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_ADD_COLOR)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterAddColor canvas_id: " << canvas_id << ", filter_id: " << filter_id << " addColor: " << filter.colorAdjustFilter.addColor;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterAddColor failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_MUL_COLOR)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterMulColor canvas_id: " << canvas_id << ", filter_id: " << filter_id << " mulColor: " << filter.colorAdjustFilter.mulColor;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterMulColor failed";
                }
                if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] CanvasFilterSetActive canvas_id: " << canvas_id << ", filter_id: " << filter_id;
                    // TODO: @xuwanhui
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] CanvasFilterSetActive failed";
                }
            }
        }
        return success;
    }

    bool MediaMgr::GetFilterInfo(const std::string& filter_id, FILTER* info, FILTER_INFO_CMD cmd)
    {
        if (info->mediaIDs.empty())
            return false;

        bool success = false;
        if (info->type == FILTER_AUDIO)
        {
            std::string audio_id = "";
            Util::NumToString(info->mediaIDs[0], &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(info->filter);
            if (cmd & FILTER_INFO_SUPPRESS_LEVEL)
            {
                success = sdk_controller_->SpeexNoiseSuppressAudioFilterGetSuppressLevel(audio_id, filter_id, &filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] SpeexNoiseSuppressAudioFilterGetSuppressLevel failed";
            }
            if (cmd & FILTER_INFO_GET_ENABLE)
            {
                success = sdk_controller_->AudioFilterGetEnable(audio_id, filter_id, &info->enable.value());
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] AudioFilterGetEnable failed";
            }
            info->filter = filter;
        }
        else if (info->type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(info->mediaIDs[0], &layer_id);

            EFFECT_FILTER filter = std::get<EFFECT_FILTER>(info->filter);
            if (cmd & FILTER_INFO_COMPOSER_EXCLUSION)
            {
                success = sdk_controller_->EffectComposerGetExclusion(layer_id, filter.pathTag.key, filter.pathTag.val, &filter.exclusion);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] EffectComposerGetExclusion failed";
            }
            info->filter = filter;
            LOG(INFO) << "[MediaMgr::GetFilterInfo] effect filter exclusion: " << filter.exclusion;
        }

        return success;
    }

    bool MediaMgr::SetModelLayout(UINT video_model, const Gdiplus::RectF& rect, const Gdiplus::RectF& layout_rect, const Gdiplus::SizeF& outputSize, HWND hwnd_parent /*= NULL*/)
    {
        if (!init_finish_)
        {
            LOG(ERROR) << "[MediaMgr::SetModelLayout] init_finish_ is false";
            return false;
        }

        auto video_model_ctx = GetModelCtx(video_model);
        if (!video_model_ctx)
        {
            video_model_ctx = std::make_shared<VideoModelContext>();
            InitDefaultCanvasContext(video_model_ctx.get());
            video_model_ctx->video_model = video_model;
            video_model_ctx->video_param.fps = init_param_.fps;
            if (outputSize.Width <= EPS && outputSize.Height <= EPS)
            {
                video_model_ctx->video_param.output_size_width = init_param_.outputSize.cx;
                video_model_ctx->video_param.output_size_height = init_param_.outputSize.cy;
            }
            else
            {
                video_model_ctx->video_param.output_size_width = std::round(outputSize.Width);
                video_model_ctx->video_param.output_size_height = std::round(outputSize.Height);
                init_param_.outputSize.cx = std::round(outputSize.Width);
                init_param_.outputSize.cy = std::round(outputSize.Height);
            }

            HWND hwnd = hwnd_parent == NULL ? init_param_.bottomWnd : hwnd_parent;
            if (!sdk_controller_->CreateModel(video_model_ctx->video_model, video_model_ctx->video_param.output_size_width, video_model_ctx->video_param.output_size_height, video_model_ctx->video_param.fps, hwnd))
            {
                LOG(ERROR) << "[MediaMgr::SetModelLayout] PreviewWindowAdd failed";
                return false;
            }

            sdk_controller_->PreviewWindowEnableShow(video_model, false);
            sdk_controller_->PreviewWindowEnableDraw(video_model, false);

            if (!SetVideoParam(sdk_controller_, video_model_ctx))
            {
                LOG(ERROR) << "[MediaMgr::SetModelLayout] SetVideoParam failed";
                return false;
            }

            video_model_ctx->show_canvas = false;
            std::unique_lock<std::mutex> lock(model_ctx_map_mutex_);
            model_ctx_map_[video_model] = video_model_ctx;
        }
        else
        {
            bool has_size_changed = false;
            if (outputSize.Width > EPS && std::abs(video_model_ctx->video_param.output_size_width - outputSize.Width) > EPS)
            {
                video_model_ctx->video_param.output_size_width = outputSize.Width;
                has_size_changed = true;
            }
            if (outputSize.Height > EPS && std::abs(video_model_ctx->video_param.output_size_height - outputSize.Height) > EPS)
            {
                video_model_ctx->video_param.output_size_height = outputSize.Height;
                has_size_changed = true;
            }

            if (has_size_changed)
            {
                if (!SetVideoParam(sdk_controller_, video_model_ctx))
                {
                    LOG(ERROR) << "[MediaMgr::SetModelLayout] SetVideoParam failed";
                    return false;
                }
            }
        }

        {
            auto found1 = cache_video_model_show_map_.find(video_model_ctx->video_model);
            if (found1 != cache_video_model_show_map_.end())
            {
                bool show = found1->second;
                cache_video_model_show_map_.erase(found1);
                ShowModel(video_model_ctx->video_model, show);
            }

            auto found2 = cache_video_model_display_map_.find(video_model_ctx->video_model);
            if (found2 != cache_video_model_display_map_.end())
            {
                bool show = found2->second;
                cache_video_model_display_map_.erase(found2);
                SetDisplay(video_model_ctx->video_model, show);
            }
        }

        {
            bool has_preview_changed = false;
            if (rect.X > EPS && std::abs(video_model_ctx->preview_x - rect.X) > EPS)
            {
                video_model_ctx->preview_x = rect.X;
                has_preview_changed = true;
            }
            if (rect.Y > EPS && std::abs(video_model_ctx->preview_y - rect.Y) > EPS)
            {
                video_model_ctx->preview_y = rect.Y;
                has_preview_changed = true;
            }
            if (rect.Width > EPS && std::abs(video_model_ctx->preview_width - rect.Width) > EPS)
            {
                video_model_ctx->preview_width = rect.Width;
                has_preview_changed = true;
            }
            if (rect.Height > EPS && std::abs(video_model_ctx->preview_height - rect.Height) > EPS)
            {
                video_model_ctx->preview_height = rect.Height;
                has_preview_changed = true;
            }

            if (layout_rect.X > EPS && std::abs(video_model_ctx->layout_preview_x - layout_rect.X) > EPS)
            {
                video_model_ctx->layout_preview_x = layout_rect.X;
                has_preview_changed = true;
            }
            if (layout_rect.Y > EPS && std::abs(video_model_ctx->layout_preview_y - layout_rect.Y) > EPS)
            {
                video_model_ctx->layout_preview_y = layout_rect.Y;
                has_preview_changed = true;
            }
            if (layout_rect.Width > EPS && std::abs(video_model_ctx->layout_preview_width - layout_rect.Width) > EPS)
            {
                video_model_ctx->layout_preview_width = layout_rect.Width;
                has_preview_changed = true;
            }
            if (layout_rect.Height > EPS && std::abs(video_model_ctx->layout_preview_height - layout_rect.Height) > EPS)
            {
                video_model_ctx->layout_preview_height = layout_rect.Height;
                has_preview_changed = true;
            }

            if (has_preview_changed)
            {
                if (!SetPreviewPosition(sdk_controller_, video_model_ctx))
                {
                    LOG(ERROR) << "[MediaMgr::SetModelLayout] SetPreviewPosition failed";
                    return false;
                }
            }
        }

        return true;
    }

    bool MediaMgr::ShowModel(uint32_t video_model, bool show)
    {
        if (!init_sdk_ || !init_api_ || !sdk_controller_)
        {
            LOG(ERROR) << "[MediaMgr::ShowModel] init_sdk_ or init_api_ or sdk_ is nullptr";
            cache_video_model_show_map_[video_model] = show;
            return false;
        }

        auto video_model_ctx = GetModelCtx(video_model);
        if (!video_model_ctx)
        {
            LOG(WARNING) << "[MediaMgr::ShowModel] video_model_ctx is nullptr, video_model: " << video_model;
            cache_video_model_show_map_[video_model] = show;
            return false;
        }

        bool success = sdk_controller_->SetModelActive(video_model_ctx->video_model, show);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::ShowModel] SetModelActive failed";
            return false;
        }

        success = sdk_controller_->PreviewWindowEnableShow(video_model_ctx->video_model, show);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::ShowModel] PreviewWindowEnableShow failed";
            return false;
        }

        bool enable_canvas_preview = all_canvas_enable_preview_ && show;
        LOG(INFO) << "[MediaMgr::ShowModel] video_model: " << video_model_ctx->video_model << ",  all_canvas_enable_preview_: " << all_canvas_enable_preview_ << ", show: " << show << ", enable_canvas_preview: " << enable_canvas_preview;

        success = sdk_controller_->PreviewWindowEnableDraw(video_model_ctx->video_model, enable_canvas_preview);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::ShowCanvas] PreviewWindowEnableDraw failed";
            return false;
        }

        video_model_ctx->show_canvas = show;
        return success;
    }

    bool MediaMgr::DestroyModel(uint32_t model)
    {
        auto video_model_ctx = GetModelCtx(model);
        if (!video_model_ctx)
            return false;

        bool success = sdk_controller_->RemoveModel(video_model_ctx->video_model);
        if (success)
        {
            std::unique_lock<std::mutex> lock(model_ctx_map_mutex_);
            model_ctx_map_.erase(model);
        }

        return success;
    }

    bool MediaMgr::AddLayer(const uint64_t canvas_id, const LAYER_INFO& layerInfo)
    {
        std::string layer_id = "";
        Util::NumToString(layerInfo.id, &layer_id);

        std::string canvas_id_str = "";
        Util::NumToString(canvas_id, &canvas_id_str);

        std::string source_id = "";
        Util::NumToString(layerInfo.sourceID, &source_id);

        bool success = false;
        SOURCE_INFO sourceInfo{};
        SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

        VISUAL_TYPE type = SourceMgr::GetInstance()->GetCompositeRealType(sourceInfo.id);
        if (type == VISUAL_CAMERA || type == VISUAL_FAV)
        {
            bool       isFallbackImage = false;
            LAYER_INFO layer_info = layerInfo;
			TRANSFORM& transform = layer_info.transform;
			if (sourceInfo.type == VISUAL_IMAGE)
			{
				auto& meta = SourceMgr::GetInstance()->GetCompositeMetas();
				if (meta[sourceInfo.id].isFallback)
				{
					IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(sourceInfo.source);
					transform.scale.X = layerInfo.transform.scale.X * layerInfo.transform.size.Width / image.materialDesc.size.Width;
					transform.scale.Y = layerInfo.transform.scale.Y * layerInfo.transform.size.Height / image.materialDesc.size.Height;
					transform.angle = image.materialDesc.angle;
					transform.size.Width = image.materialDesc.size.Width;
					transform.size.Height = image.materialDesc.size.Height;
                    isFallbackImage = true;
				}
			}

            success = sdk_controller_->CreateLayerWithFilter(layer_id, canvas_id_str, source_id, layer_info, type == VISUAL_CAMERA);
            if (isFallbackImage)
            {
                sdk_controller_->VisualFilterDestroy(layer_id, "filter", "");
            }
        }
        else if (type == VISUAL_VIRTUAL_CAMERA)
        {
            VIRTUAL_CAMERA_SOURCE virtual_camera = std::get<VIRTUAL_CAMERA_SOURCE>(sourceInfo.source);
            success = sdk_controller_->CreateVirtualCameraLayer(layer_id, virtual_camera);
        }
        else
        {
            success = sdk_controller_->CreateLayer(layer_id, canvas_id_str, source_id, layerInfo);
			if (sourceInfo.type == VISUAL_GRAFFITI)
			{
                GRAFFITI_SOURCE graffiti = std::get<GRAFFITI_SOURCE>(sourceInfo.source);
                success = sdk_controller_->SetLayerNeedDrawBorder(layer_id, false);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddLayer] SetLayerNeedDrawBorder failed";
                success = sdk_controller_->GraffitiSourceSetEditState(layer_id, graffiti.editable);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddLayer] GraffitiSourceSetEditState failed";
			}
        }

        if (type == VISUAL_VIRTUAL_CAMERA)
        {
            return success;
        }

        if (success)
        {
            success = sdk_controller_->LayerSetVisible(layer_id, layerInfo.show);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddLayer] LayerSetVisible failed";
            success = sdk_controller_->LayerSetLock(layer_id, layerInfo.locked);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddLayer] LayerSetLock failed";
            for (const VISUAL_FLAG flag : layerInfo.visualFlags)
            {
                success = sdk_controller_->LayerSetFlag(layer_id, flag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddLayer] LayerSetFlag failed";
            }
        }
        else
        {
            LOG(ERROR) << "[MediaMgr::AddLayer] CreateLayer failed";
            sdk_controller_->DestroyLayer(layer_id);
        }

        VISUAL_SOURCE_RESULT result = sourceInfo.result;
		if (type == VISUAL_CAMERA && sourceInfo.type == VISUAL_IMAGE)
		{
            OnLayerCreated(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL, result.errorCode, result.type, result.reason);
            OnLayerFallback(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL, result.errorCode, result.type, result.reason, true);
		}
		else
		{
            OnLayerCreated(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL,  result.errorCode, result.type, result.reason);
			OnLayerFallback(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL,  result.errorCode, result.type, result.reason, false);
		}

        return success;
    }

    bool MediaMgr::RemoveLayer(const uint64_t layer_id)
    {
        LOG(INFO) << "[MediaMgr::RemoveLayer] layer_id: " << layer_id;
        std::string layer_id_str = "";
        Util::NumToString(layer_id, &layer_id_str);
        bool success = sdk_controller_->DestroyLayer(layer_id_str);
        if (!success)
            LOG(WARNING) << "[MediaMgr::RemoveLayer] DestroyLayer failed, layer_id: " << layer_id;

        OnLayerDeleted(layer_id_str);
        return success;
    }

    bool MediaMgr::ControlLayer(const LAYER_INFO& layer_info, LAYER_CONTROL_CMD cmd)
    {
        std::string canvas_id = "";
        Util::NumToString(layer_info.canvasID, &canvas_id);

        std::string layer_id = "";
        Util::NumToString(layer_info.id, &layer_id);

        bool        success = false;
        if (cmd & LAYER_CONTROL_SELECT)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] SelectLayer canvas_id: " << canvas_id << ", layer_id: " << layer_id;
            success = sdk_controller_->SelectLayer(canvas_id, layer_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] SelectLayer failed";
        }

        if (cmd & LAYER_CONTROL_SET_CLIP_MASK)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerClipMask layer_id: " << layer_id;
            success = sdk_controller_->LayerClipMask(layer_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerClipMask failed";
        }

        if (cmd & LAYER_CONTROL_SET_SHOW)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetVisible layer_id: " << layer_id << ", show: " << layer_info.show;
            success = sdk_controller_->LayerSetVisible(layer_id, layer_info.show);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetVisible failed";
        }

        if (cmd & LAYER_CONTROL_SET_LOCK)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetLock layer_id: " << layer_id << ", locked: " << layer_info.locked;
            success = sdk_controller_->LayerSetLock(layer_id, layer_info.locked);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetLock failed";
        }

        if (cmd & LAYER_CONTROL_SET_MOVE_RANGE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetMoveRange layer_id: " << layer_id << ", moveRange: " << layer_info.moveRange.toString();
            success = sdk_controller_->LayerSetMoveRange(layer_id, layer_info.moveRange.x, layer_info.moveRange.y, layer_info.moveRange.z, layer_info.moveRange.w);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMoveRange failed";
        }

        TRANSFORM trans = layer_info.transform;
        SOURCE_INFO sourceInfo{};
        SourceMgr::GetInstance()->GetSourceInfoByID(layer_info.sourceID, &sourceInfo);
        if (cmd & LAYER_CONTROL_SET_TRANSLATE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetPosition layer_id: " << layer_id << ", translate=[" << trans.translate.X << "," << trans.translate.Y << "]";
            success = sdk_controller_->LayerSetPosition(layer_id, trans.translate.X, trans.translate.Y);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetPosition failed";
        }
        if (cmd & LAYER_CONTROL_SET_FLIPH)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetHorizontalFlip layer_id: " << layer_id << ", hFlip: " << trans.hFlip;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraFlipH(trans.hFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraFlipH failed";
            }
            else
            {
                success = sdk_controller_->LayerSetHorizontalFlip(layer_id, trans.hFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetHorizontalFlip failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_FLIPV)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetVerticalFlip layer_id: " << layer_id << ", vFlip: " << trans.vFlip;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraFlipV(trans.vFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraFlipV failed";
            }
            else
            {
                success = sdk_controller_->LayerSetVerticalFlip(layer_id, trans.vFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetVerticalFlip failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_ROTATE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetRotate layer_id: " << layer_id << ", angle: " << trans.angle;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraRotate(trans.angle);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraRotate failed";
            }
            else
            {
                if (sourceInfo.type == VISUAL_FAV)
                {
                    FAV_SOURCE fav = std::get<FAV_SOURCE>(sourceInfo.source);
                    trans.angle += fav.materialDesc.angle;
                }
                else if (sourceInfo.type == VISUAL_IMAGE)
                {
                    IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(sourceInfo.source);
                    trans.angle += image.materialDesc.angle;
                }

                trans.angle = fmod(trans.angle, 360);
                trans.angle = trans.angle < 0.0f ? trans.angle + 360.0f : trans.angle;
                success = sdk_controller_->LayerSetRotate(layer_id, trans.angle);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetRotate failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_SCALE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetScale layer_id: " << layer_id << ", scale=[" << trans.scale.X << "," << trans.scale.Y << "]";
            success = sdk_controller_->LayerSetScale(layer_id, trans.scale.X, trans.scale.Y);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetScale failed";
        }

        float preview_width = .0f, preview_height = .0f;
        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(layer_info.canvasID))
        {
            CANVAS_INFO_EX canvas_info_ex{};
            pCanvas->GetCanvasInfo(&canvas_info_ex);
            preview_width = canvas_info_ex.rect.Width;
            preview_height = canvas_info_ex.rect.Height;
        }
        if (cmd & LAYER_CONTROL_SET_MIN_SCALE)
        {
            float newMinScale = .0f;
            if (trans.minScale.X > EPS)
            {
                newMinScale = preview_width * trans.minScale.X / layer_info.transform.size.Width;
            }
            else if (trans.minScale.Y > EPS)
            {
                newMinScale = preview_height * trans.minScale.Y / layer_info.transform.size.Height;
            }

            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetMinScale layer_id: " << layer_id << "], minScale=[" << trans.minScale.X << "," << trans.minScale.Y << "], newMinScale: " << newMinScale;
            success = sdk_controller_->LayerSetMinScale(layer_id, newMinScale, newMinScale);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMinScale failed";
        }
        if (cmd & LAYER_CONTROL_SET_MAX_SCALE)
        {
            float newMaxScale = .0f;
            if (trans.maxScale.X > EPS)
            {
                newMaxScale = preview_width * trans.maxScale.X / layer_info.transform.size.Width;
            }
            else if (trans.minScale.Y > EPS)
            {
                newMaxScale = preview_height * trans.maxScale.Y / layer_info.transform.size.Height;
            }

            LOG(INFO) << "[MediaMgr::ControlLayer] VisualSetMaxScale layer_id: " << layer_id << "], maxScale=[" << trans.maxScale.X << "," << trans.maxScale.Y << "], newMaxScale: " << newMaxScale;
            success = sdk_controller_->LayerSetMaxScale(layer_id, newMaxScale, newMaxScale);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMaxScale failed";
        }
        if (cmd & LAYER_CONTROL_SET_CLIP)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetClip layer_id: " << layer_id << ", clipRange: " << trans.clipRange.toString();
            success = sdk_controller_->LayerSetClip(layer_id, trans.clipRange.x, trans.clipRange.y, trans.clipRange.z, trans.clipRange.w);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetClip failed";
        }
        if (cmd & LAYER_CONTROL_SET_VISUAL_FLAGS)
        {
            if (!layer_info.visualFlags.empty())
            {
                std::stringstream ss;
                for (const VISUAL_FLAG flag : layer_info.visualFlags)
                {
                    ss << flag << ", ";
                    success = sdk_controller_->LayerSetFlag(layer_id, flag);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetFlag failed";
                }
                LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetFlag layer_id: " << layer_id << ", visualFlags: " << ss.str();
            }
        }
        if (cmd & LAYER_CONTROL_SET_PREPARE_CLIP)
        {
            TRANSFORM prepare_trans;
            prepare_trans.clipRange.x = layer_info.prepareClip.x * layer_info.transform.size.Width;
            prepare_trans.clipRange.y = layer_info.prepareClip.y * layer_info.transform.size.Height;
            prepare_trans.clipRange.z = layer_info.prepareClip.z * layer_info.transform.size.Width;
            prepare_trans.clipRange.w = layer_info.prepareClip.w * layer_info.transform.size.Height;
            success = sdk_controller_->CameraSourceSetPrepareTransform(layer_id, prepare_trans);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] CameraSourceSetPrepareTransform failed";

            LOG(INFO) << "[MediaMgr::ControlLayer] CameraSourceSetPrepareTransform layer_id: " << layer_id << ", prepareClip: " << layer_info.prepareClip.toString() << ", rect=[" << layer_info.transform.size.Width << "," << layer_info.transform.size.Height << "], prepare_trans clip=[" << prepare_trans.clipRange.x << "," << prepare_trans.clipRange.y << "," << prepare_trans.clipRange.z << "," << prepare_trans.clipRange.w << "]";
        }

        return success;
    }

    bool MediaMgr::SetMixParameter(const uint32_t video_model_id, const VIDEO_MIX_PARAM& param)
    {
        auto canvas_ctx = GetModelCtx(video_model_id);
        if (!canvas_ctx)
        {
            init_param_.fps = param.fps;
            init_param_.outputSize.cx = param.outputSize.Width;
            init_param_.outputSize.cy = param.outputSize.Height;
            LOG(INFO) << "[MediaMgr::SetMixParameter] videoModeCtx is nullptr, videoModel: " << video_model_id;
            return false;
        }

        canvas_ctx->video_param.output_size_width = param.outputSize.Width;
        canvas_ctx->video_param.output_size_height = param.outputSize.Height;
        canvas_ctx->video_param.focus_nv12 = param.focusNv12;
        canvas_ctx->video_param.fps = param.fps < EPS ? 60.0f : param.fps;

        bool success = false;

        do
        {
            canvas_ctx->video_param.color_space = param.colorConfig.colorSpace;
            canvas_ctx->video_param.color_transfer = param.colorConfig.colorTransfer;
            canvas_ctx->video_param.color_range = param.colorConfig.colorRange;
            success = true;
        } while (0);

        if (!success)
            return false;

        success = SetVideoParam(sdk_controller_, canvas_ctx);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetMixParameter] SetVideoParam failed";

        return success;
    }

    bool MediaMgr::VideoQualityManagerInitialize(const VideoQualityManagerInitializeParam& param, VideoQualityManagerGoLiveParamsOut* default_go_live_params)
    {
        bool success = sdk_controller_->VideoQualityManagerInitialize(param, default_go_live_params);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerInitialize] VideoQualityManagerInitialize failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryCameraRecommendedParams(const VideoQualityManagerQueryCameraRecommendedParamsRequest& request, VideoQualityManagerQueryCameraRecommendedParamsResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryCameraRecommendedParams]";
        bool success = sdk_controller_->VideoQualityManagerQueryCameraRecommendedParams(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryCameraRecommendedParams] VideoQualityManagerQueryCameraRecommendedParams failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& request, VideoQualityManagerQueryCameraBestParamsForTargetResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget]";
        bool success = sdk_controller_->VideoQualityManagerQueryCameraBestParamsForTarget(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget] VideoQualityManagerQueryCameraBestParamsForTarget failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& request, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams]";
        bool success = sdk_controller_->VideoQualityManagerQueryGoLiveRecommendedParams(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams] VideoQualityManagerQueryGoLiveRecommendedParams failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryManuallySelectedResult(const VideoQualityManagerQueryManuallySelectedResultRequest& request, VideoQualityManagerQueryManuallySelectedResultResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryManuallySelectedResult]";
        bool success = sdk_controller_->VideoQualityManagerQueryManuallySelectedResult(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryManuallySelectedResult] VideoQualityManagerQueryManuallySelectedResult failed";
        return success;
    }

	bool MediaMgr::ReconfigVideoOutput(const VideoOutputParamsRequest& request, VideoOutputParamsResponse* response)
	{
		LOG(INFO) << "[MediaMgr::ReconfigVideoOutput]";
		bool success = sdk_controller_->ReconfigVideoOutput(request, response);
		if (!success)
			LOG(ERROR) << "[MediaMgr::ReconfigVideoOutput] ReconfigVideoOutput failed";
		return success;
	}

	bool MediaMgr::FallbackVideoEncoder(const FallbackVideoEncoderParamsRequest& request)
	{
		LOG(INFO) << "[MediaMgr::FallbackVideoEncoder]";
		bool success = sdk_controller_->FallbackVideoEncoder(request);
		if (!success)
			LOG(ERROR) << "[MediaMgr::ReconfigVideoOutput] ReconfigVideoOutput failed";
		return success;
	}

	bool MediaMgr::SetPreprocessDefaultSize(const std::string& visual_id, int cx, int cy)
	{
		bool success = sdk_controller_->SetPreprocessDefaultSize(visual_id, cx, cy);
		if (!success)
			LOG(ERROR) << "[MediaMgr::SetPreprocessDefaultSize] SetPreprocessDefaultSize failed";
		return success;
	}

	bool MediaMgr::RemovePreprocessDefaultSize(const std::string& visual_id)
	{
		bool success = sdk_controller_->RemovePreprocessDefaultSize(visual_id);
		if (!success)
			LOG(ERROR) << "[MediaMgr::RemovePreprocessDefaultSize] RemovePreprocessDefaultSize failed";
		return success;
	}

    bool MediaMgr::StartAdaptiveGearStrategyReport(const std::string& stream_id, const std::string& abr_config)
    {
        LOG(INFO) << "[MediaMgr::StartAdaptiveGearStrategyReport]";
        bool success = sdk_controller_->StartAdaptiveGearStrategyReport(stream_id, abr_config);
        if (!success)
            LOG(ERROR) << "[MediaMgr::StartAdaptiveGearStrategyReport] StartAdaptiveGearStrategyReport failed";
        return success;
    }

    void MediaMgr::SetEnableTransition(bool enable_transition)
    {
        enable_transition_ = enable_transition;
    }

    bool MediaMgr::GetEnableTransition()
    {
        return enable_transition_;
    }

    void MediaMgr::StartTimer(void* param)
    {
        StopTimer();

        ITimerMgr* mgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
        uint64_t   id = timer_id_counter_++;
        uint64_t   ret_id = mgr->SetTimer(id, 1000, this);
        if (ret_id != 0)
        {
            timer_id_ = ret_id;
        }
    }

    void MediaMgr::StopTimer()
    {
        if (timer_id_ != 0)
        {
            ITimerMgr* mgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
            mgr->KillTimer(timer_id_);
            timer_id_ = 0;
        }
    }

    void MediaMgr::OnTimer()
    {
        auto now = std::chrono::steady_clock::now();
        for (auto it : effect_ctx_map_)
        {
            auto effect_ctx = it.second;
            if (effect_ctx && !effect_ctx->enable_effect && !effect_ctx->enable_effect_operation_done)
            {
                const auto interval = now - effect_ctx->last_enable_effect_tp;
                if (interval >= std::chrono::seconds(EFFECT_ENABLE_TIME))
                {
                    LOG(WARNING) << "[MediaMgr::OnTimer] interval to 30s";
                    effect_ctx->enable_effect_operation_done = true;
                    sdk_controller_->EffectEnable(it.first, effect_ctx->enable_effect);
                }
            }
        }
    }

    void MediaMgr::HandleTimer(UINT64 id)
    {
        auto handler = [this]() {
            OnTimer();
         };
        auto task = task_mgr_.CreateThreadTask(handler);
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_IMMEDIATE);
    }

    void MediaMgr::InitDefaultCanvasContext(VideoModelContext* ctx)
    {
        ctx->video_model = 0;

        ctx->video_param.output_size_width = 800;
        ctx->video_param.output_size_height = 600;
        ctx->video_param.focus_nv12 = false;
        ctx->video_param.fps = 60;

        ctx->video_param.color_space = COLOR_SPACE::COLOR_SPACE_BT709;
        ctx->video_param.color_transfer = COLOR_TRANSFER::COLOR_TRANSFER_BT709;
        ctx->video_param.color_range = COLOR_RANGE::COLOR_RANGE_FULL;

        ctx->preview_x = 0;
        ctx->preview_y = 0;
        ctx->preview_width = 1;
        ctx->preview_height = 1;

        ctx->layout_preview_x = 0;
        ctx->layout_preview_y = 0;
        ctx->layout_preview_width = 1;
        ctx->layout_preview_height = 1;
    }

    bool MediaMgr::SetVideoParam(sdk_helper::MediaSDKControllerImplV2* sdk, VideoModelContextPtr video_model_ctx)
    {
        if (video_model_ctx->video_param.output_size_width == 0 || video_model_ctx->video_param.output_size_height == 0)
        {
            LOG(ERROR) << "[MediaMgr::SetVideoParam] output_size_width == 0 or output_size_height == 0";
            return false;
        }

        SetVideoSettingParam video_setting_param;
        video_setting_param.track_id_list.push_back(video_model_ctx->video_model);
        video_setting_param.video_param_list.push_back(video_model_ctx->video_param);
        bool success = sdk_controller_->SetVideoSetting(video_setting_param);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetVideoParam] SetVideoSetting failed";

        // Update VideoModel FPS, MeaningWhile Update Game Capture FPS
        const auto& metas = SourceMgr::GetInstance()->GetCompositeMetas();
        for (const auto& meta : metas)
        {
            if (video_model_ctx->video_param.fps > 1.0f && meta.second.primaryType == VISUAL_GAME && SourceMgr::GetInstance()->CheckSourceExist(meta.first))
            {
                std::string source_id = "";
                Util::NumToString(meta.first, &source_id);
                GAME_SOURCE game = std::get<GAME_SOURCE>(meta.second.source);
                sdk_controller_->GameSourceLimitCaptureFPS(source_id, game.enableLimit, video_model_ctx->video_param.fps);
            }
        }

        return success;
    }

    bool MediaMgr::SetPreviewPosition(sdk_helper::MediaSDKControllerImplV2* sdk, VideoModelContextPtr video_model_ctx)
    {
        if (sdk == nullptr || video_model_ctx == nullptr)
        {
            LOG(ERROR) << "[MediaMgr::SetPreviewPosition] sdk or video_model_ctx is nullptr";
            return false;
        }

        LOG(INFO) << "[MediaMgr::SetPreviewPosition] preview=[" << video_model_ctx->preview_x << "," << video_model_ctx->preview_y << "," << video_model_ctx->preview_width << "," << video_model_ctx->preview_height << "], layout_preview=[" << video_model_ctx->layout_preview_x << "," << video_model_ctx->layout_preview_y << "," << video_model_ctx->layout_preview_width << "," << video_model_ctx->layout_preview_height << "]";

        Gdiplus::RectF rect{};
        rect.X = video_model_ctx->preview_x;
        rect.Y = video_model_ctx->preview_y;
        rect.Width = video_model_ctx->preview_width;
        rect.Height = video_model_ctx->preview_height;

        Gdiplus::RectF layout_rect{};
        if (video_model_ctx->layout_preview_width == 0 && video_model_ctx->layout_preview_height == 0)
        {
            layout_rect = rect;
        }
        else
        {
            layout_rect.X = video_model_ctx->layout_preview_x;
            layout_rect.Y = video_model_ctx->layout_preview_y;
            layout_rect.Width = video_model_ctx->layout_preview_width;
            layout_rect.Height = video_model_ctx->layout_preview_height;
        }

        bool success = sdk->PreviewWindowSetPosition(video_model_ctx->video_model, rect, layout_rect);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetPreviewPosition] PreviewWindowSetPosition failed";

        return success;
    }

    media_mgr::VideoModelContextPtr MediaMgr::GetModelCtx(const uint32_t video_model)
    {
        std::unique_lock<std::mutex> lock(model_ctx_map_mutex_);
        auto found = model_ctx_map_.find(video_model);
        if (found != model_ctx_map_.end())
            return found->second;
        return nullptr;
    }

    bool MediaMgr::EnableEffect(const std::string& layer_id, bool enable)
    {
        std::unique_lock<std::mutex> lock(effect_ctx_map_mutex_);
        auto                         found = effect_ctx_map_.find(layer_id);

        bool success = false;
        EffectContextPtr effect_ctx = nullptr;
        if (found != effect_ctx_map_.end())
        {
            effect_ctx = found->second;
        }
        else
        {
            effect_ctx = std::make_shared<EffectContext>();
        }

        if (enable)
        {
            success = sdk_controller_->EffectEnable(layer_id, enable);
            effect_ctx->enable_effect = enable;
            effect_ctx->enable_effect_operation_done = true;
            effect_ctx->last_enable_effect_tp = std::chrono::steady_clock::now();
        }
        else
        {
            if (enable != effect_ctx->enable_effect)
            {
                effect_ctx->enable_effect = enable;
                effect_ctx->enable_effect_operation_done = false;
            }
            success = true;
        }
        
        effect_ctx_map_[layer_id] = effect_ctx;
        return success;
    }

    void MediaMgr::OnLayerCreated(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            CreateVisualEvent event{};
            event.visualID = visual_id;
            event.success = success;
            event.errCode = static_cast<INT32>(error_code);
            event.type = type;
            event.reason = failed_reason;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnLayerDeleted(const std::string& visual_id)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            DeleteVisualEvent event{};
            event.visualID = visual_id;
            event.success = true;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnLayerFallback(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason, bool fallbackToPlaceHolder /*= false*/)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            FallbackVisualEvent event{};
            event.visualID = visual_id;
            event.success = success;
            event.errCode = static_cast<INT32>(error_code);
            event.type = type;
            event.fallbackToPlaceHolder = fallbackToPlaceHolder;
            event.reason = failed_reason;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnFullScreenDetectorCallback(const std::string& detector_id, const bool found_target, const FullScreenDetector::WindowInfo& info)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            FullScreenDetectorEvent event{};
            event.detectorID = detector_id;
            event.foundTarget = found_target;
            event.className = info.class_name;
            event.exeName = info.exe_path;
            event.pid = info.pid;
            event.winID = info.win_id;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnThreadMonitorEvent(MONITOR_THREAD_ID thread_id, MONITOR_THREAD_EVENT_TYPE event_type)
    {
        IThreadMonitor* threadMonitor = (IThreadMonitor*)g_cief->QueryInterface(LSINAME_THREADMONITOR);
        if (!threadMonitor)
            return;

        std::string thread_name = "";
        bool        success = true;

        bool is_render_thread = false;

        switch (thread_id)
        {
        case MONITOR_THREAD_ID::MONITOR_THREAD_ID_RENDER_THREAD:
            thread_name = THREADNAME_RENDER;
            is_render_thread = true;
            break;
        default:
            success = false;
        }

        if (!success)
            return;

        switch (event_type)
        {
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_START:
            threadMonitor->OnThreadBegin(thread_name.c_str());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->EnableDetect(true);
            break;
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_TICK:
            threadMonitor->OnFrameBegin(threadMonitor->GetContext());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->OnTick();
            break;
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_END:
            threadMonitor->OnThreadEnd(threadMonitor->GetContext());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->EnableDetect(false);
            break;
        default:
            break;
        }
    }
} // namespace media_mgr