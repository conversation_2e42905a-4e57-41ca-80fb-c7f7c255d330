#pragma once

#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_canvas.pb.h"
#include "ls_base.pb.h"

namespace LS
{

class LSCanvas : public PB::Module
{
private:
    virtual RequestList GetRequestHandlers() const override;

public:
    struct CreateCanvas : public PB::RequestHandler<ls_canvas::CreateCanvas>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct DestoryCanvas : public PB::RequestHandler<ls_canvas::DestoryCanvas>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetCanvasInfo : public PB::RequestHandler<ls_canvas::SetCanvasInfo>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetCanvasInfo : public PB::RequestHandler<ls_canvas::GetCanvasInfo>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct AddLayers : public PB::RequestHandler<ls_canvas::AddLayers>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveLayers : public PB::RequestHandler<ls_canvas::RemoveLayers>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct AddFilter : public PB::RequestHandler<ls_canvas::AddFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveFilter : public PB::RequestHandler<ls_canvas::RemoveFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct VibeTriggerEffect : public PB::RequestHandler<ls_canvas::VibeTriggerEffect>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };
};
} // namespace MediaSDK