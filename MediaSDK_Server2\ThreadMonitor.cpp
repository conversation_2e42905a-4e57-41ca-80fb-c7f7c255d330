﻿#include "ThreadMonitor.h"
#include "LogDumpMgr.h"
#include "WatchdogClient.h"

void ThreadMonitor::CheckThread()
{
	UINT64 curTick = ::GetTickCount64();
	//Check frozen
	if (::IsDebuggerPresent())
		return;
	DWORD tid = 0;
	std::string tname;
	UINT64 reportTick = 0;
	bool report = false;

	UINT64 lastCheckTime = m_lastCheckTime;
	m_lastCheckTime = curTick;

	// Prevent false hang detection caused by system sleep
	{
		if (lastCheckTime != 0 && curTick - lastCheckTime >= 5000)
		{
			LOG(INFO) << "[ThreadMonitor] The system recovers from sleep";
			m_recoverTime = curTick;
			return;
		}

		if (curTick - m_recoverTime < 3000)
			return;
	}

	m_lock.Lock();
	for (auto it = m_threadInfos.rbegin(); it != m_threadInfos.rend(); it++)
	{
		THREADMONITORINFO* info = it->second;
		if (info->frameBegin)
		{
			UINT64 frameBeginTick = _InterlockedOr64((LONG64*)&info->frameBeginTick, 0);
			if (frameBeginTick && curTick >= frameBeginTick && curTick - frameBeginTick >= m_frozenTime)
			{
				info->reportTime++;
				if (!m_reported)//Only report first freeze
				{
					m_reported = true;
					report = true;
					tid = it->first;
					tname = info->name;
					reportTick = frameBeginTick;
				}
			}
		}
	}
	m_lock.Unlock();

	if (report)
	{
		LogDumpMgr::GetInstance()->ReportFrozen(tid, tname.c_str(), reportTick);
	}
}

void* ThreadMonitor::OnThreadBegin(const char* name)
{
	THREADMONITORINFO* info = new THREADMONITORINFO;
	info->tid = ::GetCurrentThreadId();
	if(name)
		info->name = name;
	info->threadBeginTick = ::GetTickCount64();

	m_lock.Lock();
	auto ret = m_threadInfos.insert(std::pair<DWORD, THREADMONITORINFO*>(info->tid, info));
	if (!ret.second)
	{
		delete info;
		info = 0;
		LOG(INFO) << "[ThreadMonitor] OnThreadBegin find the same tid";
	}
	m_lock.Unlock();

	return info;
}

void ThreadMonitor::OnThreadEnd(void* context)
{
	if (!context)
		return;
	DWORD tid = ::GetCurrentThreadId();
	m_lock.Lock();
	m_threadInfos.erase(tid);
	m_lock.Unlock();
}

void ThreadMonitor::OnFrameBegin(void* context)
{
	if (!context)
		return;
	THREADMONITORINFO* info = (THREADMONITORINFO*)context;
	::InterlockedExchange64((LONGLONG*)&info->frameBeginTick, ::GetTickCount64());
	info->frameBegin = true;
	//Check fps
	UINT64 secondIndex = (info->frameBeginTick - info->threadBeginTick) / 1000;
	if (secondIndex != info->secondCounter)
	{
		info->fps = info->fpsCounter;
		info->fpsCounter = 0;
		info->secondCounter = secondIndex;
		//Report fps
	}
	//Watchdog heartbeat
	WatchdogClient::GetInstance().PostHeartBeat(info->name);
}

void ThreadMonitor::OnFrameEnd(void* context)
{
	if (!context)
		return;
	THREADMONITORINFO* info = (THREADMONITORINFO*)context;
	info->frameEndTick = ::GetTickCount64();
	info->frameBegin = false;
	info->fpsCounter++;
	//Check longtime frame
	if (::IsDebuggerPresent())
	{
		info->ticks.clear();
		return;
	}
		
	if (info->frameEndTick - info->frameBeginTick >= m_respondTime)//卡顿要一直报
	{
		//Report Timeout
		LOG(WARNING) << StringPrintf("ThreadMonitor::OnFrameEnd more than %dms. tid=%d time=%dms", m_respondTime, info->tid, (info->frameEndTick - info->frameBeginTick));
	}
	if (info->frameEndTick - info->frameBeginTick >= 50)
	{
		LOG(WARNING) << StringPrintf("ThreadMonitor::OnFrameEnd more than 50ms. tid=%d time=%dms", info->tid, (info->frameEndTick - info->frameBeginTick));
		UINT64 lastTick = info->frameBeginTick;
		while (info->ticks.size())
		{
			TICKINFO ti = info->ticks.front();
			info->ticks.pop_front();
			LOG(WARNING) << StringPrintf("ThreadMonitor::OnFrameEnd %s=%dms", ti.desc.c_str(), (ti.tick - lastTick));
			lastTick = ti.tick;
		}
	}
	info->ticks.clear();
}

void ThreadMonitor::OnFrameTick(void* context, const char* desc)
{
	if (!context)
		return;
	if (::IsDebuggerPresent())
		return;
	THREADMONITORINFO* info = (THREADMONITORINFO*)context;
	TICKINFO ti;
	ti.tick = ::GetTickCount64();
	if (desc)
		ti.desc = desc;
	info->ticks.push_back(ti);
}

void* ThreadMonitor::GetContext()
{
	void* context = 0;
	DWORD tid = ::GetCurrentThreadId();
	m_lock.Lock();
	auto it = m_threadInfos.find(tid);
	if (it != m_threadInfos.end())
		context = it->second;
	m_lock.Unlock();
	return context;
}

void ThreadMonitor::SetFrozenTime(UINT64 timeMS)
{
	if (timeMS < 15ULL * 1000)
		timeMS = 15ULL * 1000;
	m_frozenTime = timeMS;
}
