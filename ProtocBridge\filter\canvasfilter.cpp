#include "stdafx.h"
#include "canvasfilter.h"

namespace LS
{
CanvasFilter::RequestList CanvasFilter::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<CanvasFilter::Create>());
    list.push_back(std::make_unique<CanvasFilter::Remove>());
    list.push_back(std::make_unique<CanvasFilter::SetCanvasFilter>());
    list.push_back(std::make_unique<CanvasFilter::GetCanvasFilter>());
    return list;
}

void CanvasFilter::SetCanvasFilterInfo(CANVAS_FILTER& canvasFilterInfo, const ls_canvasfilter::CanvasFilter& canvas_filter, UINT64* cmd /* = NULL */)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return;

    canvasFilterInfo.filterType = static_cast<CANVAS_FILTER_TYPE>(canvas_filter.filter_type());
    if (canvas_filter.filter_type() == CANVAS_FILTER_TRANSITION && canvas_filter.has_transition_filter())
    {
        const ls_canvasfilter::TransitionFilter& transition_filter = canvas_filter.transition_filter();
        TransitionFilter&                        transitionFilterInfo = canvasFilterInfo.transitionFilter;
        transitionFilterInfo.transitionType = static_cast<TRANSITION_TYPE>(transition_filter.transition_type());
        if (transition_filter.has_duration_ms())
        {
            transitionFilterInfo.durationMs = transition_filter.duration_ms();
        }

        if (transitionFilterInfo.transitionType == TRANSITION_TYPE_SLIDE && transition_filter.has_slide_transition())
        {
            const ls_canvasfilter::SlideTransition& slide_filter = transition_filter.slide_transition();
            transitionFilterInfo.slideTransition.direction = static_cast<TRANSITION_DIRECTION>(slide_filter.direction());
            if (slide_filter.has_function())
            {
                transitionFilterInfo.slideTransition.func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(slide_filter.function());
            }
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_SWIPE && transition_filter.has_swipe_transition())
        {
            const ls_canvasfilter::SwipeTransition& swipe_filter = transition_filter.swipe_transition();
            transitionFilterInfo.swipeTransition.direction = static_cast<TRANSITION_DIRECTION>(swipe_filter.direction());
            transitionFilterInfo.swipeTransition.swipeType = static_cast<SWIPE_TYPE>(swipe_filter.swipe_type());
            if (swipe_filter.has_function())
            {
                transitionFilterInfo.swipeTransition.func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(swipe_filter.function());
            }
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_FADE && transition_filter.has_fade_transition())
        {
            const ls_canvasfilter::FadeTransition& fade_filter = transition_filter.fade_transition();
            if (fade_filter.has_function())
            {
                transitionFilterInfo.fadeTransition.func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(fade_filter.function());
            }
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_FADE2COLOR && transition_filter.has_fade2color_transition())
        {
            const ls_canvasfilter::Fade2ColorTransition& fade2color_filter = transition_filter.fade2color_transition();
            transitionFilterInfo.fade2ColorTransition.color = fade2color_filter.color();
            transitionFilterInfo.fade2ColorTransition.middleProgress = fade2color_filter.middle_progress();
            if (fade2color_filter.has_function())
            {
                transitionFilterInfo.fade2ColorTransition.func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(fade2color_filter.function());
            }
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_CARTOON && transition_filter.has_cartoon_transition())
        {
            const ls_canvasfilter::CartoonTransition& cartoon_transition = transition_filter.cartoon_transition();
            transitionFilterInfo.cartoonTransition.middleProgress = cartoon_transition.middle_progress();
            transitionFilterInfo.cartoonTransition.filePath = cartoon_transition.file_path();
            transitionFilterInfo.durationMs = 0;
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_LUMINANCE_WIDE && transition_filter.has_luminance_wide_transition())
        {
            const ls_canvasfilter::LuminanceWideTransition& luminance_wide_filter = transition_filter.luminance_wide_transition();
            transitionFilterInfo.luminanceWideTransition.filePath = luminance_wide_filter.file_path();
            transitionFilterInfo.luminanceWideTransition.invert = luminance_wide_filter.invert();
            transitionFilterInfo.luminanceWideTransition.softness = luminance_wide_filter.softness();
            if (luminance_wide_filter.has_function())
            {
                transitionFilterInfo.luminanceWideTransition.func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(luminance_wide_filter.function());
            }
        }
        else if (transitionFilterInfo.transitionType == TRANSITION_TYPE_MOVE && transition_filter.has_move_transition())
        {
            const ls_canvasfilter::MoveTransition& move_transition = transition_filter.move_transition();
            transitionFilterInfo.moveTransition.middleProgress = move_transition.middle_progress();
            transitionFilterInfo.moveTransition.matchedLayerIDs.clear();
            for (int i = 0; i < move_transition.matched_layer_ids_size(); ++i)
            {
                ls_canvasfilter::MatchedLayerID matched_layerid = move_transition.matched_layer_ids(i);
                transitionFilterInfo.moveTransition.matchedLayerIDs.push_back({matched_layerid.source_layer_id(), matched_layerid.target_layer_id()});
            }

            if (move_transition.has_move_transition_info())
            {
                ls_canvasfilter::MoveTransitionInfo move_transition_info = move_transition.move_transition_info();
                transitionFilterInfo.moveTransition.moveTransitionInfo.moveType = static_cast<TRANSITION_MOVE_TYPE>(move_transition_info.move_type());
                if (move_transition_info.has_move_in_from_position())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveInFromPos.X = move_transition_info.move_in_from_position().x();
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveInFromPos.Y = move_transition_info.move_in_from_position().y();
                }
                if (move_transition_info.has_move_out_to_position())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveOut2Pos.X = move_transition_info.move_out_to_position().x();
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveOut2Pos.Y = move_transition_info.move_out_to_position().y();
                }
                if (move_transition_info.has_move_in_from_direction())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveInFromDirection = static_cast<TRANSITION_DIRECTION>(move_transition_info.move_in_from_direction());
                }
                if (move_transition_info.has_move_out_to_direction())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveOut2Direction = static_cast<TRANSITION_DIRECTION>(move_transition_info.move_out_to_direction());
                }
                if (move_transition_info.has_move_to_function())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.move2Func = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(move_transition_info.move_to_function());
                }
                if (move_transition_info.has_move_in_function())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveInFunc = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(move_transition_info.move_in_function());
                }
                if (move_transition_info.has_move_out_function())
                {
                    transitionFilterInfo.moveTransition.moveTransitionInfo.moveOutFunc = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(move_transition_info.move_out_function());
                }
            }
        }
    }
    else if (canvas_filter.filter_type() == CANVAS_FILTER_COLOR_ADJUST && canvas_filter.has_color_adjust_filter())
    {
        const ls_canvasfilter::ColorAdjustFilter& color_adjust_filter = canvas_filter.color_adjust_filter();
        ColorAdjustFilter&                        colorAdjustFilter = canvasFilterInfo.colorAdjustFilter;
        if (color_adjust_filter.has_brightness() &&
            std::abs(colorAdjustFilter.brightness - color_adjust_filter.brightness()) > EPS)
        {
            colorAdjustFilter.brightness = color_adjust_filter.brightness();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_BRIGHTNESS;
        }
        if (color_adjust_filter.has_contrast() &&
            std::abs(colorAdjustFilter.contrast - color_adjust_filter.contrast()) > EPS)
        {
            colorAdjustFilter.contrast = color_adjust_filter.contrast();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_CONTRAST;
        }
        if (color_adjust_filter.has_gamma() &&
            std::abs(colorAdjustFilter.gamma - color_adjust_filter.gamma()) > EPS)
        {
            colorAdjustFilter.gamma = color_adjust_filter.gamma();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_GAMMA;
        }
        if (color_adjust_filter.has_hue_shift() &&
            std::abs(colorAdjustFilter.hueShift - color_adjust_filter.hue_shift()) > EPS)
        {
            colorAdjustFilter.hueShift = color_adjust_filter.hue_shift();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_HUE_SHIFT;
        }
        if (color_adjust_filter.has_opacity() &&
            std::abs(colorAdjustFilter.opacity - color_adjust_filter.opacity()) > EPS)
        {
            colorAdjustFilter.opacity = color_adjust_filter.opacity();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_OPACITY;
        }
        if (color_adjust_filter.has_saturation() &&
            std::abs(colorAdjustFilter.saturation - color_adjust_filter.saturation()) > EPS)
        {
            colorAdjustFilter.saturation = color_adjust_filter.saturation();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_SATURATION;
        }
        if (color_adjust_filter.has_add_color() &&
            colorAdjustFilter.addColor != color_adjust_filter.add_color())
        {
            colorAdjustFilter.addColor = color_adjust_filter.add_color();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_ADD_COLOR;
        }
        if (color_adjust_filter.has_mul_color() &&
            colorAdjustFilter.mulColor != color_adjust_filter.mul_color())
        {
            colorAdjustFilter.mulColor = color_adjust_filter.mul_color();
            if (cmd)
                *cmd |= FILTER_CONTROL_SET_COLOR_MUL_COLOR;
        }
    }
}

bool CanvasFilter::Create::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    FILTER filterInfo{};
    filterInfo.type = FILTER_CANVAS;

    CANVAS_FILTER canvasFilterInfo{};
    if (req.has_canvas_filter())
    {
        SetCanvasFilterInfo(canvasFilterInfo, req.canvas_filter());
        if (req.canvas_filter().has_enable())
        {
            filterInfo.enable = req.canvas_filter().enable();
        }
    }
    filterInfo.filter = canvasFilterInfo;

    UINT64      filterID = controller->CreateFilter(&filterInfo);
    std::string filter_id = "";
    Util::NumToString(filterID, &filter_id);
    rsp.set_filter_id(filter_id);

    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[TransitionFilter::Create] CreateFilter failed, filterID: " << filterID;
        return false;
    }
    return true;
}

bool CanvasFilter::Remove::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::Remove] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->DeleteFilter(filterID);
    return true;
}

bool CanvasFilter::SetCanvasFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::SetVisualFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    if (filterInfo.type == FILTER_CANVAS)
    {
        UINT64        cmd = FILTER_CONTROL_NONE;
        CANVAS_FILTER canvasFilterInfo = std::get<CANVAS_FILTER>(filterInfo.filter);
        if (req.has_canvas_filter())
        {
            SetCanvasFilterInfo(canvasFilterInfo, req.canvas_filter(), &cmd);
            if (req.canvas_filter().has_enable())
            {
                filterInfo.enable = req.canvas_filter().enable();
                cmd |= FILTER_CONTROL_SET_FILTER_ENABLE;
            }
        }

        filterInfo.filter = canvasFilterInfo;
        controller->ControlFilter(filterID, filterInfo, static_cast<FILTER_CONTROL_CMD>(cmd));
    }

    return true;
}

bool CanvasFilter::GetCanvasFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::GetVisualFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    CANVAS_FILTER                 canvasFilterInfo = std::get<CANVAS_FILTER>(filterInfo.filter);
    ls_canvasfilter::CanvasFilter canvas_filter{};

    if (canvasFilterInfo.filterType == CANVAS_FILTER_TRANSITION)
    {
        // TODO: @xuwanhui
    }
    else if (canvasFilterInfo.filterType == CANVAS_FILTER_COLOR_ADJUST)
    {
        // TODO: @xuwanhui
    }

    rsp.mutable_canvas_filter()->CopyFrom(canvas_filter);
    rsp.set_type(static_cast<ls_canvasfilter::CANVAS_FILTER_TYPE>(canvasFilterInfo.filterType));
    return true;
}

} // namespace LS