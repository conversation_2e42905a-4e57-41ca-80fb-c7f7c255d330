#pragma once
#include <future>
#include <limits>
#include "mediasdk_v2_header/mediasdk_defines_audio.h"
#include "mediasdk_v2_header/mediasdk_callback_defines.h"
#include "mediasdk_v2_header/mediasdk_api.h"
#include "mediasdk_v2_header/mediasdk_api_visual.h"
#include "mediasdk_v2_header/mediasdk_api_alloc.h"
#include "mediasdk_v2_header/mediasdk_rtc_event_observer.h"
#include "mediasdk_v2_header/mediasdk_effect_platform_event_observer.h"
#include "mediasdk_v2_header/mediasdk_api_rtc.h"
#include "mediasdk_v2_header/mediasdk_defines_canvas.h"
#include "mediasdk_v2_header/mediasdk_canvas_item_event_observer.h"
#include "mediasdk_v2_header/mediasdk_api_canvas.h"
#include "mediasdk_v2_header/hook_api/hook_api.h"
#include "mediasdk_v2_header/hook_api/custom_audio_input_delegate.h"

namespace mediasdk
{
	extern "C" typedef void* (__stdcall* MediaSDKV2_AllocBuffer)(uint32_t size);
	extern "C" typedef void(__stdcall* MediaSDKV2_FreeBuffer)(void* p);
	extern "C" typedef const char* (__stdcall* MediaSDKV2_MediaSDKVersion)();
	extern "C" typedef void(__stdcall* MediaSDKV2_SetLogHandlerFunc)(LogHandlerFunc func);
    extern "C" typedef void(__stdcall* MediaSDKV2_Initialize)(MediaSDKGlobalEventObserver* observer, const char* json_params, hook_api::HookApi* hook_api, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateGlobalConfig)(const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_Uninitialize)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RegisterWindowEventObserver)(MediaSDKWindowEventObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterWindowEventObserver)(MediaSDKWindowEventObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RegisterAudioObserver)(MediaSDKAudioStatusObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterAudioObserver)(MediaSDKAudioStatusObserver* observer, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetPreviewPosition)(uint32_t sink_id, const MSRect& rect, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAndClipPreviewPosition)(uint32_t sink_id, const MSRect& pos_rect, const MSClip& clip_rect, Closure closure);

	// Origin Transition
    // video_model
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateModel)(uint32_t video_model_id, const ModelParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVideoModelActive)(uint32_t video_model_id, bool enable, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetVideoModelActive)(uint32_t video_model_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_IsPreviewEnableWithVideoModelId)(uint32_t video_model_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_EnablePreviewWithVideoModelId)(uint32_t video_model_id, bool enable, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetScaleForVideoModel)(uint32_t video_model_id, float scale, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemByOrderIDSOnVideoModel)(uint32_t video_model_id, const MediaSDKStringArray& array, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemOrderIDSOnVideoModel)(uint32_t video_model_id, Closure closure);

    // canvas
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateCanvas)(const char* canvas_id, uint32_t video_model_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_DestroyCanvas)(const char* canvas_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCurrentCanvas)(uint32_t video_model_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCurrentCanvas)(uint32_t video_model_id, const char* canvas_id, const char* transition_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemByOrderIDS)(const char* canvas_id, const MediaSDKStringArray& array, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemOrderIDS)(const char* canvas_id, Closure closure);

	// transition
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateTransition)(const char* transition_id, const CreateTransitionParams& transition_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyTransition)(const char* transition_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetTransitionProperty)(const char* transition_id, const char* property, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_AmbientEffect)(const MediaSDKString& param, Closure closure);

    // canvas_item
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateCanvasItem)(const char* canvas_item_id, const char* canvas_id, const char* visual_id, CreateCanvasItemParams& canvas_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_DestroyCanvasItem)(const char* canvas_item_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateCanvasItemWithFilter)(const char* canvas_item_id, const char* canvas_id, const char* visual_id, const CreateCanvasItemParams& params, const char* filter_id, const CreateVisualFilterParams& filter_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CanvasItemChangeVisual)(const char* canvas_item_id, const char* visual_id, const MSTransform* new_transform, Closure closure);

	extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemIdsFromVisual)(const char* visual_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCurrentCanvasItem)(const char* canvas_id, const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCurrentCanvasItem)(const char* canvas_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetCurrentCanvasItemOnVideoModel)(uint32_t video_mode_id, const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCurrentCanvasItemOnVideoModel)(uint32_t video_mode_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetVisualFromCanvasItem)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemNeedDrawBorder)(const char* canvas_item_id, bool border, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemAlwaysTop)(const char* canvas_item_id, bool always_top, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemAvoidOutput)(const char* canvas_item_id, bool avoid, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_BeginCanvasItemClip)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemFlipH)(const char* canvas_item_id, bool flip, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemFlipH)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemFlipV)(const char* canvas_item_id, bool flip, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemFlipV)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemMoveRange)(const char* canvas_item_id, const MSClipF& clip, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemMoveRange)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemRotate)(const char* canvas_item_id, float rotate, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemRotate)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemHighlight)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemScale)(const char* canvas_item_id, const MSScaleF& scale, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemScale)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemMinScale)(const char* canvas_item_id, const MSScaleF& scale, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemMinScale)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemMaxScale)(const char* canvas_item_id, const MSScaleF& scale, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemMaxScale)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_LockCanvasItem)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_UnLockCanvasItem)(const char* canvas_item_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemAlwaysTop)(const char* canvas_item_id, bool always_top, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_IsCanvasItemLocked)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemEditable)(const char* canvas_item_id, bool editable, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemEditable)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemTranslate)(const char* canvas_item_id, const MSTranslateF& translate, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemTranslate)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemSizeF)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemClip)(const char* canvas_item_id, const MSClipF& clip, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemClip)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_IsCanvasItemClip)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_EnableCanvasItemClip)(const char* canvas_item_id, bool enable, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemTransform)(const char* canvas_item_id, const MSTransform& transform, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemTransform)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_MoveCanvasItemZOrder)(const char* canvas_item_id, MovePostion pos, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemVisible)(const char* canvas_item_id, bool visable, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemVisible)(const char* canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_CanvasItemSaveAsFile)(const char* canvas_item_id, const char* file_path, ImageFileFormat format, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreprocess)(const char* canvas_item_id, bool enable, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_GetCanvasItemPreprocess)(const char* canvas_item_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemSourceClip)(const char* canvas_item_id, const MSClipF& clip, Closure closure);

    extern "C" typedef void(__stdcall* MediaSDKV2_RegisterCanvasEventObserver)(MediaSDKCanvasEventObserver* observer, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterCanvasEventObserver)(MediaSDKCanvasEventObserver* observer, Closure closure);

    // canvas_item preview
    extern "C" typedef void(__stdcall* MediaSDKV2_StartCanvasItemPreview)(const char* canvas_item_id, const char* preview_id, uint64_t parent_wnd, MSRect region, int fill_type, bool flip_h, bool flip_v, float angle, int32_t bk_color, const WndParams& params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_StopCanvasItemPreview)(const char* preview_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreviewParams)(const char* visual_preview_id, const WndParams& params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreviewPos)(const char* preview_id, const MSRect& rect, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreviewFlipH)(const char* preview_id, bool flip_h, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreviewFlipV)(const char* preview_id, bool flip_v, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreviewRotate)(const char* preview_id, float rotate, Closure closure);

    // visual
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumVisualSource)(Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateVisual)(const char* visual_id, const CreateVisualParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyVisual)(const char* id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_ReopenVisualAndUpdateTransforms)(const char* visual_id, const char* json_params, const MediaSDKArray<ReopenParam>& reopen_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualDestroyedWhenAllReferenceRemoved)(const char* visual_id, bool destroyed, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_RegisterVisualEventObserver)(MediaSDKVisualEventObserver* observer, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterVisualEventObserver)(MediaSDKVisualEventObserver* observer, Closure closure);

	// virtual camera
	extern "C" typedef void(__stdcall* MediaSDKV2_StartVirtualCamera)(const char* canvas_item_id, MSSize size, uint32_t bg_color, VirtualCameraObjectFitMode fit_mode, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopVirtualCamera)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SwitchVirtualCamera)(const char* canvas_item_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RegisterVirtualCameraEventObserver)(MediaSDKVirtualCameraEventObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterVirtualCameraEventObserver)(MediaSDKVirtualCameraEventObserver* observer, Closure closure);

    // visual filter
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateVisualFilter)(const char* filter_id, const char* filter_name, const char* canvas_item_id, const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyVisualFilter)(const char* filter_id, const char* owner_canvas_item_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualFilterProperty)(const char* filter_id, const char* owner_canvas_item_id, const char* key, const char* json_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_VisualFilterAction)(const char* filter_id, const char* owner_canvas_item_id, const char* action, const char* action_param, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_VisualFilterSetActive)(const char* filter_id, const char* owner_canvas_item_id, bool active, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_VisualFilterGetActive)(const char* filter_id, const char* owner_canvas_item_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualFiltersPriority)(const char* owner_canvas_item_id, const MediaSDKStringArray& filter_id_array, Closure closure);

	extern "C" typedef void(__stdcall* MediaSDKV2_ShowPreviewWindow)(uint32_t sink_id, Closure  closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_HidePreviewWindow)(uint32_t sink_id, Closure  closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsAllPreviewEnable)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableAllPreview)(bool enable, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableTrack)(uint32_t sink_id, bool enbale, Closure  closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateProjector)(const char* canvas_project_id, uint64_t hwnd_parent, uint32_t sink_id, const MSRect& rect, const WndParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetUIConfig)(uint32_t sink_id, const PreviewUIConfig& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableFineTuning)(uint32_t sink_id, bool enbale, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CloseProjector)(const char* canvas_project_id, uint32_t sink_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetProjectorWndParams)(const char* canvas_project_id, const int32_t sink_id, const WndParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetProjectorPosition)(const char* canvas_project_id, const int32_t sink_id, const MSRect& rect, Closure closure);

	extern "C" typedef void(__stdcall* MediaSDKV2_GetVideoFileFrameInfo)(const char* path, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RemoveModel)(uint32_t sink_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateModelFPS)(uint32_t sink_id, float fps, Closure  closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateModelOutputSize)(uint32_t sink_id, MSSize size, Closure  closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateModelColorSpaceAndVideoRange)(uint32_t sink_id, ColorSpace cs, VideoRange vr, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumEncoderSource)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_MockRenderHung)(int32_t hung_ms);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartRenderProfiler)();
	extern "C" typedef void(__stdcall* MediaSDKV2_StartCollectPerformanceMatrics)(const char* json_info);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetTTNtpMS)(int64_t ntp_ms, int64_t local_ms);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumServiceSource)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartStream)(const StreamParams& stream_params, MediaSDKStreamStatusObserver* observer);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopStream)(const char* stream_id);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartPushStream)(const StreamParams& stream_params);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopPushStream)(const StreamParams& stream_params);

	extern "C" typedef void(__stdcall* MediaSDKV2_OutputThumbnailSaveAs)(uint32_t sink_id, const char* file_path, ImageFileFormat format, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetStreamSEI)(const char* json_info);
	extern "C" typedef void(__stdcall* MediaSDKV2_ClearStreamSEI)(const char* json_info);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateStreamAbrConfig)(const char* stream_id, uint32_t up_limit, uint32_t down_limit);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsStreamInProcess)(const char* stream_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartSpeedTestStream)(const StreamParams& stream_params, MediaSDKStreamStatusObserver* observer);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopSpeedTestStream)(const char* stream_id);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetStatisticInfo)(const char* stream_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetEncoderStatisticInfo)(const char* stream_id, mediasdk::EncoderStatisticInfo& info);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetEncoderPtsByStreamId)(const char* stream_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateABConfig)(const char* json_info, Closure closure);	
	extern "C" typedef void(__stdcall* MediaSDKV2_VideoEncoderTargetBitrate)(const char* video_encoder_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetRenderInfo)(mediasdk::RenderInfo& info);
	extern "C" typedef void(__stdcall* MediaSDKV2_TestEncoderSessionCountSupported)(const char* encoder_name, uint32_t session_count, Closure closure);

	extern "C" typedef bool(__stdcall* MediaSDKV2_GetVisualFPS)(const char* id, float& fps);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualOverlay)(const char* id, const char* file_path, float scale, const char* params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetVisualOverlay)(const char* id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumVisualInput)(const char* plugin_name, const char* json_config, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DoVisualInputAction)(const char* id, const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_ReopenVisual)(const char* id, const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumVisualFormat)(const char* plugin_name, const char* device_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumAudioFormat)(const char* plugin_name, const char* device_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualInputProperty)(const char* id, const char* key, const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVisualSourceProperty)(const char* plugin_name, const char* key, const char* json, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetVisualSourceProperty)(const char* plugin_name, const char* json, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetVisualInputProperty)(const char* id, const char* key, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_PauseVisualCapture)(const char* id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsVisualCapturePause)(const char* id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_ContinueVisualCapture)(const char* id, Closure closure);
	
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateVirtualCameraProperty)(MSSize size, uint32_t bg_color, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVirtualCameraRotate)(float rotate, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVirtualCameraFlipV)(bool flip_v, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetVirtualCameraFlipH)(bool flip_h, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_ForwardWindowMessage)(int32_t sink_id, int32_t msg, uint64_t lparam, uint64_t wparam);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetVisualFrame)(const char* visual_id, int target_width, int target_height, mediasdk::MSClip clip, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetDefaultCaptureAudio)(const char* plugin_name, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetDefaultRenderAudio)(const char* plugin_name, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumCaptureAudio)(const char* plugin_name, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumRenderAudio)(const char* plugin_name, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateAudioInput)(const char* audio_input_id, const CreateAudioParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateLyraxAudioInput)(const char* audio_input_id, const CreateAudioParams& params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_CreateCustomAudioInput)(const char* audio_input_id, uint32_t track_id, mediasdk::hook_api::CustomAudioInputDelegate* delegate, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyAudioInput)(const char* audio_input_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdatePCMAudioDatas)(const char* audio_input_id, const char* pcm_audio_datas, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputParams)(const char* audio_input_id, AudioInputParams param, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioVolume)(const char* audio_input_id, float       volume, Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioVolume)(const char* audio_input_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioBalancing)(const char* audio_input_id,		float       balance,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioBalancing)(const char* audio_input_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsAudioDownmixMono)(const char* audio_input_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioDownmixMono)(const char* audio_input_id,		bool        mono,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioSyncOffset)(const char* audio_input_id,		int32_t     sync_offset,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioSyncOffset)(const char* audio_input_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioMonitorType)(const char* audio_input_id,		int32_t     monitor_type,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioMonitorType)(const char* audio_input_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInterval)(const char* audio_input_id,		int32_t     interval,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumAudioInputSource)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnumAudioInput)(const char* plugin_name, Closure closure);	
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioMute)(const char* audio_input_id,		bool        mute,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsAudioMute)(const char* audio_input_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_AddAudioInputToTrack)(const char* audio_input_id,		uint32_t    track_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RemoveAudioInputFromTrack)(const char* audio_input_id,			uint32_t track_id,			Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioInputPerformance)(const char* audio_input_id,		mediasdk::MSAudioPerformance& performance);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateLyraxEngine)(const char* json_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputReferenceId)(const char* audio_input_id, const char* ref_audio_input_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputAECOption)(const char* audio_input_id, const bool enable, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputRawDataOption)(const char* audio_input_id, const int option, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputANSOption)(const char* audio_input_id, const int level, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_EnableAudioInputEchoDetection)(const char* audio_input_id, const int interval, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioInputRenderDeviceID)(const char* audio_input_id, const char* render_device_id, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioTrackDelayMs)(uint32_t track_id,const int64_t ms);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetGlobalRenderDeviceID)(const char* device_id);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetLyraxLiveRoomId)(const char* live_room_id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateEngine)(const char* app_id,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyEngine)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RegisterRTCEventObserver)(mediasdk::MediaSDKRTCEventObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnregisterRTCEventObserver)(mediasdk::MediaSDKRTCEventObserver* observer, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetBusinessId)(const char* business_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_JoinRoom)(const char* room_id,		const char* user_id,		const char* token,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_LeaveRoom)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioProfile)(int audio_profile, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_PublishStream)(int     stream_index,		int     media_type,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnPublishStream)(int     stream_index,		int     media_type,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SubscribeStream)(const char* user_id,		int         stream_index,		int         media_type,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnSubscribeStream)(const char* user_id,		int         stream_index,		int         media_type,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableLocalAudio)(int     stream_index,		bool    enable,		int     track_id,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetPlaybackVolume)(int volume, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioPlaybackDevice)(const char* device_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioPlaybackDevice)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableAudioPropertiesReport)(int     interval,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EnableLocalVideo)(int     stream_index,		bool    enable,		int     sink_id,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateCropAndScale)(int         stream_index,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateExcludedVisuals)(int stream_index,		const char* json_params,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetCanvasItemPreprocessDefaultSize)(const char* id, const MSSize& size, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_RemoveCanvasItemPreprocessDefaultSize)(const char* id, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateStreamAbrOffset)(const char* stream_id, int offset);

	extern "C" typedef void(__stdcall* MediaSDKV2_SetRemoteVideoConfig)(const char* user_id,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SendSEIMessage)(int         stream_index,		const char* data,		int         length,		int         repeat_count,		bool        single_sei_per_frame,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartForwardStreamToRooms)(const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateForwardStreamToRooms)(const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopForwardStreamToRooms)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartLiveTranscoding)(const char* task_id,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UpdateLiveTranscoding)(const char* task_id,		const char* json_params,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StopLiveTranscoding)(const char* task_id,		Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SendRoomMessage)(const char* data, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SendUserMessage)(const char* user_id,		const char* data,		Closure     closure);	
	
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformInitialize)(const char* json_param,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformUninitialize)(Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformLoadModels)(const char* request_id,		const char* model_name,		MediaSDKStringArray* requirments,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformUpdateConfig)(const char* user_id,		const char* hardware_level,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformRegistObserver)(MediaSDKEPEventObserver* observer,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_EffectPlatformUnregistObserver)(MediaSDKEPEventObserver* observer,		Closure closure);	
	extern "C" typedef void(__stdcall* MediaSDKV2_CreateAudioFilter)(const char* audio_filter_id,		const char* audio_filter_name,		const char* audio_input_id,		const char* json_params,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_DestroyAudioFilter)(const char* audio_filter_id,		const char* audio_input_id,		Closure closure);		
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioFilterEnable)(const char* audio_filter_id,		const char* audio_input_id,		bool enable,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_IsAudioFilterEnable)(const char* audio_filter_id,		const char* audio_input_id,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_AudioFilterAction)(const char* audio_input_id,		const char* audio_filter_id,		const char* action,		const char* param,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_SetAudioFilterProperty)(const char* audio_filter_id,		const char* audio_input_id,		const char* key,		const char* value,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_GetAudioFilterProperty)(const char* audio_filter_id,		const char* audio_input_id,		const char* key,		Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_InitVQM)(const char* json_params,                                                           Closure     closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_UnInitVQM)(Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_QueryGoLiveRecommendedParams)(const char* json_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_QueryCameraRecommendedParams)(const char* json_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_QueryGoLiveManuallySelectedParams)(const char* json_params, Closure closure);
    extern "C" typedef void(__stdcall* MediaSDKV2_QueryCameraBestParamsForTarget)(const char* json_params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_ReconfigVideoOutput)(const VideoOutputParams& params, const char* reason, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_FallbackVideoEncoder)(const MediaSDKString& codec_id, const mediasdk::FallbackVideoEncoderConfigParams& params, Closure closure);
	extern "C" typedef void(__stdcall* MediaSDKV2_StartAdaptiveGearStrategyReport)(const MediaSDKString& stream_id, const MediaSDKString& abr_config);
} // namespace mediasdk

#define DECLARE_V2_MSDK_VARIABLE(name) mediasdk::MediaSDKV2_##name name = nullptr;

namespace sdk_helper
{
struct MediaSDKV2API
{
    DECLARE_V2_MSDK_VARIABLE(AllocBuffer)
    DECLARE_V2_MSDK_VARIABLE(FreeBuffer)

    DECLARE_V2_MSDK_VARIABLE(MediaSDKVersion)
    DECLARE_V2_MSDK_VARIABLE(SetLogHandlerFunc)
    DECLARE_V2_MSDK_VARIABLE(Initialize)
    DECLARE_V2_MSDK_VARIABLE(UpdateGlobalConfig)
    DECLARE_V2_MSDK_VARIABLE(Uninitialize)
    DECLARE_V2_MSDK_VARIABLE(RegisterWindowEventObserver)
    DECLARE_V2_MSDK_VARIABLE(UnregisterWindowEventObserver)
    DECLARE_V2_MSDK_VARIABLE(RegisterAudioObserver)
    DECLARE_V2_MSDK_VARIABLE(UnregisterAudioObserver)
    DECLARE_V2_MSDK_VARIABLE(SetPreviewPosition)
    DECLARE_V2_MSDK_VARIABLE(SetAndClipPreviewPosition)

	// Origin Transition
	// video_model
	DECLARE_V2_MSDK_VARIABLE(CreateModel)
	DECLARE_V2_MSDK_VARIABLE(SetVideoModelActive)
	DECLARE_V2_MSDK_VARIABLE(GetVideoModelActive)
	DECLARE_V2_MSDK_VARIABLE(IsPreviewEnableWithVideoModelId)
	DECLARE_V2_MSDK_VARIABLE(EnablePreviewWithVideoModelId)
	DECLARE_V2_MSDK_VARIABLE(SetScaleForVideoModel)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemByOrderIDSOnVideoModel)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemOrderIDSOnVideoModel)

	// canvas
	DECLARE_V2_MSDK_VARIABLE(CreateCanvas)
	DECLARE_V2_MSDK_VARIABLE(DestroyCanvas)
	DECLARE_V2_MSDK_VARIABLE(GetCurrentCanvas)
	DECLARE_V2_MSDK_VARIABLE(SetCurrentCanvas)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemByOrderIDS)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemOrderIDS)

	// canvas_item
	DECLARE_V2_MSDK_VARIABLE(CreateCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(DestroyCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(CreateCanvasItemWithFilter)
	DECLARE_V2_MSDK_VARIABLE(CanvasItemChangeVisual)
	
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemIdsFromVisual)
	DECLARE_V2_MSDK_VARIABLE(SetCurrentCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(GetCurrentCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(SetCurrentCanvasItemOnVideoModel)
	DECLARE_V2_MSDK_VARIABLE(GetCurrentCanvasItemOnVideoModel)
	DECLARE_V2_MSDK_VARIABLE(GetVisualFromCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemNeedDrawBorder)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemAlwaysTop)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemAvoidOutput)
	DECLARE_V2_MSDK_VARIABLE(BeginCanvasItemClip)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemFlipH)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemFlipH)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemFlipV)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemFlipV)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemMoveRange)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemMoveRange)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemRotate)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemRotate)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemHighlight)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemScale)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemScale)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemMinScale)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemMinScale)
    DECLARE_V2_MSDK_VARIABLE(SetCanvasItemMaxScale)
    DECLARE_V2_MSDK_VARIABLE(GetCanvasItemMaxScale)
	DECLARE_V2_MSDK_VARIABLE(LockCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(UnLockCanvasItem)
	DECLARE_V2_MSDK_VARIABLE(IsCanvasItemLocked)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemEditable)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemEditable)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemTranslate)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemTranslate)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemSizeF)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemClip)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemClip)
	DECLARE_V2_MSDK_VARIABLE(IsCanvasItemClip)
	DECLARE_V2_MSDK_VARIABLE(EnableCanvasItemClip)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemTransform)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemTransform)
	DECLARE_V2_MSDK_VARIABLE(MoveCanvasItemZOrder)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemVisible)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemVisible)
	DECLARE_V2_MSDK_VARIABLE(CanvasItemSaveAsFile)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreprocess)
	DECLARE_V2_MSDK_VARIABLE(GetCanvasItemPreprocess)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemSourceClip)

	DECLARE_V2_MSDK_VARIABLE(RegisterCanvasEventObserver)
    DECLARE_V2_MSDK_VARIABLE(UnregisterCanvasEventObserver)

	// transition
	DECLARE_V2_MSDK_VARIABLE(CreateTransition)
	DECLARE_V2_MSDK_VARIABLE(DestroyTransition)
	DECLARE_V2_MSDK_VARIABLE(SetTransitionProperty)
    DECLARE_V2_MSDK_VARIABLE(AmbientEffect)

	// canvas_item preview
	DECLARE_V2_MSDK_VARIABLE(StartCanvasItemPreview)
	DECLARE_V2_MSDK_VARIABLE(StopCanvasItemPreview)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreviewParams)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreviewPos)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreviewFlipH)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreviewFlipV)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreviewRotate)

	// visual
	DECLARE_V2_MSDK_VARIABLE(EnumVisualSource)
	DECLARE_V2_MSDK_VARIABLE(CreateVisual)
	DECLARE_V2_MSDK_VARIABLE(DestroyVisual)
	DECLARE_V2_MSDK_VARIABLE(ReopenVisualAndUpdateTransforms)
	DECLARE_V2_MSDK_VARIABLE(SetVisualDestroyedWhenAllReferenceRemoved)
	DECLARE_V2_MSDK_VARIABLE(RegisterVisualEventObserver)
    DECLARE_V2_MSDK_VARIABLE(UnregisterVisualEventObserver)

	// virtual camera
	DECLARE_V2_MSDK_VARIABLE(StartVirtualCamera)
	DECLARE_V2_MSDK_VARIABLE(StopVirtualCamera)
	DECLARE_V2_MSDK_VARIABLE(SwitchVirtualCamera)
	DECLARE_V2_MSDK_VARIABLE(RegisterVirtualCameraEventObserver)
	DECLARE_V2_MSDK_VARIABLE(UnregisterVirtualCameraEventObserver)

	// visual filter
	DECLARE_V2_MSDK_VARIABLE(CreateVisualFilter)
	DECLARE_V2_MSDK_VARIABLE(DestroyVisualFilter)
    DECLARE_V2_MSDK_VARIABLE(SetVisualFilterProperty)
    DECLARE_V2_MSDK_VARIABLE(VisualFilterAction)
    DECLARE_V2_MSDK_VARIABLE(VisualFilterSetActive)
    DECLARE_V2_MSDK_VARIABLE(VisualFilterGetActive)
	DECLARE_V2_MSDK_VARIABLE(SetVisualFiltersPriority)

	DECLARE_V2_MSDK_VARIABLE(ShowPreviewWindow)
	DECLARE_V2_MSDK_VARIABLE(HidePreviewWindow)
	DECLARE_V2_MSDK_VARIABLE(IsAllPreviewEnable)
	DECLARE_V2_MSDK_VARIABLE(EnableAllPreview)
	DECLARE_V2_MSDK_VARIABLE(EnableTrack)
	DECLARE_V2_MSDK_VARIABLE(CreateProjector)
	DECLARE_V2_MSDK_VARIABLE(CloseProjector)
	DECLARE_V2_MSDK_VARIABLE(SetProjectorWndParams)
	DECLARE_V2_MSDK_VARIABLE(SetProjectorPosition)
	DECLARE_V2_MSDK_VARIABLE(SetUIConfig)
	DECLARE_V2_MSDK_VARIABLE(EnableFineTuning)
	DECLARE_V2_MSDK_VARIABLE(GetVideoFileFrameInfo)
	DECLARE_V2_MSDK_VARIABLE(RemoveModel)
	DECLARE_V2_MSDK_VARIABLE(UpdateModelFPS)
	DECLARE_V2_MSDK_VARIABLE(UpdateModelOutputSize)
	DECLARE_V2_MSDK_VARIABLE(UpdateModelColorSpaceAndVideoRange)
	DECLARE_V2_MSDK_VARIABLE(EnumEncoderSource)
	DECLARE_V2_MSDK_VARIABLE(MockRenderHung)
	DECLARE_V2_MSDK_VARIABLE(StartRenderProfiler)
	DECLARE_V2_MSDK_VARIABLE(StartCollectPerformanceMatrics)
	DECLARE_V2_MSDK_VARIABLE(SetTTNtpMS)

	DECLARE_V2_MSDK_VARIABLE(EnumServiceSource)
	DECLARE_V2_MSDK_VARIABLE(StartStream)
	DECLARE_V2_MSDK_VARIABLE(StopStream)
	DECLARE_V2_MSDK_VARIABLE(StartPushStream)
	DECLARE_V2_MSDK_VARIABLE(StopPushStream)

	DECLARE_V2_MSDK_VARIABLE(OutputThumbnailSaveAs)
	DECLARE_V2_MSDK_VARIABLE(SetStreamSEI)
	DECLARE_V2_MSDK_VARIABLE(ClearStreamSEI)
	DECLARE_V2_MSDK_VARIABLE(UpdateStreamAbrConfig)
	DECLARE_V2_MSDK_VARIABLE(StartSpeedTestStream)
	DECLARE_V2_MSDK_VARIABLE(StopSpeedTestStream)
	DECLARE_V2_MSDK_VARIABLE(GetStatisticInfo)
	DECLARE_V2_MSDK_VARIABLE(GetEncoderStatisticInfo)
	DECLARE_V2_MSDK_VARIABLE(GetEncoderPtsByStreamId)
	DECLARE_V2_MSDK_VARIABLE(UpdateABConfig)
	DECLARE_V2_MSDK_VARIABLE(VideoEncoderTargetBitrate)
	DECLARE_V2_MSDK_VARIABLE(GetRenderInfo)
	DECLARE_V2_MSDK_VARIABLE(TestEncoderSessionCountSupported)

	DECLARE_V2_MSDK_VARIABLE(GetVisualFPS)
	DECLARE_V2_MSDK_VARIABLE(SetVisualOverlay)
	DECLARE_V2_MSDK_VARIABLE(GetVisualOverlay)
	DECLARE_V2_MSDK_VARIABLE(EnumVisualInput)
	DECLARE_V2_MSDK_VARIABLE(DoVisualInputAction)
	DECLARE_V2_MSDK_VARIABLE(ReopenVisual)
	DECLARE_V2_MSDK_VARIABLE(EnumVisualFormat)
	DECLARE_V2_MSDK_VARIABLE(EnumAudioFormat)
	DECLARE_V2_MSDK_VARIABLE(SetVisualInputProperty)
	DECLARE_V2_MSDK_VARIABLE(SetVisualSourceProperty)
	DECLARE_V2_MSDK_VARIABLE(GetVisualSourceProperty)
	DECLARE_V2_MSDK_VARIABLE(GetVisualInputProperty)
	DECLARE_V2_MSDK_VARIABLE(PauseVisualCapture)
	DECLARE_V2_MSDK_VARIABLE(IsVisualCapturePause)
	DECLARE_V2_MSDK_VARIABLE(ContinueVisualCapture)
	
	DECLARE_V2_MSDK_VARIABLE(UpdateVirtualCameraProperty)
	DECLARE_V2_MSDK_VARIABLE(SetVirtualCameraRotate)
	DECLARE_V2_MSDK_VARIABLE(SetVirtualCameraFlipV)
	DECLARE_V2_MSDK_VARIABLE(SetVirtualCameraFlipH)
	DECLARE_V2_MSDK_VARIABLE(ForwardWindowMessage)

	DECLARE_V2_MSDK_VARIABLE(GetDefaultCaptureAudio)
	DECLARE_V2_MSDK_VARIABLE(GetDefaultRenderAudio)
	DECLARE_V2_MSDK_VARIABLE(EnumCaptureAudio)
	DECLARE_V2_MSDK_VARIABLE(EnumRenderAudio)
	DECLARE_V2_MSDK_VARIABLE(CreateAudioInput)
	DECLARE_V2_MSDK_VARIABLE(CreateLyraxAudioInput)
    DECLARE_V2_MSDK_VARIABLE(CreateCustomAudioInput)
	DECLARE_V2_MSDK_VARIABLE(DestroyAudioInput)
	DECLARE_V2_MSDK_VARIABLE(UpdatePCMAudioDatas)
	DECLARE_V2_MSDK_VARIABLE(SetAudioInputParams)
	DECLARE_V2_MSDK_VARIABLE(SetAudioVolume)
	DECLARE_V2_MSDK_VARIABLE(GetAudioVolume)
	DECLARE_V2_MSDK_VARIABLE(SetAudioBalancing)
	DECLARE_V2_MSDK_VARIABLE(GetAudioBalancing)
	DECLARE_V2_MSDK_VARIABLE(IsAudioDownmixMono)
	DECLARE_V2_MSDK_VARIABLE(SetAudioDownmixMono)
	DECLARE_V2_MSDK_VARIABLE(SetAudioSyncOffset)
	DECLARE_V2_MSDK_VARIABLE(GetAudioSyncOffset)
	DECLARE_V2_MSDK_VARIABLE(SetAudioMonitorType)
	DECLARE_V2_MSDK_VARIABLE(GetAudioMonitorType)
	DECLARE_V2_MSDK_VARIABLE(SetAudioInterval)
	DECLARE_V2_MSDK_VARIABLE(EnumAudioInputSource)
	DECLARE_V2_MSDK_VARIABLE(EnumAudioInput)
	DECLARE_V2_MSDK_VARIABLE(SetAudioMute)
	DECLARE_V2_MSDK_VARIABLE(IsAudioMute)
	DECLARE_V2_MSDK_VARIABLE(AddAudioInputToTrack)
	DECLARE_V2_MSDK_VARIABLE(RemoveAudioInputFromTrack)
	DECLARE_V2_MSDK_VARIABLE(GetAudioInputPerformance)
	DECLARE_V2_MSDK_VARIABLE(CreateLyraxEngine)
    DECLARE_V2_MSDK_VARIABLE(SetAudioInputReferenceId)
    DECLARE_V2_MSDK_VARIABLE(SetAudioInputAECOption)
    DECLARE_V2_MSDK_VARIABLE(SetAudioInputRawDataOption)
    DECLARE_V2_MSDK_VARIABLE(SetAudioInputANSOption)
    DECLARE_V2_MSDK_VARIABLE(SetAudioTrackDelayMs)
	DECLARE_V2_MSDK_VARIABLE(SetGlobalRenderDeviceID)
    DECLARE_V2_MSDK_VARIABLE(SetLyraxLiveRoomId)
    DECLARE_V2_MSDK_VARIABLE(EnableAudioInputEchoDetection)
    DECLARE_V2_MSDK_VARIABLE(SetAudioInputRenderDeviceID)

	// rtc
	DECLARE_V2_MSDK_VARIABLE(CreateEngine)
	DECLARE_V2_MSDK_VARIABLE(DestroyEngine)
	DECLARE_V2_MSDK_VARIABLE(RegisterRTCEventObserver)
	DECLARE_V2_MSDK_VARIABLE(UnregisterRTCEventObserver)
	DECLARE_V2_MSDK_VARIABLE(SetBusinessId)
	DECLARE_V2_MSDK_VARIABLE(JoinRoom)
	DECLARE_V2_MSDK_VARIABLE(LeaveRoom)
	DECLARE_V2_MSDK_VARIABLE(SetAudioProfile)
	DECLARE_V2_MSDK_VARIABLE(PublishStream)
	DECLARE_V2_MSDK_VARIABLE(UnPublishStream)
	DECLARE_V2_MSDK_VARIABLE(SubscribeStream)
	DECLARE_V2_MSDK_VARIABLE(UnSubscribeStream)
	DECLARE_V2_MSDK_VARIABLE(EnableLocalAudio)
	DECLARE_V2_MSDK_VARIABLE(SetPlaybackVolume)
	DECLARE_V2_MSDK_VARIABLE(SetAudioPlaybackDevice)
	DECLARE_V2_MSDK_VARIABLE(GetAudioPlaybackDevice)
	DECLARE_V2_MSDK_VARIABLE(EnableAudioPropertiesReport)
	DECLARE_V2_MSDK_VARIABLE(EnableLocalVideo)
	DECLARE_V2_MSDK_VARIABLE(UpdateCropAndScale)
	DECLARE_V2_MSDK_VARIABLE(UpdateExcludedVisuals)
	DECLARE_V2_MSDK_VARIABLE(SetRemoteVideoConfig)
	DECLARE_V2_MSDK_VARIABLE(SendSEIMessage)
	DECLARE_V2_MSDK_VARIABLE(StartForwardStreamToRooms)
	DECLARE_V2_MSDK_VARIABLE(UpdateForwardStreamToRooms)
	DECLARE_V2_MSDK_VARIABLE(StopForwardStreamToRooms)
	DECLARE_V2_MSDK_VARIABLE(StartLiveTranscoding)
	DECLARE_V2_MSDK_VARIABLE(UpdateLiveTranscoding)
	DECLARE_V2_MSDK_VARIABLE(StopLiveTranscoding)
	DECLARE_V2_MSDK_VARIABLE(SendRoomMessage)
	DECLARE_V2_MSDK_VARIABLE(SendUserMessage)

	// filter
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformInitialize)
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformUninitialize)
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformLoadModels)
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformUpdateConfig)
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformRegistObserver)
	DECLARE_V2_MSDK_VARIABLE(EffectPlatformUnregistObserver)

    DECLARE_V2_MSDK_VARIABLE(CreateAudioFilter)
    DECLARE_V2_MSDK_VARIABLE(DestroyAudioFilter)
    DECLARE_V2_MSDK_VARIABLE(SetAudioFilterEnable)
    DECLARE_V2_MSDK_VARIABLE(IsAudioFilterEnable)
    DECLARE_V2_MSDK_VARIABLE(AudioFilterAction)
    DECLARE_V2_MSDK_VARIABLE(SetAudioFilterProperty)
    DECLARE_V2_MSDK_VARIABLE(GetAudioFilterProperty)

    // VQM
	DECLARE_V2_MSDK_VARIABLE(InitVQM)
	DECLARE_V2_MSDK_VARIABLE(UnInitVQM)
	DECLARE_V2_MSDK_VARIABLE(QueryGoLiveRecommendedParams)
	DECLARE_V2_MSDK_VARIABLE(QueryCameraRecommendedParams)
	DECLARE_V2_MSDK_VARIABLE(QueryGoLiveManuallySelectedParams)
	DECLARE_V2_MSDK_VARIABLE(QueryCameraBestParamsForTarget)
	DECLARE_V2_MSDK_VARIABLE(ReconfigVideoOutput)
	DECLARE_V2_MSDK_VARIABLE(FallbackVideoEncoder)
	DECLARE_V2_MSDK_VARIABLE(SetCanvasItemPreprocessDefaultSize)
	DECLARE_V2_MSDK_VARIABLE(RemoveCanvasItemPreprocessDefaultSize)
	DECLARE_V2_MSDK_VARIABLE(UpdateStreamAbrOffset)
	DECLARE_V2_MSDK_VARIABLE(StartAdaptiveGearStrategyReport)

    DECLARE_V2_MSDK_VARIABLE(GetVisualFrame)
    DECLARE_V2_MSDK_VARIABLE(IsStreamInProcess)
};

class CallHelper
{
public:
    using call_id_t = uint32_t;

    CallHelper()
    {
    }

    ~CallHelper()
    {
    }

    template <typename ResultType, typename Func, typename... Args>
    ResultType SyncCall(Func func, Args... args)
    {
        std::promise<ResultType> pro;

        auto handler = [&pro](void* result) {
            ResultType res = *reinterpret_cast<ResultType*>(result);
            pro.set_value(res);
        };

        call_id_t call_id = 0;
        bool      ok = AddHandler(std::move(handler), &call_id);

        mediasdk::Closure closure;
        closure.call_id = call_id;
        closure.cb = &CallbackHandler;
        closure.ctx = this;

        func(std::forward<Args>(args)..., closure);

        return pro.get_future().get();
    }

    template <typename ResultType, typename Func, typename... Args>
    void SyncCallWithTimeout(int32_t timeout_ms, bool& success, ResultType& result, Func func, Args... args)
    {
        std::promise<ResultType> pro;

        auto handler = [&pro](void* result) {
            ResultType res = *reinterpret_cast<ResultType*>(result);
            pro.set_value(res);
        };

        call_id_t call_id = 0;
        bool      ok = AddHandler(std::move(handler), &call_id);

        mediasdk::Closure closure;
        closure.call_id = call_id;
        closure.cb = &CallbackHandler;
        closure.ctx = this;

        func(std::forward<Args>(args)..., closure);

        auto f = pro.get_future();
        auto status = f.wait_for(std::chrono::milliseconds(timeout_ms));
        if (status == std::future_status::ready)
        {
            result = f.get();
            success = true;
        }
        else
        {
            CallbackHandler(call_id, &result, this);
            success = false;
        }
    }

    template <typename Func, typename... Args>
    void AsyncCall(Func func, Args... args)
    {
        call_id_t call_id = 0;
        auto      handler = [](void* result) {};
        bool      ok = AddHandler(
            std::move(handler),
            &call_id);

        mediasdk::Closure closure;
        closure.call_id = call_id;
        closure.cb = &CallbackHandler;
        closure.ctx = this;

        return func(std::forward<Args>(args)..., closure);
    }

private:
    static void __stdcall CallbackHandler(call_id_t call_id, void* result, void* ctx)
    {
        CallHelper*                       c = reinterpret_cast<CallHelper*>(ctx);
        std::function<void(void* result)> handler;

        {
            std::unique_lock<std::mutex> lock(c->handler_list_mutex_);

            auto found = c->handler_list_.find(call_id);

            if (found != c->handler_list_.end())
            {
                handler = found->second;
                c->handler_list_.erase(found);
            }
        }

        if (handler)
        {
            handler(result);
        }
    }

    bool AddHandler(std::function<void(void* result)>&& handler, call_id_t* call_id)
    {
        std::unique_lock<std::mutex> lock(handler_list_mutex_);
        bool                         success = false;
        while (1)
        {
            call_id_t id = ++g_call_id_;
            if (handler_list_.find(id) == handler_list_.end())
            {
                if (call_id)
                    *call_id = id;

                handler_list_[id] = std::move(handler);
                success = true;
                break;
            }

				if (handler_list_.size() >= UINT_MAX)
				{
					success = false;
					break;
				}
			}

        return success;
    }

private:
    static std::atomic_uint64_t                            g_call_id_;
    std::mutex                                             handler_list_mutex_;
    std::map<call_id_t, std::function<void(void* result)>> handler_list_;
};
} // namespace sdk_helper