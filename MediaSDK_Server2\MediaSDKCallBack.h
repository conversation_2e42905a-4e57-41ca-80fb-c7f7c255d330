﻿#pragma once

#include <string>
#include <functional>
#include <map>
#include <set>

#include "ciefhelper.h"
#include "MediaSDKV2Define.h"
#include "NativeBridge/RTCLiveHelper.h"
#include "mediasdk_v2_header/nlohmann/json.hpp"
#include "mediasdk_v2_header/mediasdk_virtual_camera_event_observer.h"
#include "mediasdk_v2_header/mediasdk_visual_event_observer.h"
#include "mediasdk_v2_header/mediasdk_canvas_item_event_observer.h"
#include "LSPublicHeader.h"

namespace sdk_helper
{
	using ThreadMonitorEventHandler = std::function<void(MONITOR_THREAD_ID thread_id, MONITOR_THREAD_EVENT_TYPE event_type)>;

	class SDKCallBackMgr
		: public mediasdk::MediaSDKGlobalEventObserver,
		public mediasdk::MediaSDKWindowEventObserver,
		public mediasdk::MediaSDKCanvasEventObserver,
		public mediasdk::MediaSDKVisualEventObserver,
		public mediasdk::MediaSDKVirtualCameraEventObserver,
		public mediasdk::MediaSDKAudioStatusObserver,
		public mediasdk::MediaSDKStreamStatusObserver,
		public mediasdk::MediaSDKRTCEventObserver,
        public mediasdk::MediaSDKEPEventObserver
	{
	public:
		SDKCallBackMgr(sdk_helper::CallHelper& call_helper);
		~SDKCallBackMgr();
		// SDKCallBackMgr
		void SetThreadMonitorEventHandler(sdk_helper::ThreadMonitorEventHandler handler);
		void AddMobileProjectorID(const std::string& id);
		void EraseMobileProjectorID(const std::string& id);
		void SetVirtualCameraID(std::string virtual_camera_id);
		// MediaSDKGlobalEventObserver
		void OnPluginGlobalEvent(mediasdk::PluginInfo info, mediasdk::MediaSDKString event);
		void OnRenderThreadEvent(mediasdk::RenderThreadEvent event_type);
		void OnDeviceLostEvent(mediasdk::MSDevLostEvent event_type);
		void OnTeaEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event);
        void OnParfaitContextEvent(mediasdk::MediaSDKString key, mediasdk::MediaSDKString value);
		void OnVqosDataReport(mediasdk::MediaSDKString data);
		// MediaSDKWindowEventObserver
		void OnRButtonDown(int sink_id);
		void OnRButtonUp(int sink_id);
		void OnLButtonDblClk(int sink_id);
		void OnLButtonDown(int sink_id);
		void OnLButtonUp(int sink_id);
		void OnMouseLeave(int sink_id);
		void OnMouseHover(int sink_id);
		// MediaSDKCanvasEventObserver
		void OnTransitionFinished(const std::string& transition_id);
		void OnCurrentCanvasChanged(uint32_t video_model_id, mediasdk::MediaSDKString canvas_id);
		void OnCanvasItemsDestroyedByDestroyCanvas(mediasdk::MediaSDKString destory_canvas_id, mediasdk::MediaSDKStringArray canvas_item_ids);
		void OnCanvasCurrentItemChanged(uint32_t video_model_id, mediasdk::MediaSDKString canvas_id, bool manuel, mediasdk::MediaSDKString canvas_item_id);
		void OnHittestCanvasItemChanged(int video_model_id, mediasdk::MediaSDKString canvas_item_id);
		void OnCanvasItemTransformChanged(mediasdk::MediaSDKString canvas_item_id, const mediasdk::MSTransform& transform);
		void OnBeginTrack(int video_model_id, mediasdk::MediaSDKString canvas_item_id, mediasdk::HittestCursorPos pos);
		void OnEndTrack(int video_model_id, mediasdk::MediaSDKString canvas_item_id, mediasdk::HittestCursorPos pos);
		void OnCanvasItemInvalidArea(int video_model_id, mediasdk::MediaSDKString canvas_item_id);
		void OnCanvasItemValidArea(int video_model_id, mediasdk::MediaSDKString canvas_item_id);
		void OnCanvasItemFilterNotify(mediasdk::MediaSDKString canvas_item_id, mediasdk::MediaSDKString filter_id, mediasdk::MediaSDKString notify, mediasdk::MediaSDKString json_data);
		void OnCanvasItemShortcutActionNotify(mediasdk::MediaSDKString canvas_item_id, mediasdk::ShortcutAction shortcut_action);
		void OnCanvasItemClipMaskEnd(mediasdk::MediaSDKString canvas_item_id);
		void OnCanvasFilterNotify(mediasdk::MediaSDKString canvas_id, mediasdk::MediaSDKString filter_id, mediasdk::MediaSDKString  notify, mediasdk::MediaSDKString json_data);
        void OnCanvasItemTransformAnimationFinished(const mediasdk::MediaSDKString& canvas_item_id);
        void OnCanvasItemVisibleAnimationFinished(const mediasdk::MediaSDKString& canvas_item_id);
		// MediaSDKVisualEventObserver
		void OnVisualSizeChanged(mediasdk::MediaSDKString id, const mediasdk::MSSize& new_size);
		void OnVisualSourceEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event);
		void OnVisualReopenResult(bool reopen_result, mediasdk::MediaSDKString id);
		void OnVisualDestroyedWhenAllRefsRemoved(mediasdk::MediaSDKString id);
		//MediaSDKVirtualCameraEventObserver
		void OnVirtualCameraFirstFrameWrite(const mediasdk::MSSize& new_size);
		// MediaSDKAudioStatusObserver
		void OnAudioPeak(mediasdk::MediaSDKString id, const float* peak_array, uint32_t peak_array_size);
		void OnAudioPeakLR(mediasdk::MediaSDKString id, const float left_dev, const float right_dev, const float left, const float right);
		void OnAudioTrackPeak(uint32_t    track_id, const float left, const float right);
        void OnEchoDetectionResult(const float probability);
		void OnAudioEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event);
		// MediaSDKStreamStatusObserver
		void OnStartStreamResult(mediasdk::MediaSDKString  id, mediasdk::StreamErrorCode error_code);
		void OnReconnecting(mediasdk::MediaSDKString id);
		void OnConnected(mediasdk::MediaSDKString id);
		void OnFirstFrame(mediasdk::MediaSDKString id);
		void OnStartFallback(mediasdk::MediaSDKString id);
		void OnFallbackResult(mediasdk::MediaSDKString id, bool success);
		void OnStreamStopped(mediasdk::MediaSDKString  id, mediasdk::StreamErrorCode error_code);
		void OnEncodeError(mediasdk::MediaSDKString id);
		void OnEncodeEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString json_param);
		void OnBitrateChange(mediasdk::MediaSDKString id, uint32_t pre_bitrate_kbps, uint32_t bitrate_kbps);
		void OnSpeedTestResult(mediasdk::MediaSDKString id, mediasdk::MediaSDKString speed_test_result);
		// MediaSDKRTCEventObserver
		void OnEngineStart(int error_code);
		void OnEngineStop();
		void OnJoinChannel(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString user_id, int state, mediasdk::MediaSDKString extra_info);
		void OnLeaveChannel();
		void OnUserJoined(mediasdk::MediaSDKString user_id, int elapsed);
		void OnUserLeave(mediasdk::MediaSDKString user_id, int reason);
		void OnLocalStreamStats(mediasdk::MediaSDKString stats);
		void OnRemoteStreamStats(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString stats);
		void OnUserPublishStream(mediasdk::MediaSDKString user_id, int stream_index, bool is_screen, int media_stream_type);
		void OnUserUnpublishStream(mediasdk::MediaSDKString user_id, int stream_index, int media_stream_type, int reason);
		void OnNetworkQuality(mediasdk::MediaSDKString quality);
		void OnConnectionStateChanged(int state);
		void OnWarning(int warn);
		void OnError(int error);
		void OnForwardStreamStateChanged(mediasdk::MediaSDKString stream_state_infos);
		void OnLocalAudioPropertiesReport(mediasdk::MediaSDKString audio_properties_infos);
		void OnRemoteAudioPropertiesReport(mediasdk::MediaSDKString audio_properties_infos);
		void OnActiveSpeaker(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString uid);
		void OnAudioDeviceStateChanged(mediasdk::MediaSDKString state_info);
		void OnFirstRemoteAudioFrame(mediasdk::MediaSDKString user_id, int stream_index);
		void OnFirstRemoteVideoFrameDecoded(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info);
		void OnFirstRemoteVideoFrameRendered(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info);
		void OnRemoteVideoSizeChanged(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info);
		void OnSEIMessageReceived(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString message);
		void OnRoomMessageReceived(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString message);
		void OnUserMessageReceived(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString message);
		void OnStreamMixingEvent(mediasdk::MediaSDKString task_id, int event_type, int error, int mixed_stream_type);
		//MediaSDKEPEventObserver
		void OnDownloadModelSuccess(mediasdk::MediaSDKString request_id);
		void OnDownloadModelError(mediasdk::MediaSDKString request_id, mediasdk::MediaSDKString error);
		void OnDownloadModelProgress(mediasdk::MediaSDKString request_id, int progress);

	private:
		void PostAudioPeak();
		void OnAudioPeakTimeout();

		sdk_helper::CallHelper& call_helper_;
		std::mutex event_handler_mutex_;
		std::mutex thread_monitor_handler_mutex_;
		sdk_helper::ThreadMonitorEventHandler thread_monitor_handler_;
		std::atomic_bool thread_tick_start_ = false;

		std::map<uint32_t, std::string> mouse_click_canvas_item_id_;
		std::map<std::string, std::vector<float>> audio_info_list_;
		std::set<std::string> mobile_projector_id_;
		std::string m_virtual_camera_id;

		unsigned long long timestamp_;
		unsigned long long interval_ = 100;

		// Audio level processing parameters
        struct AudioLevelState
        {
            std::vector<float> currentLevels;      // Current smoothed levels (left, right, left_dev, right_dev)
            std::vector<float> peakHoldLevels;     // Peak levels for display
            uint64_t           lastUpdateTime = 0; // Last time this state was updated
            uint64_t           lastDataTime = 0;   // Last time new audio data was received
        };

        std::map<std::string, AudioLevelState> audio_level_states_;
	};

}