﻿#pragma once

#include "LSPublicHeader.h"
#include "systemutil.h"
#include "Source.h"
#include "ProcessCheck.h"
#include "icief.h"
#include "ciefhelper.h"

struct GameRetryConfig
{
	int64_t                             maxRetryCount = 6;
	int64_t                             totalTimeoutMs = 30000;
	std::chrono::steady_clock::duration timeout_1 = std::chrono::seconds(5);
	std::chrono::steady_clock::duration timeout_n = std::chrono::seconds(10);
	std::chrono::steady_clock::duration retryInterval = std::chrono::seconds(30);
};

class SourceMgr : public ITimerHandler
{
public:
	static SourceMgr* GetInstance();
	SourceMgr();
	~SourceMgr();

	void Init();
	void Uninit();

	UINT64  AllocSourceID(UINT64 track);
	Source* GetSourceByID(UINT64 id);
	bool    FindSourceByID(UINT64 id);
	bool    CheckSourceExist(UINT64 id);

	void GetSourceInfoByID(UINT64 id, SOURCE_INFO* info);
	void SetSourceInfoByID(UINT64 id, const SOURCE_INFO& info);

	UINT64 CreateSource(SOURCE_INFO* info, const UINT64* id = NULL);
	bool   ReopenSource(UINT64 sourceID, SOURCE_INFO* info);
	UINT64 CreateNativeSource(SOURCE_INFO* info, const UINT64* id = NULL);

	void DestroySource(UINT64 id);
	void DestroySource(Source* source);
	void DestroySourceObject(UINT64 id);
	void SetSourceNotRealCreated(UINT64 id);

	bool                ActivateFallback(COMPOSITE_SOURCE_META& meta, UINT64 failedSourceID);
	std::vector<UINT64> GetLayersBySourceID(UINT64 sourceID);
	void                UpdateLayerBinding(UINT64 layerID, UINT64 newSourceID);
	UINT64              IsMatchedSource(SOURCE_INFO& sourceInfo);
	void                TransferLayerBindingsWithTransform(UINT64 oldSourceID, UINT64 newSourceID);

	std::unordered_map<UINT64, COMPOSITE_SOURCE_META> GetCompositeMetas();
	void                                              SetCompositeMetas(const std::unordered_map<UINT64, COMPOSITE_SOURCE_META>& metas);
	VISUAL_TYPE                                       GetCompositeRealType(UINT64 sourceID);

	void SetGameRetryConfig(const GameRetryConfig& config);
	GameRetryConfig GetGameRetryConfig() const;

protected:
	UINT64 CreateGameSource(SOURCE_INFO* info, const UINT64* id = NULL);
	UINT64 CreateCameraSource(SOURCE_INFO* info, const UINT64* id = NULL);
	bool   ReopenCameraSource(UINT64 sourceID, const CAMERA_SOURCE& newCamera);

	void OnLayerFallback(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason, bool fallbackToPlaceHolder = false);

protected:
	UINT                                              m_sourceCounter = Util::GetMicrosecondTimestamp();
	std::map<UINT64, Source*>                         m_sources{};
	std::unordered_map<UINT64, COMPOSITE_SOURCE_META> m_compositeMetas;

public:
	enum GAME_CAPTURE_STATE
	{
		GAME_CAPTURE_START,
		GAME_CAPTURE_WAIT,
		GAME_CAPTURE_STOP,
	};

	struct GameRetryContext
	{
		UINT32              attemptCount = 0;
		std::vector<UINT64> pendingLayers;
		GAME_CAPTURE_STATE  state = GAME_CAPTURE_STATE::GAME_CAPTURE_START;
		bool				processCaptureExists = false;
		bool				notifiedCaptureType = false;
		HWND				hwnd = NULL;
		DWORD				processID = 0;
		std::string			exeName;
		bool                recoveryAttempted = false;

		std::chrono::steady_clock::time_point firstFailureTime = std::chrono::steady_clock::now();
		std::chrono::steady_clock::time_point lastStateChangeTime = std::chrono::steady_clock::now();
		std::chrono::steady_clock::time_point lastProcessCheckTime = std::chrono::steady_clock::now();
	};

	void StartGameRetry(UINT64 sourceID, const GameRetryContext& ctx);
	void StopGameRetry(UINT64 sourceID);
	bool GetRetryContext(UINT64 sourceID, GameRetryContext* outCtx);
	void TriggerGameRetry(UINT64 sourceID);

	bool CheckProcessExists(DWORD processId) const;
	bool CheckWindowExists(HWND hwnd) const;
	bool FindProcessByExeName(const std::string& exeName, DWORD* outProcessId, HWND* outHwnd) const;
	void NotifyGameProcessChanged(UINT64 sourceID, bool exists, DWORD processID, HWND hwnd);

private:
	void         StartTimer(void* param);
	void         StopTimer();
	virtual void HandleTimer(UINT64 id) override;
	void         ProcessGameSource(UINT64 sourceID, std::chrono::steady_clock::time_point now);

private:
	std::mutex                                   m_retryMutex;
	std::unordered_map<UINT64, GameRetryContext> m_gameRetryContexts;
	LS::ThreadTaskManager                        m_taskMgr;
	UINT64                                       m_timerID = 0;
	UINT64                                       m_timerCounter = 1;
	ProcessCheck                                 m_processCheck{};
	GameRetryConfig                              m_retryConfig;
};