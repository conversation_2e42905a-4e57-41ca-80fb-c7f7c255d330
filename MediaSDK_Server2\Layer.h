﻿#pragma once

#include "Source.h"
#include "filter.h"

struct SYNCCH<PERSON>DRENINFO
{
	UINT64 layerID;
	UINT64 mediaCanvas;
};

class Layer
{
public:
	Layer();
	~Layer();
	void SetLayerInfo(const LAYER_INFO* info);
	void SetParent(UINT64 id);
	void ControlLayer(const LAYER_INFO& layerInfo, LAYER_CONTROL_CMD cmd);
	void GetLayerInfo(LAYER_INFO* layerInfo);
	bool SyncToMediaCanvas(uint64_t canvasID, bool add);

	void AddFilter(UINT64 id, FILTER* info);
	void RemoveFilter(UINT64 id, FILTER* info);

	void GetCanvasLayoutRect(Gdiplus::RectF* rect);
	void GetLayerDescAngle(float* angle);

	void SetLayerPreview(UINT64 previewID, const LAYER_PREVIEW* preview);
	void GetLayerPreview(UINT64 previewID, LAYER_PREVIEW* preview);

	void SetLayerSize(const Gdiplus::SizeF* oldSize, const Gdiplus::SizeF* newSize);
	void SetLayerLayout(const Gdiplus::RectF* curRect);

	void UpdateLayerTransform(const TRANSFORM* transform);
	void UpdateVISAudioInfo(const AUDIO_INFO* info);

	void CalLayerTransform(UINT64& cmd, UINT64 canvasID = 0);
	void ReCalLayerTransform(float width, float height);
protected:
	// Calculate method for layer layout
    void GetClippedSize(Gdiplus::SizeF* csize);
    bool GetRotatedSize(const Gdiplus::SizeF* size, Gdiplus::SizeF* rsize);

	void SetLayerToCenter(const Gdiplus::RectF* rect);
	void SetLayerToHorizontalCenter(const Gdiplus::RectF* rect);
	void SetLayerToVerticalCenter(const Gdiplus::RectF* rect);
    void SetLayerCoverRatioFill(const Gdiplus::RectF* rect);
    void SetLayerContainRatioFill(const Gdiplus::RectF* rect);
    void SetLayerTileFill(const Gdiplus::RectF* rect);

protected:
	LAYER_INFO m_layerInfo{};
	std::unordered_map<UINT64, LAYER_PREVIEW> m_layerPreview;
	bool m_syncFlag = false;
};