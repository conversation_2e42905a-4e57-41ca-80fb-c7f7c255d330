#include "stdafx.h"
#include "layout.h"
#include "stringutil.h"
#include "visual/visual.h"
#include "../BaseLib/event_bus.h"

namespace LS
{
Layout::RequestList Layout::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Layout::SwitchMode>());
    list.push_back(std::make_unique<Layout::AddScenes>());
    list.push_back(std::make_unique<Layout::CreateScene>());
    list.push_back(std::make_unique<Layout::SwitchScene>());
    list.push_back(std::make_unique<Layout::RemoveScene>());
    list.push_back(std::make_unique<Layout::LoadSceneNotShow>());

    list.push_back(std::make_unique<Layout::UpdateCanvasLayout>());
    list.push_back(std::make_unique<Layout::SetMixParameter>());

	list.push_back(std::make_unique<Layout::EnumVisuals>());
    list.push_back(std::make_unique<Layout::AddVisuals>());
    list.push_back(std::make_unique<Layout::CloneVisual>());

	list.push_back(std::make_unique<Layout::EnumVideoENC>());
	list.push_back(std::make_unique<Layout::EnumAudioENC>());

    list.push_back(std::make_unique<Layout::SetDisplay>());
    list.push_back(std::make_unique<Layout::GetDisplay>());

    list.push_back(std::make_unique<Layout::OpenMobilePreview>());
    list.push_back(std::make_unique<Layout::CloseMobilePreview>());

    list.push_back(std::make_unique<Layout::ResetOrder>());
    list.push_back(std::make_unique<Layout::OutputThumbnail>());
    list.push_back(std::make_unique<Layout::HasVisualIsClipping>());

    list.push_back(std::make_unique<Layout::OpenCanvasPreview>());
    list.push_back(std::make_unique<Layout::CloseCanvasPreview>());
    list.push_back(std::make_unique<Layout::SetCanvasPreviewParams>());
    list.push_back(std::make_unique<Layout::SetCanvasPreviewPosition>());

    return list;
}

void Layout::ModuleCreate()
{
	eventbus::EventBus::Subscribe([](const OutputThumbnailEvent& event) {
        OutputThumbnail::AsyncMethodContext* newfunctor = (OutputThumbnail::AsyncMethodContext*)event.functor;
        newfunctor->resolve(true);
        delete newfunctor;
		});
}

bool Layout::SwitchMode::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    LIVE_MODE layout_mode = (LIVE_MODE)req.layout_mode();
    controller->SelectMode(layout_mode);
    return true;
}

bool Layout::AddScenes::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    LIVE_MODE layout_mode = (LIVE_MODE)req.layout_mode();

    for (int i = 0; i < req.scene_ids_size(); ++i)
    {
        UINT64 sceneID = 0;
        Util::StringToNum(req.scene_ids(i), &sceneID);
        if (!controller->FindSceneByID(sceneID))
        {
            LOG(ERROR) << "[Layout::AddScenes] add scene to mode: " << layout_mode << " failed, sceneID: " << sceneID;
            continue;
        }

        controller->AddSceneByID(layout_mode, sceneID);
    }

    return true;
}

bool Layout::CreateScene::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    SCENE_INFO sceneInfo{};
    sceneInfo.mode = (LIVE_MODE)req.scene_info().layout_mode();
    UINT64 sceneID = controller->AddSceneWithInfo(sceneInfo.mode, &sceneInfo);
    std::string scene_id = "";
    Util::NumToString(sceneID, &scene_id);
    rsp.set_scene_id(scene_id);

    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::CreateScene] AddScene failed, sceneID: " << sceneID;
        return false;
    }

    std::vector<UINT64> canvasIDs{};
    if (sceneInfo.mode == LIVE_MODE_LANDSCAPE || sceneInfo.mode == LIVE_MODE_PORTRAIT)
    {
        UINT64 canvasID = controller->GetCanvasID(sceneID, (UINT32)sceneInfo.mode);
        if (!controller->FindCanvasByID(canvasID))
        {
            LOG(ERROR) << "[Layout::CreateScene] canvas not exist, canvsaID: " << canvasID;
            return false;
        }
        
        if (sceneInfo.mode == LIVE_MODE_LANDSCAPE)
        {
            canvasIDs.push_back(canvasID);
            canvasIDs.push_back(0);
        }
        else
        {
            canvasIDs.push_back(0);
            canvasIDs.push_back(canvasID);
        }
    }
    else if (sceneInfo.mode == LIVE_MODE_DBCANVAS)
    {
        for (int i = 0; i < LIVE_MODE_DBCANVAS; ++i)
        {
            UINT64 canvasID = controller->GetCanvasID(sceneID, i);
            if (!controller->FindCanvasByID(canvasID))
            {
                LOG(ERROR) << "[Layout::CreateScene] canvas not exist, canvsaID: " << canvasID;
                return false;
            }
            canvasIDs.push_back(canvasID);
        }
    }

    for (int i = 0; i < canvasIDs.size(); ++i)
    {
        auto ref = rsp.add_canvas_ids();
        std::string canvas_id = "";
        Util::NumToString(canvasIDs[i], &canvas_id);
        *ref = canvas_id;
    }

    return true;
}

bool Layout::SwitchScene::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
    
    UINT64 sceneID = 0;
    Util::StringToNum(req.scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::SwitchScene] scene not exist, sceneID: " << sceneID;
        return false;
    }

    controller->SelectScene(sceneID, req.reload_when_switch_mode());
    return true;
}

bool Layout::RemoveScene::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    for (auto info : req.scene_infos())
    {
        UINT64 sceneID = 0;
        Util::StringToNum(info.scene_id(), &sceneID);
        if (!controller->FindSceneByID(sceneID))
        {
            LOG(ERROR) << "[Layout::RemoveScene] scene not exist, sceneID: " << sceneID;
            continue;
        }

        controller->DeleteScene(sceneID);
    }
    return true;
}

bool Layout::LoadSceneNotShow::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 sceneID = 0;
    Util::StringToNum(req.scene_info().scene_id(), &sceneID);
    controller->PreLoadScene(sceneID, req.reload_when_switch_mode());
    return true;
}

bool Layout::UpdateCanvasLayout::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 sceneID = 0;
    Util::StringToNum(req.canvas_info().scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::UpdateCanvasLayout] scene not exist, sceneID: " << sceneID;
        return false;
    }

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_info().canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Layout::UpdateCanvasLayout] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    CANVAS_INFO canvasInfo{};
    controller->GetCanvasInfo(canvasID, &canvasInfo);

    if (req.has_view_rect())
    {
        canvasInfo.rect = {req.view_rect().left(), req.view_rect().top(), req.view_rect().width(), req.view_rect().height()};
    }
    if (req.has_layout_view_rect())
    {
        canvasInfo.layoutRect = {0, 0, req.layout_view_rect().width(), req.layout_view_rect().height()};
    }
    if (req.has_output_size())
    {
        canvasInfo.outputSize = {req.output_size().x(), req.output_size().y()};
    }
    controller->UpdateCanvasLayout(canvasID, &canvasInfo);

    return true;
}

bool Layout::SetMixParameter::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    VIDEO_MIX_PARAM mixParam{};
    mixParam.colorConfig.colorSpace = (COLOR_SPACE)req.video_mix().color_config().color_space();
    mixParam.colorConfig.colorTransfer = (COLOR_TRANSFER)req.video_mix().color_config().color_transfer();
    mixParam.colorConfig.colorRange = (COLOR_RANGE)req.video_mix().color_config().color_range();
    mixParam.focusNv12 = req.video_mix().focus_nv12();
    mixParam.outputSize.Width = (LONG)req.video_mix().output_size().x();
    mixParam.outputSize.Height = (LONG)req.video_mix().output_size().y();
    mixParam.fps = req.video_mix().fps();
    mixParam.format = (VIDEO_PIXEL_FORMAT)req.video_mix().pixel_format();
    mixParam.bufferType = (BUFFER_TYPE)req.video_mix().buffer_type();

    controller->SetMixParameter(req.video_model_id(), mixParam);

    return true;
}

bool Layout::EnumVideoENC::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<VIDEO_ENCODER_INFO> oVideoEncoders{};
    controller->EnumVideoEnc(&oVideoEncoders);
    for (int i = 0; i < oVideoEncoders.size(); ++i)
    {
        VIDEO_ENCODER_INFO encoder = oVideoEncoders[i];
        auto ref = rsp.add_video_encoders();
        ref->set_codec(encoder.codec);
        ref->set_name(encoder.name);
        ref->set_hardware(encoder.hardware);
    }
    return true;
}

bool Layout::EnumAudioENC::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<std::string> audioEncoders = {"FAAC", "FFMEPG_AAC"};
    for (int i = 0; i < audioEncoders.size(); ++i)
    {
        std::string encoder = audioEncoders[i];
        auto ref = rsp.add_audio_encoders();
        *ref = encoder;
    }
    return true;
}

bool Layout::EnumVisuals::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 sceneID = 0;
    Util::StringToNum(req.canvas_info().scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::EnumVisuals] scene not exist, sceneID: " << sceneID;
        return false;
    }

    auto visitLayers = [&controller, &rsp](UINT64 canvasID) {
        CANVAS_INFO_EX canvasInfoEx{};
        controller->EnumLayers(canvasID, &canvasInfoEx);

        std::vector<LAYER_INFO> layers = canvasInfoEx.layers;
        for (int i = 0; i < layers.size(); ++i)
        {
            LAYER_INFO  layerInfo = layers[i];
            auto        ref = rsp.add_visual_infos();
            std::string visual_id = "";
            Util::NumToString(layerInfo.id, &visual_id);
            ref->set_visual_id(visual_id);

            SOURCE_INFO sourceInfo{};
            controller->GetSourceInfo(layerInfo.sourceID, &sourceInfo);

            std::string canvas_id = "";
            Util::NumToString(canvasID, &canvas_id);
            ref->set_canvas_id(canvas_id);

            ls_visual::VisualUniAttr uni_attr{};
            Visual::GetVisualUniformInfo(canvasInfoEx, layerInfo, sourceInfo, uni_attr);
            ref->mutable_visual_uni_attr()->CopyFrom(uni_attr);
        }
    };

    UINT64     canvasID = 0;
    SCENE_INFO sceneInfo{};
    controller->GetSceneInfo(sceneID, &sceneInfo);
    if (sceneInfo.mode == LIVE_MODE_PORTRAIT || sceneInfo.mode == LIVE_MODE_LANDSCAPE)
    {
        canvasID = controller->GetCanvasID(sceneID, (UINT32)sceneInfo.mode);
        if (!controller->FindCanvasByID(canvasID))
        {
            LOG(ERROR) << "[Layout::EnumVisuals] canvas not exist, canvsaID: " << canvasID;
            return false;
        }
        visitLayers(canvasID);
    }
    else if (sceneInfo.mode == LIVE_MODE_DBCANVAS)
    {
        for (int i = 0; i < LIVE_MODE_DBCANVAS; ++i)
        {
            canvasID = controller->GetCanvasID(sceneID, i);
            if (!controller->FindCanvasByID(canvasID))
            {
                LOG(ERROR) << "[Layout::EnumVisuals] canvas not exist, canvsaID: " << canvasID;
                return false;
            }
            visitLayers(canvasID);
        }
    }

    return true;
}

bool Layout::AddVisuals::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 sceneID = 0;
    Util::StringToNum(req.canvas_info().scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::AddVisuals] scene not exist, sceneID: " << sceneID;
        return false;
    }

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_info().canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Layout::AddVisuals] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    std::vector<std::string> failed_visual_ids{};
    for (int i = 0; i < req.visual_ids_size(); ++i)
    {
        UINT64 layerID = 0;
        Util::StringToNum(req.visual_ids(i), &layerID);
        if (!controller->FindLayerByID(layerID))
        {
            failed_visual_ids.push_back(req.visual_ids(i));
            LOG(ERROR) << "[Layout::AddVisuals] visual not exist, visualID: " << layerID;
            continue;
        }

        bool ret = controller->AddLayerByID(canvasID, layerID);
        if (ret)
        {
            controller->HandleLayerSizeChange(layerID);
        }
        else
        {
            failed_visual_ids.push_back(req.visual_ids(i)); 
        }
    }

    for (const auto& visual_id : failed_visual_ids)
    {
        auto* ref = rsp.add_failed_visual_ids();
        *ref = visual_id;
    }

    return true;
}

bool Layout::CloneVisual::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 oriLayerID = 0;
    Util::StringToNum(req.origin_visual_id(), &oriLayerID);
    if (!controller->FindLayerByID(oriLayerID))
    {
        LOG(ERROR) << "[Layout::CloneVisual] origin layer not exist, oriLayerID: " << oriLayerID;
        return false;
    }

    LAYER_INFO layerInfo{};
    controller->GetLayerInfo(oriLayerID, &layerInfo);

    SOURCE_INFO sourceInfo{};
    controller->GetSourceInfo(layerInfo.sourceID, &sourceInfo);
    sourceInfo.isCloned = true;

    Gdiplus::SizeF originLayerSize = layerInfo.transform.size;
    Visual::SetVisualUniformInfo(layerInfo, sourceInfo, req.visual_uni_attr());

    // Only Bytelink Use Original Source size
    if (sourceInfo.type != VISUAL_CAMERA)
    {
        layerInfo.transform.size = originLayerSize;
    }
    layerInfo.filters = {};
    layerInfo.isCreated = false;
    controller->CreateLayer(&layerInfo);

    sourceInfo.layerIDs.push_back(layerInfo.id);
    controller->SetSourceInfo(layerInfo.sourceID, &sourceInfo);

    std::string target_visual_id = "";
    Util::NumToString(layerInfo.id, &target_visual_id);
    rsp.set_target_visual_id(target_visual_id);

    if (!controller->FindLayerByID(layerInfo.id))
    {
        LOG(ERROR) << "[Layout::CloneVisual] clone layer failed, tarLayerID: " << layerInfo.id;
        return false;
    }

    return true;
}

bool Layout::SetDisplay::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT32 video_model_id = 0;
    if (req.has_video_model_id())
    {
        controller->SetDisplay(req.show_view(), req.video_model_id());
    }
    else
    {
        controller->SetDisplay(req.show_view());
    }
    
    return true;
}

bool Layout::GetDisplay::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    bool showView = false;
    showView = controller->GetDisplay(req.video_model_id());

    rsp.set_show_view(showView);
    return true;
}

bool Layout::OpenMobilePreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    Gdiplus::RectF rect = {req.view_rect().left(), req.view_rect().top(), req.view_rect().width(), req.view_rect().height()};
    controller->OpenMobilePreview(req.video_model_id(), req.hwnd(), rect);

    return true;
}

bool Layout::OpenCanvasPreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    Gdiplus::RectF rect = { req.view_rect().left(), req.view_rect().top(), req.view_rect().width(), req.view_rect().height() };
    UINT64 previewID = controller->AllocPreviewID();
    std::string preview_id = "";
    Util::NumToString(previewID, &preview_id);
    PREVIEW_PARAMS params = { req.is_popup(), req.top_border_radius(), req.bottom_border_radius(), req.opacity()};
    if (!controller->OpenCanvasPreview(preview_id, req.canvas_idx(), (HWND)req.hwnd(), rect, params)) {
        rsp.set_canvas_preview_id("");
        rsp.set_error_code(-1);
    }
    else
    {

        rsp.set_canvas_preview_id(preview_id);
        rsp.set_error_code(0);
    }
    return true;
}

bool Layout::CloseCanvasPreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    if (!controller->CloseCanvasPreview(req.canvas_preview_id(), req.canvas_idx()))
    {
        rsp.set_error_code(-1);
    }
    else
    {
        rsp.set_error_code(0);
    }
    return true;
}

bool Layout::SetCanvasPreviewParams::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    PREVIEW_PARAMS params = { true, req.top_border_radius(), req.bottom_border_radius(), req.opacity()};
    if (!controller->SetCanvasPreviewParams(req.canvas_preview_id(), req.canvas_idx(), &params))
    {
        rsp.set_error_code(-1);
    }
    else
    {
        rsp.set_error_code(0);
    }
    return true;
}

bool Layout::SetCanvasPreviewPosition::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    Gdiplus::RectF rect = { req.view_rect().left(), req.view_rect().top(), req.view_rect().width(), req.view_rect().height() };

    if (!controller->SetCanvasPreviewPosition(req.canvas_preview_id(), req.canvas_idx(), rect)) {
        rsp.set_error_code(-1);
    }
    else
    {
        rsp.set_error_code(0);
    }
    return true;
}

bool Layout::CloseMobilePreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->CloseMobilePreview(req.video_model_id());
    return true;
}

bool Layout::ResetOrder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_info().canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Layout::ResetOrder] canvas not exist, canvasID: " << canvasID;
        return false;
    }

	std::vector<UINT64> layerIDs{};
	for (int i = 0; i < req.visual_ids_size(); ++i)
	{
        UINT64 layerID = 0;
        Util::StringToNum(req.visual_ids(i), &layerID);
        layerIDs.push_back(layerID);
	}

    std::vector<UINT64> moreLayerIDs{}, lessLayerIDs{};
    if (!layerIDs.empty())
        controller->UpdateLayersOrder(canvasID, layerIDs, &moreLayerIDs, &lessLayerIDs);

    if (!moreLayerIDs.empty() || !lessLayerIDs.empty())
    {
        for (const auto& layerID : moreLayerIDs)
        {
            std::string visual_id = "";
            Util::NumToString(layerID, &visual_id);

            auto ref = rsp.add_more_visual_ids();
            *ref = visual_id;
        }

        for (const auto& layerID : lessLayerIDs)
        {
            std::string visual_id = "";
            Util::NumToString(layerID, &visual_id);

            auto ref = rsp.add_less_visual_ids();
            *ref = visual_id;
        }

        LOG(ERROR) << "[Layout::ResetOrder] FE input layerIDs more or less than native!";
    }

    return true;
}

void Layout::OutputThumbnail::asyncHandle(std::unique_ptr<AsyncMethodContext> functor)
{
	m_requestHeader = &functor->requestHeader;
	m_responseHeader = &functor->responseHeader;
	auto& req = functor->requestMessage;
	auto& rsp = functor->responseMessage;

	auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
    {
		functor->resolve(false);
		return;
    }

    AsyncMethodContext* newfunctor = new AsyncMethodContext{};
    *newfunctor = *functor.release();

	UINT64 sceneID = 0;
	Util::StringToNum(req.canvas_info().scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::OutputThumbnail::asyncHandle] scene not exist, sceneID: " << sceneID;
        return;
    }

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_info().canvas_id(), &canvasID);
    controller->OutputThumbnail((LIVE_MODE)req.canvas_info().scene_info().layout_mode(), sceneID, canvasID, req.file_path(), (IMAGE_FORMAT)req.file_format(), newfunctor);
}

bool Layout::OutputThumbnail::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 sceneID = 0;
    Util::StringToNum(req.canvas_info().scene_info().scene_id(), &sceneID);
    if (!controller->FindSceneByID(sceneID))
    {
        LOG(ERROR) << "[Layout::OutputThumbnail::doHandle] scene not exist, sceneID: " << sceneID;
        return false;
    }

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_info().canvas_id(), &canvasID);
    controller->OutputThumbnail((LIVE_MODE)req.canvas_info().scene_info().layout_mode(), sceneID, canvasID, req.file_path(), (IMAGE_FORMAT)req.file_format(), 0);

    return true;
}

bool Layout::HasVisualIsClipping::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
    return true;
}
} // namespace MediaSDK