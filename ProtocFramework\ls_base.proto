// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

//////// BASE Message Manage -- 1.visual 2.filter 3.output stream ... ////////
package ls_base;
import "ls_basicenum.proto";

/*
 * Base Class of Request Event 
 * Need Request req_info = 1; variable for record request timestamp
 */
message Request {
	uint64			timestamp = 1;
	string			callid = 2;
	optional uint32 version = 3;
	string			name = 4;
	optional Debug  debug_info = 5;
}

message Response {
	bool result = 1;
	ls_basicenum.ERROR_TYPE error_code = 2;
	string name = 3;					// name of Response
	string callid = 4;
	string req_name = 5;				// who trigger Response
	optional uint32 version = 6;		// set by user
	optional string error_message = 7;	// set by user
	optional Debug debug_info = 8;		// if json_body is true, parse return value like json string
}

// Get Interface info about mediasdk
message Reflection {
	message Request {
	}

	message Response {
		// return all available routeID dynamically.
		// can be used to generate other call
		repeated string RouteID = 1;
	}
}

message CommonResponse {
}

message Debug {
	bool print_log = 1;
	bool print_dbgview = 2;
	bool print_cost = 3;
	bool json_body = 4;				// Request/Response serialize/deserialization by json, just for Native Debug
	optional bool print_args = 5;	// debug with param
	optional sint32 frequency = 6;	// print log frequency
}

message Event {
	string				name = 1; 		// // the type of body. would be name of Response (if response were not empty) or callback route name
	uint64				timestamp = 2;
	optional Response	response = 3;
}

message RectL {
	sint64 left = 1;
	sint64 top = 2;
	sint64 width =3;
	sint64 height = 4;
}

message RectF {
	float left = 1;
	float top = 2;
	float width =3;
	float height = 4;
}

message SizeS {
	sint32 x = 1;
	sint32 y = 2;
}

message SizeF {
	float x = 1;
	float y = 2;
}

message PointS {
	sint32 x = 1;
	sint32 y = 2;
}

message PointF {
	float x = 1;
	float y = 2;
}

message ScaleS {
	sint32 x = 1;
	sint32 y = 2;
}

message ScaleF {
	float x = 1;
	float y = 2;
}

message TranslateS {
	sint32 x = 1;
	sint32 y = 2;
}

message TranslateF {
	float x = 1;
	float y = 2;
}

message ClipS {
	sint32 x = 1;
	sint32 y = 2;
	sint32 z = 3;
	sint32 w = 4;
}

message ClipF {
	float x = 1;
	float y = 2;
	float z = 3;
	float w = 4;
}

message RectangleF {
	float x = 1;
	float y = 2;
	float z = 3;
	float w = 4;
}
message RectangleS {
	sint32 x = 1;
	sint32 y = 2;
	sint32 z = 3;
	sint32 w = 4;
}

message Rational {
	float x = 1;
	float y = 2;
}

message Transform {
	optional bool flip_h = 1;
	optional bool flip_v = 2;
	optional float angle = 3;
	optional ScaleF scale = 4;
	optional TranslateF translate = 5;
	optional ScaleF min_scale = 6;
	optional ClipF clip = 7;
	optional SizeF size = 8;
	optional ScaleF max_scale = 9;
}

message ColorConfig {
	ls_basicenum.COLOR_SPACE color_space = 1;
	ls_basicenum.VIDEO_RANGE color_range = 2;
	ls_basicenum.COLOR_TRANSFER color_transfer = 3;
}

message VisualView {
	uint64	   hwnd = 1;
	RectangleS rect = 2;
	bool	   show = 3;
}

message MaterialParam {
	string path = 1;  // support url
	SizeF  size = 2;  // resolution
	optional sint32 duration = 3;
	optional bool seekable = 4;
	optional bool loop = 5;
	optional bool gif = 6;
	optional float angle = 7;
}

message WindowParam {
	uint64 hwnd = 1;
	uint64 pid = 2;
	string class_name = 3;
	string execute_name = 4;
	string title_name = 5;
	string icon_base64 = 6;
	SizeF  window_size = 7;
	optional bool compatiable = 8;
	optional bool show_cursor = 9;
	optional bool minimized = 10;
}

message DShow {
	string id = 1;			// Device id, get by system, open video and audio
	string name = 2;		// Device name, include ls_camera ls_analog and microphone
	optional string exe = 3;     // Device exe
}

message VideoCaptureFormat {
	ls_basicenum.VIDEO_PIXEL_FORMAT format = 1;
	SizeF size = 2;
	float rate = 3;
	float min_rate = 4;
	float max_rate = 5;
}

message AudioCaptureFormat {
	ls_basicenum.ANALOG_RENDER_TYPE render_type = 1;
	sint32 channels = 2;
	sint32 sample_rate = 3;
	sint32 bits_per_sample = 4;
}

message EffectConfig {
	// <effect path>[;<key>;<value>]
	repeated string composer = 1;
	string backgroundkey = 2;
	string backgroundpath = 3;
}