﻿#pragma once

#include <cstdint>
#include <map>
#include <memory>
#include <mutex>
#include <chrono>
#include <vector>

#include "LSPublicHeader.h"
#include "icief.h"
#include "ciefhelper.h"
#include <condition_variable>
#include <deque>

#include "ColorPickerWatch.h"
#include "FullscreenDetector.h"
#include "MediaSDKControllerV2Impl.h"

#define EFFECT_ENABLE_TIME 30

namespace media_mgr
{
	struct EffectContext
	{
		bool enable_effect = false;
		bool enable_effect_operation_done = false;
		std::chrono::steady_clock::time_point last_enable_effect_tp = std::chrono::steady_clock::now() - std::chrono::seconds(EFFECT_ENABLE_TIME);
	};
	using EffectContextPtr = std::shared_ptr<EffectContext>;

	struct VideoModelContext
	{
		uint32_t   video_model;
		VideoParam video_param;

		float preview_x;
		float preview_y;
		float preview_width;
		float preview_height;
		float layout_preview_x;
		float layout_preview_y;
		float layout_preview_width;
		float layout_preview_height;

		std::vector<std::string> layer_id_order_list; // first is top
		bool show_canvas = true;
	};
	using VideoModelContextPtr = std::shared_ptr<VideoModelContext>;

	class MediaMgr : public ITimerHandler
	{
	public:
		MediaMgr();
		~MediaMgr();

		// App
		bool Initialize(INITIALIZE_INFO info);
		void Uninitialize();
		
		bool CreateFullScreenDetector(const std::string& detector_id);
		bool DestroyFullScreenDetector(const std::string& detector_id);
		bool SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exe_names);
		bool StartColorPicker(HWND hwnd);
		// Effect platform
		bool InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect);
		bool UpdateEffectConfig(const std::string& user_id, const std::string& ttls_hardware_level);
		// VideoModel
		bool SetModelLayout(UINT video_model, const Gdiplus::RectF& rect, const Gdiplus::RectF& layout_rect, const Gdiplus::SizeF& outputSize, HWND hwnd_parent = NULL);
		bool ShowModel(uint32_t video_model, bool show);
		bool DestroyModel(uint32_t model);
		bool EnableAllPreview(bool enable);
		bool EnablePreviewByVideoModel(UINT32 video_model, bool enable);
		bool SetDisplay(uint32_t video_model, bool show);
		bool GetDisplay(uint32_t model);
		// Layer
		bool AddLayer(const uint64_t canvas_id, const LAYER_INFO& layer);
		bool RemoveLayer(const uint64_t layer_id);
		bool ControlLayer(const LAYER_INFO& layer_info, LAYER_CONTROL_CMD cmd);
		// Audio
		bool AddAudio(const AUDIO_INFO& info);
		bool RemoveAudio(const uint64_t audio_id);
		bool ControlAudio(const AUDIO_CONTROL_INFO& info, bool isEraseAudio = false);
		bool GetAudioInfo(const std::string& audio_id, AUDIO_INFO* info, AUDIO_INFO_CMD cmd);
		// Filter
		bool AddFilter(const UINT64 media_id, const FILTER& info);
		bool RemoveFilter(const UINT64 media_id, const FILTER& info);
		bool ControlFilter(const UINT64 media_id, const FILTER& info, FILTER_CONTROL_CMD cmd);
		bool GetFilterInfo(const std::string& filter_id, FILTER* info, FILTER_INFO_CMD cmd);

		// Mix
		bool SetMixParameter(const uint32_t video_model_id, const VIDEO_MIX_PARAM& param);

		// VideoQualityManager
		bool VideoQualityManagerInitialize(const VideoQualityManagerInitializeParam& param, VideoQualityManagerGoLiveParamsOut* default_go_live_params);
		bool VideoQualityManagerQueryCameraRecommendedParams(const VideoQualityManagerQueryCameraRecommendedParamsRequest& request, VideoQualityManagerQueryCameraRecommendedParamsResponse* response);
		bool VideoQualityManagerQueryGoLiveRecommendedParams(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& request, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* response);
		bool VideoQualityManagerQueryManuallySelectedResult(const VideoQualityManagerQueryManuallySelectedResultRequest& request, VideoQualityManagerQueryManuallySelectedResultResponse* response);
		bool VideoQualityManagerQueryCameraBestParamsForTarget(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& request, VideoQualityManagerQueryCameraBestParamsForTargetResponse* response);
		bool ReconfigVideoOutput(const VideoOutputParamsRequest& request, VideoOutputParamsResponse* response);
		bool FallbackVideoEncoder(const FallbackVideoEncoderParamsRequest& request);
		bool SetPreprocessDefaultSize(const std::string& visual_id, int cx, int cy);
		bool RemovePreprocessDefaultSize(const std::string& visual_id);
		bool StartAdaptiveGearStrategyReport(const std::string& stream_id, const std::string& abr_config);

		void SetEnableTransition(bool enable_transition);
		bool GetEnableTransition();

	private:
		MediaMgr(const MediaMgr&);
		MediaMgr(MediaMgr&&);
		MediaMgr& operator=(const MediaMgr&);
		MediaMgr& operator=(MediaMgr&&);

		void         StartTimer(void* param);
		void         StopTimer();
		void         OnTimer();
		virtual void HandleTimer(UINT64 id) override;
		void         LogMessageHandler(int32_t severity, const char* szFile, int32_t line, const char* szText);
		
	private:
		VideoModelContextPtr GetModelCtx(const uint32_t video_model);
		bool				 SetVideoParam(sdk_helper::MediaSDKControllerImplV2* sdk, VideoModelContextPtr video_model_ctx);
		bool                 EnableEffect(const std::string& layer_id, bool enable);

		static void			 InitDefaultCanvasContext(VideoModelContext* ctx);
		static bool			 SetPreviewPosition(sdk_helper::MediaSDKControllerImplV2* sdk, VideoModelContextPtr video_model_ctx);

	private:
		void OnLayerCreated(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason);
		void OnLayerDeleted(const std::string& visual_id);
		void OnLayerFallback(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason, bool fallbackToPlaceHolder = false);
		void OnFullScreenDetectorCallback(const std::string& detector_id, const bool found_target, const FullScreenDetector::WindowInfo& info);
		void OnThreadMonitorEvent(MONITOR_THREAD_ID thread_id, MONITOR_THREAD_EVENT_TYPE event_type);

	private:
		sdk_helper::MediaSDKControllerImplV2*	 sdk_controller_ = nullptr;
        INITIALIZE_INFO							 init_param_;
        std::string								 app_work_dir_ = "";
		std::atomic_bool						 init_sdk_ = false;
		std::atomic_bool						 init_api_ = false;
		std::atomic_bool						 init_finish_ = false;
		std::mutex								 model_ctx_map_mutex_;
		std::mutex                               effect_ctx_map_mutex_;
		std::map<uint64_t, bool>				 cache_video_model_show_map_;
		std::map<uint64_t, bool>                 cache_video_model_display_map_;
		std::map<uint32_t, VideoModelContextPtr> model_ctx_map_;
		std::map<std::string, EffectContextPtr>  effect_ctx_map_;
        LS::ThreadTaskManager                    task_mgr_;
        uint64_t                                 timer_id_counter_ = 1;
        uint64_t                                 timer_id_ = 0;
        bool                                     all_canvas_enable_preview_ = true;
        bool                                     enable_nvidia_driver_hang_recover_ = false;
        FullScreenDetector                       full_screen_detector_;
        ColorPickerWatch                         color_picker_watch_;
        bool                                     enable_transition_ = false;
	};
} // namespace media_mgr