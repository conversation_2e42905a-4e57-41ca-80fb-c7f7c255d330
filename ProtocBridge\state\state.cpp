#include "stdafx.h"
#include "state.h"
#include "visual/visual.h"
#include "../BaseLib/event_bus.h"

namespace LS
{
State::RequestList State::GetRequestHandlers() const
{
    RequestList list;
    return list;
}

void State::ModuleCreate()
{
    // Initialize
    eventbus::EventBus::Subscribe([](const InitEvent& event) {
        ls_state::Callback::OnInitEvent result{};
        result.set_success(event.success);
        result.set_dx_error(event.dxError);
        result.set_pid(event.pid);
        PB::Sender::GetInstance().Send(result);
    });

    // Visual
	eventbus::EventBus::Subscribe([](const VibeTransformAnimationFinishedEvent& event) {
		ls_state::Callback::Visual::OnVisualVibeTransformAnimationFinishedEvent result{};
		result.set_visual_id(event.visualID);
		result.set_action_id(event.actrionID);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const VibeVisibleAnimationFinishedEvent& event) {
		ls_state::Callback::Visual::OnVisualVibeVisibleAnimationFinishedEvent result{};
		result.set_visual_id(event.visualID);
		result.set_action_id(event.actionID);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const CreateVisualEvent& event) {
		ls_state::Callback::Visual::OnVisualCreatedEvent result{};
		result.set_visual_id(event.visualID);
		result.set_success(event.success);
		result.set_reason(static_cast<ls_basicenum::CREATE_SOURCE_FAILED_REASON>(event.reason));
		result.set_error_code(event.errCode);
		result.set_visual_type(static_cast<ls_visual::VISUAL_TYPE>(event.type));
		PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const FallbackVisualEvent& event) {
        ls_state::Callback::Visual::OnVisualFallbackEvent result{};
        result.set_visual_id(event.visualID);
        result.set_success(event.success);
        result.set_reason(static_cast<ls_basicenum::CREATE_SOURCE_FAILED_REASON>(event.reason));
        result.set_error_code(event.errCode);
        result.set_visual_type(static_cast<ls_visual::VISUAL_TYPE>(event.type));
        result.set_fallback_to_placeholder(event.fallbackToPlaceHolder);
        PB::Sender::GetInstance().Send(result);
        });

    eventbus::EventBus::Subscribe([](const VisualReadyEvent& event) {
        ls_state::Callback::Visual::OnVisualReadyEvent result{};
        result.set_visual_id(event.visualID);
        ls_base::SizeF size{};
        size.set_x(event.size.Width);
        size.set_y(event.size.Height);
        result.mutable_size()->CopyFrom(size);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const DeleteVisualEvent& event) {
        ls_state::Callback::Visual::OnVisualDeletedEvent result{};
        result.set_visual_id(event.visualID);
        result.set_success(event.success);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const SelectVisualEvent& event) {
        ls_state::Callback::Visual::OnSelectChangeEvent result{};
        result.set_visual_id(event.visualID);
        result.set_video_model_id(event.videoModel);
        result.set_manual(event.manual);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const TransformChangedEvent& event) {
        ls_state::Callback::Visual::OnVisualTransformChangedEvent result{};
        result.set_visual_id(event.visualID);
        ls_base::Transform transform{};
        transform.set_angle(event.transform.angle);
        transform.set_flip_h(event.transform.hFlip);
        transform.set_flip_v(event.transform.vFlip);
        ls_base::TranslateF translate{};
        translate.set_x(event.transform.translate.X);
        translate.set_y(event.transform.translate.Y);
        transform.mutable_translate()->CopyFrom(translate);
        ls_base::ScaleF scale{};
        scale.set_x(event.transform.scale.X);
        scale.set_y(event.transform.scale.Y);
        transform.mutable_scale()->CopyFrom(scale);
        ls_base::ScaleF min_scale{};
        min_scale.set_x(event.transform.minScale.X);
        min_scale.set_y(event.transform.minScale.Y);
        transform.mutable_min_scale()->CopyFrom(min_scale);
        ls_base::ClipF clip{};
        clip.set_x(event.transform.clipRange.x);
        clip.set_y(event.transform.clipRange.y);
        clip.set_z(event.transform.clipRange.z);
        clip.set_w(event.transform.clipRange.w);
        transform.mutable_clip()->CopyFrom(clip);
        result.mutable_transform()->CopyFrom(transform);
        ls_base::SizeF canvas_size{};
        canvas_size.set_x(event.canvasSize.Width);
        canvas_size.set_y(event.canvasSize.Height);
        result.mutable_canvas_size()->CopyFrom(canvas_size);
        ls_base::SizeF visual_size{};
        visual_size.set_x(event.visualSize.Width);
        visual_size.set_y(event.visualSize.Height);
        result.mutable_visual_size()->CopyFrom(visual_size);
        result.set_layout(static_cast<ls_basicenum::VISUAL_LAYOUT>(event.layout));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const BeginTrackEvent& event) {
        ls_state::Callback::Visual::OnBeginTrackEvent result{};
        result.set_visual_id(event.layerID);
        result.set_hit_pos(static_cast<ls_state::CURSOR_HIT_POSITION>(event.hitPos));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const EndTrackEvent& event) {
        ls_state::Callback::Visual::OnEndTrackEvent result{};
        result.set_visual_id(event.layerID);
        result.set_hit_pos(static_cast<ls_state::CURSOR_HIT_POSITION>(event.hitPos));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ClipMaskEndEvent& event) {
        ls_state::Callback::Visual::OnClipMaskEndEvent result{};
        result.set_visual_id(event.layerID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const MouseClickEvent& event) {
        ls_state::Callback::Visual::OnMouseClickEvent result{};
        result.set_type(static_cast<ls_state::MOUSE_CLICK_EVENT>(event.type));
        result.set_visual_id(event.visualID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const MouseDoubleClickEvent& event) {
        ls_state::Callback::Visual::OnMouseDoubleClickEvent result{};
        result.set_visual_id(event.layerID);
        result.set_video_model_id(event.videoModel);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const StateChangeEvent& event) {
        ls_state::Callback::Visual::OnStateChangeEvent result{};
        result.set_visual_id(event.layerID);
        result.set_state(event.state);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ShortcutActionEvent& event) {
        ls_state::Callback::Visual::OnShortcutActionEvent result{};
        result.set_visual_id(event.layerID);
        result.set_action(static_cast<ls_state::SHORT_CUT_ACTION>(event.action));
        PB::Sender::GetInstance().Send(result);
    });

    // Camera
    eventbus::EventBus::Subscribe([](const ColorRangeDetectEvent& event) {
        ls_state::Callback::Visual::OnColorRangeDetectEvent result{};
        result.set_visual_id(event.layerID);
        result.set_result(static_cast<ls_basicenum::VIDEO_RANGE>(event.result));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const CameraControlEvent& event) {
        ls_state::Callback::Visual::OnCameraControlEvent result{};
        result.set_visual_id(event.layerID);
        result.set_control_type(static_cast<ls_camera::CAMERA_CONTROL_TYPE>(event.type));
        result.set_state(event.state);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const CameraOpenErrorEvent& event) {
        ls_state::Callback::Visual::OnCameraOpenErrorEvent result{};
        result.set_visual_id(event.layerID);
        result.set_error_type(static_cast<ls_state::CAMERA_OPEN_ERROR_TYPE>(event.errorType));
        result.set_error_code(event.errorCode);
        PB::Sender::GetInstance().Send(result);
    });

    // Game
    eventbus::EventBus::Subscribe([](const GameStateEvent& event) {
        ls_state::Callback::Visual::OnGameEvent result{};
        result.set_visual_id(event.layerID);
        result.set_capture_event(static_cast<ls_state::GAME_CAPTURE_EVENT_TYPE>(event.type));
        result.set_ts(event.timestamp);
        result.set_info(event.info);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const GameSourceProcessCrashDetectEvent& event) {
        ls_state::Callback::Visual::OnGameSourceProcessCrashDetectEvent result{};
        result.set_visual_id(event.visualID);
        result.set_exe_name(event.exeName);
        result.set_exit_code(event.exitCode);
        result.set_exited(event.exited);
        result.set_pid(event.pid);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const FullScreenDetectorEvent& event) {
        ls_state::Callback::Visual::OnFullScreenDetectorEvent result{};
        result.set_detector_id(event.detectorID);
        result.set_found_target(event.foundTarget);
        result.set_win_id(event.winID);
        result.set_class_name(event.className);
        result.set_exe_name(event.exeName);
        result.set_pid(event.pid);
        PB::Sender::GetInstance().Send(result);
    });

    // Graffiti
    eventbus::EventBus::Subscribe([](const TracksInfoEvent& event) {
        ls_state::Callback::Visual::OnTracksInfoChangeEvent result{};
        result.set_visual_id(event.layerID);
        result.set_tracks_info(event.tracksInfo);
        PB::Sender::GetInstance().Send(result);
    });

	// Bytelink
	eventbus::EventBus::Subscribe([](const CreateCastMateEvent& event) {
		ls_state::Callback::Visual::OnCastMateEvent result{};
		result.set_castmate_event(static_cast<ls_state::CASTMATE_EVENT_TYPE>(event.eventType));
		result.set_event_code(event.eventCode);
		result.set_protocol(static_cast<ls_bytelink::CASTMATE_PROTOCOL_TYPE>(event.protocolType));
		result.set_msg(event.msg);
		PB::Sender::GetInstance().Send(result);
	});

    // Fav
    eventbus::EventBus::Subscribe([](const FavStateEvent& event) {
        ls_state::Callback::Visual::OnFAVEvent result{};
        result.set_visual_id(event.visualID);
        result.set_event_type(static_cast<ls_state::FAV_EVENT_TYPE>(event.type));
        result.set_is_animation(event.isAnimation);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const VisualCaptureTypeChangeEvent& event) {
        ls_state::Callback::Visual::OnVisualCaptureTypeChangeEvent result{};
        result.set_visual_id(event.layerID);
        result.set_type(static_cast<ls_state::VISUAL_CAPTURE_TYPE>(event.type));
        result.set_capture_pid(event.capPID);
        result.set_capture_hwnd(event.capHwnd);
        PB::Sender::GetInstance().Send(result);
    });

    // PhoneCamera
    eventbus::EventBus::Subscribe([](const PhoneCameraOnStartResultEvent& event) {
        ls_state::Callback::Visual::OnStartResult result{};
        result.set_err_code(event.err_code);
        result.set_ip_list(event.ip_list);
        result.set_port(event.port);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnConnectEvent& event) {
        ls_state::Callback::Visual::OnConnect result{};
        result.set_err_code(event.err_code);
        auto info = result.mutable_info();
        info->set_type((ls_phonecamera::PHONECAMERA_DEVICE_TYPE)event.device_type);
        info->set_devicekey(event.deviceKey);
        info->set_hostname(event.name);

        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnCameraReadyResultEvent& event) {
        ls_state::Callback::Visual::OnCameraReadyResult result{};
        result.set_err_code(event.err_code);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnClosedEvent& event) {
        ls_state::Callback::Visual::OnClosed result{};
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnDisconnectEvent& event) {
        ls_state::Callback::Visual::OnDisconnect result{};
        result.set_err_code(event.err_code);
        result.set_msg(event.msg);
        result.set_device_key(event.deviceKey);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnDeiviceFoundEvent& event) {
        ls_state::Callback::Visual::OnDeiviceFound result{};
        result.set_err_code(event.err_code);
        result.set_opt((ls_state::DEVICEFOUNDOPT)event.opt);

        auto info = result.mutable_info();
        info->set_type((ls_phonecamera::PHONECAMERA_DEVICE_TYPE)event.device_type);
        info->set_devicekey(event.deviceKey);
        info->set_hostname(event.name);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnErrorFoundEvent& event) {
        ls_state::Callback::Visual::OnError result{};
        result.set_err_code(event.err_code);
        result.set_msg(event.msg);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PhoneCameraOnRecvMetaDataFoundEvent& event) {
        ls_state::Callback::Visual::OnRecvMetaData result{};
        result.set_src(event.src);
        result.set_msg(event.msg);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const CreateFrameEvent& event) {
        ls_state::Callback::Visual::OnCreateFrameStatue result{};
        result.set_frame_id(event.frameID);
        ls_base::SizeF frameSize;
        frameSize.set_x(event.frameSize.Width);
        frameSize.set_y(event.frameSize.Height);
        result.mutable_frame_size()->CopyFrom(frameSize);
        result.set_err_code(event.errCode);
        result.set_err_msg(event.errMsg);
        PB::Sender::GetInstance().Send(result);
    });

    // Audio
    eventbus::EventBus::Subscribe([](const AudioPeakEvent& event) {
        ls_state::Callback::Audio::OnAudioPeakEvent result{};
        for (const auto& info : event.infos)
        {
            auto ref = result.add_info();
            ref->set_audio_id(info.audioID);
            ref->set_istrack(info.isTrack);
            for (const auto& peakVal : info.peak)
            {
                ref->add_peak(peakVal);
            }
            if (!info.isTrack)
            {
				for (const auto& devicePeakVal : info.devicePeak)
				{
					ref->add_device_peak(devicePeakVal);
				}
            }
        }
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDefaultDeviceChangeEvent& event) {
        ls_state::Callback::Audio::OnAudioDefaultChangeEvent result{};
        result.set_device_id(event.deviceID);
        result.set_data_flow(static_cast<ls_state::AUDIO_DATA_FLOW>(event.dataFlow));
        result.set_device_role(static_cast<ls_state::AUDIO_DEVICE_ROLE>(event.deviceRole));
        result.set_device_name(event.deviceName);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDeviceStateChangeEvent& event) {
        ls_state::Callback::Audio::OnAudioDeviceStateChangeEvent result{};
        result.set_device_id(event.deviceID);
        result.set_device_state(static_cast<ls_state::AUDIO_DEVICE_STATE>(event.deviceState));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDeviceAddEvent& event) {
        ls_state::Callback::Audio::OnAudioDeviceAddEvent result{};
        result.set_device_id(event.deviceID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDeviceRemoveEvent& event) {
        ls_state::Callback::Audio::OnAudioDeviceRemoveEvent result{};
        result.set_device_id(event.deviceID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDevicePropertyChangeEvent& event) {
        ls_state::Callback::Audio::OnAudioDevicePropertyChangeEvent result{};
        result.set_device_id(event.deviceID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioCapableAppChangedEvent& event) {
        ls_state::Callback::Audio::OnAudioCapableAppChangedEvent result{};
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PCMAudioEOFEvent& event) {
        ls_state::Callback::Audio::OnPCMAudioEOFEvent result{};
        result.set_audio_id(event.audioID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const PCMAudioBreakEvent& event) {
        ls_state::Callback::Audio::OnPCMAudioBreakEvent result{};
        result.set_audio_id(event.audioID);
        result.set_left_sample_cnt(event.leftSampleCnt);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const EchoDetectionResultEvent& event) {
        ls_state::Callback::Audio::OnEchoDetectionResultEvent result{};
        result.set_probability(event.probability);
        PB::Sender::GetInstance().Send(result);
    });

	eventbus::EventBus::Subscribe([](const WASAPIDeviceInfoEvent& event) {
		ls_state::Callback::Audio::OnWASAPIDeviceInfoEvent result{};
		result.set_device_id(event.deviceID);
        result.set_audio_id(event.audioID);
        result.set_type(static_cast<ls_state::DEVICE_TRANSPORT_TYPE>(event.type));
        result.set_mic(event.mic);
        result.set_speaker(event.speaker);
        result.set_system_mute(event.systemMute);
        result.set_system_capture_volume(event.systemCaptureVolume);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const WASAPIEnableRawDataModeFailedEvent& event) {
		ls_state::Callback::Audio::OnWASAPIEnableRawDataModeFailedEvent result{};
		result.set_device_id(event.deviceID);
        result.set_audio_id(event.audioID);
        result.set_hresult(event.hresult);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const WASAPIGetMicrophoneBoostFailedEvent& event) {
		ls_state::Callback::Audio::OnWASAPIGetMicrophoneBoostFailedEvent result{};
		result.set_device_id(event.deviceID);
		result.set_audio_id(event.audioID);
		result.set_hresult(event.hresult);
		PB::Sender::GetInstance().Send(result);
	});

    eventbus::EventBus::Subscribe([](const AudioBufferMisalignmentEvent& event) {
        ls_state::Callback::Audio::OnAudioBufferMisalignmentEvent result{};
        result.set_audio_id(event.audioID);
        result.set_too_early(event.tooEarly);
        result.set_too_late(event.tooLate);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioFailedStatusEvent& event) {
        ls_state::Callback::Audio::OnAudioFailedStatusEvent result{};
        result.set_audio_id(event.audioID);
        result.set_error_code(event.errCode);
        result.set_error_msg(event.errMsg);
        result.set_audio_type((ls_audio::AUDIO_TYPE)event.audioType);
        result.set_hresult(event.hresult);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioSourceFormatEvent& event) {
        ls_state::Callback::Audio::OnAudioSourceFormatEvent result{};
        result.set_audio_id(event.audioID);
        result.set_format((ls_basicenum::AUDIO_FORMAT)event.format);
        result.set_channel(event.channel);
        result.set_sample_rate(event.sampleRate);
        result.set_layout((ls_basicenum::AUDIO_CHANNEL_LAYOUT)event.layout);
        result.set_block_size(event.blockSize);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioSourceErrorFormatEvent& event) {
        ls_state::Callback::Audio::OnAudioSourceErrorFormatEvent result{};
        result.set_audio_id(event.audioID);
        result.set_channel(event.channel);
        result.set_channel_mask(event.channelMask);
        result.set_sample_rate(event.sampleRate);
        result.set_bits_per_sample(event.bitsPerSample);
        PB::Sender::GetInstance().Send(result);
        });

    eventbus::EventBus::Subscribe([](const AudioSystemDeviceVolumeEvent& event) {
        ls_state::Callback::Audio::OnAudioSystemDeviceVolumeEvent result{};
        result.set_system_capture_volume(event.system_capture_volume);
        result.set_system_mute(event.system_mute);
        result.set_audio_id(event.audioID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioSourceWarningEvent& event) {
        ls_state::Callback::Audio::OnAudioSourceWarningEvent result{};
        result.set_audio_id(event.audioID);
        result.set_warning_code((ls_state::AUDIO_SOURCE_WARNING_TYPE)event.warningCode);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioLyraxRawDataResultEvent& event) {
        ls_state::Callback::Audio::OnAudioLyraxRawDataResult result{};
        result.set_raw_data_api_option(event.raw_data_api_option);
        result.set_raw_data_api_decision_option(event.raw_data_api_decision_option);
        result.set_apply_raw_data_option(event.apply_raw_data_option);
        result.set_apply_result(event.apply_result);
        result.set_system_state(event.system_state);
        result.set_audio_id(event.audioID);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const AudioDeviceCaptureRateCheckResultEvent& event) {
        ls_state::Callback::Audio::OnAudioDeviceCaptureRateCheckResult result{};
        result.set_raw_rate(event.raw_rate);
        result.set_diff_rate(event.diff_rate);
        result.set_audio_id(event.audioID);
        PB::Sender::GetInstance().Send(result);
    });

    // Filter
	eventbus::EventBus::Subscribe([](const EffectStateEvent& event) {
		ls_state::Callback::Filter::OnEffectStateEvent result{};
		result.set_visual_id(event.layerID);
        result.set_valid(event.valid);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const EffectResourceLoadEvent& event) {
		ls_state::Callback::Filter::OnEffectResourceLoadEvent result{};
		result.set_visual_id(event.visualID);
		result.set_msg_id(event.msgID);
        result.set_arg1(event.arg1);
        result.set_arg2(event.arg2);
        result.set_arg3(event.arg3);
        result.set_effect_first_frame_elapsed_time(event.effectFirstFrameElapsedTime);
        result.set_effect_post_first_frame_elapsed_time(event.effectPostFirstFrameElapsedTime);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const EffectMsgEvent& event) {
		ls_state::Callback::Filter::OnEffectMsgEvent result{};
		result.set_visual_id(event.visualID);
		result.set_msg_id(event.msgID);
		result.set_arg1(event.arg1);
		result.set_arg2(event.arg2);
		result.set_arg3(event.arg3);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const EffectPlatformEvent& event) {
		ls_state::Callback::Filter::OnEffectPlatformEvent result{};
		result.set_serialized_json(event.serializedJson);
		PB::Sender::GetInstance().Send(result);
	});

	eventbus::EventBus::Subscribe([](const CommonMetricsEvent& event) {
		ls_state::Callback::Filter::OnCommonMetricsEvent result{};
		result.set_filter_id(event.filterID);
        result.set_common_metrics(event.commonMetrics);
        for (const auto& id : event.mediaIDs)
        {
            auto ref = result.add_audio_ids();
            *ref = id;
        }
		PB::Sender::GetInstance().Send(result);
	});

    eventbus::EventBus::Subscribe([](const EffectGLVersionEvent& event) {
        ls_state::Callback::Filter::OnEffectGLVersionEvent result{};
        result.set_version(event.version);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const EffectLagEvent& event) {
        ls_state::Callback::Filter::OnEffectLagEvent result{};
        result.set_visual_id(event.visual_id);
        result.set_effect_info(event.effect_info);
        result.set_is_first_frame(event.is_first_frame);
        result.set_time(event.time);
        result.set_cost(event.cost);
        result.set_type(event.type);
        PB::Sender::GetInstance().Send(result);
    });

    // Canvas
    eventbus::EventBus::Subscribe([](const MouseEnterEvent& event) {
        ls_state::Callback::Canvas::OnMouseHoverEvent result{};
        result.set_video_model_id(event.videoModel);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const MouseLeaveEvent& event) {
        ls_state::Callback::Canvas::OnMouseLeaveEvent result{};
        result.set_video_model_id(event.videoModel);
        PB::Sender::GetInstance().Send(result);
    });

    // RTMP
    eventbus::EventBus::Subscribe([](const ABRBitrateChangeEvent& event) {
        ls_state::Callback::RTMP::OnABRBitrateChangeEvent result{};
        result.set_stream_id(event.streamID);
        result.set_cur_bitrate(event.curBitrate);
        result.set_prev_bitrate(event.prevBitrate);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const StreamEvent& event) {
        ls_state::Callback::RTMP::OnStreamEvent result{};
        result.set_stream_id(event.streamID);
        result.set_action(static_cast<ls_state::STREAM_OUTPUT_ACTION>(event.action));
        result.set_code(static_cast<ls_state::STREAM_OUTPUT_CODE>(event.code));
        result.set_extra(event.extra);
        result.set_time_epoch(event.timeEpoch);
        result.set_cur_type(static_cast<ls_rtmp::STREAM_TYPE>(event.curType));
        result.set_fallback_type(static_cast<ls_rtmp::STREAM_TYPE>(event.fallbackType));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const StreamEncodeEvent& event) {
        ls_state::Callback::RTMP::OnStreamEncodeEvent result{};
        result.set_stream_id(event.streamID);
        result.set_json_info(event.jsonInfo);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const BandwidthEvent& event) {
        ls_state::Callback::RTMP::OnBandwidthEvent result{};
        result.set_stream_id(event.streamID);
        result.set_stream_type(static_cast<ls_rtmp::STREAM_TYPE>(event.streamType));
        result.set_average_transport_bitrate(event.averageTransportBitrate);
        result.set_prob_rtt(event.probRtt);
        result.set_prob_bandwidth(event.probBandwidth);
        result.set_total_sends(event.totalSends);
        result.set_total_drops(event.totalDrops);
        result.set_total_duration(event.totalDuration);
        result.set_total_send_duration(event.totalSendDuration);
        result.set_result(static_cast<ls_rtmp::RTMP_UPLOAD_SPEED_TEST_RESULT>(event.result));
        result.set_failed_reason(static_cast<ls_rtmp::RTMP_UPLOAD_SPEED_TEST_FAIL_REASON>(event.failedReason));
        PB::Sender::GetInstance().Send(result);
    });

    // RTC
    eventbus::EventBus::Subscribe([](const JoinChannelEvent& event) {
        ls_state::Callback::RTC::OnJoinChannel result{};
        result.set_channel(event.channel);
        result.set_uid(event.uid);
        result.set_error_code(event.errorCode);
        result.set_first_time(event.firstTime);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const LeaveChannelEvent& event) {
        ls_state::Callback::RTC::OnLeaveChannel result{};
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteEnterEvent& event) {
        ls_state::Callback::RTC::OnRemoteEnter result{};
        result.set_uid(event.uid);
        result.set_elapsed(event.elapsed);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteLeaveEvent& event) {
        ls_state::Callback::RTC::OnRemoteLeave result{};
        result.set_uid(event.uid);
        result.set_elapsed(static_cast<ls_state::REMOTE_LEAVE_REASON>(event.elapsed));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const EngineStartEvent& event) {
        ls_state::Callback::RTC::OnEngineStart result{};
        result.set_ret(event.ret);
        result.set_error_code(event.errorCode);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const EngineStopEvent& event) {
        ls_state::Callback::RTC::OnEngineStop result{};
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RoomMessageReceivedEvent& event) {
        ls_state::Callback::RTC::OnRoomMessageReceived result{};
        result.set_uid(event.uid);
        result.set_msg(event.msg);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const StreamMixingEvent& event) {
        ls_state::Callback::RTC::OnStreamMixing result{};
        result.set_task_id(event.taskID);
        result.set_event_type(static_cast<ls_state::STREAM_MIXING_EVENT>(event.eventType));
        result.set_error(event.error);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const UserMessageReceivedEvent& event) {
        ls_state::Callback::RTC::OnUserMessageReceived result{};
        result.set_uid(event.uid);
        result.set_msg(event.msg);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteVideoSizeChangeEvent& event) {
        ls_state::Callback::RTC::OnRemoteVideoSizeChange result{};
        result.set_uid(event.uid);
        result.set_stream_index(event.streamIdx);
        result.set_width(event.width);
        result.set_height(event.height);
        result.set_rotation(event.rotation);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteFirstVideoFrameDecodedEvent& event) {
        ls_state::Callback::RTC::OnRemoteFirstVideoFrameDecoded result{};
        result.set_uid(event.uid);
        result.set_stream_index(event.streamIdx);
        result.set_width(event.width);
        result.set_height(event.height);
        result.set_rotation(event.rotation);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteFirstAudioFrameEvent& event) {
        ls_state::Callback::RTC::OnRemoteFirstAudioFrame result{};
        result.set_uid(event.uid);
        result.set_stream_index(event.streamIdx);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteFirstVideoFrameRenderEvent& event) {
        ls_state::Callback::RTC::OnRemoteFirstVideoFrameRender result{};
        result.set_uid(event.uid);
        result.set_stream_index(event.streamIdx);
        result.set_width(event.width);
        result.set_height(event.height);
        result.set_rotation(event.rotation);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const LocalAudioVolumeIndicationEvent& event) {
        ls_state::Callback::RTC::OnLocalAudioVolumeIndication result{};
        result.set_json_content(event.jsonContent);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const RemoteAudioVolumeIndicationEvent& event) {
        ls_state::Callback::RTC::OnRemoteAudioVolumeIndication result{};
        result.set_json_content(event.jsonContent);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const StreamStatsEvent& event) {
        ls_state::Callback::RTC::OnStreamStats result{};
        result.set_uid(event.uid);
        result.set_stream_index(event.streamIdx);
        result.set_video_send_target_bitrate(event.videoSendTargetBitrate);
        result.set_video_send_bitrate(event.videoSendBitrate);
        result.set_video_sent_rate(event.videoSentRate);
        result.set_video_loss_rate(event.videoLossRate);
        result.set_video_encoder_target_rate(event.videoEncoderTargetRate);
        result.set_video_encoder_rate(event.videoEncoderRate);
        result.set_local_tx_quality(event.localTxQuality);
        result.set_width(event.width);
        result.set_height(event.height);
        result.set_codec_type(event.codecType);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const NetworkStatsEvent& event) {
        ls_state::Callback::RTC::OnNetworkStats result{};
        for (const auto& info : event.infos)
        {
            auto ref = result.add_infos();
            ref->set_uid(info.uid);
            ref->set_fraction_lost(info.fractionLost);
            ref->set_rtt(info.rtt);
            ref->set_total_bandwidth(info.totalBandwidth);
            ref->set_rx_quality(info.rxQuality);
            ref->set_tx_quality(info.txQuality);
        }
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ErrorEvent& event) {
        ls_state::Callback::RTC::OnError result{};
        result.set_err(event.error);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ConnectionStateEvent& event) {
        ls_state::Callback::RTC::OnConnectionState result{};
        result.set_state(event.state);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const WarningEvent& event) {
        ls_state::Callback::RTC::OnWarning result{};
        result.set_warn(event.warning);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ConnectionLostEvent& event) {
        ls_state::Callback::RTC::OnConnectionLost result{};
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ActiveSpeakerEvent& event) {
        ls_state::Callback::RTC::OnActiveSpeaker result;
        result.set_uid(event.uid);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ForwardStreamStateInfoEvent& event) {
        ls_state::Callback::RTC::OnForwardStreamStateInfo result{};
        for (const auto& info : event.infos)
        {
            auto ref = result.add_infos();
            ref->set_room_id(info.roomID);
            ref->set_forward_state_mask(info.forwardStateMask);
            ref->set_forward_error_mask(info.forwardErrorMask);
        }
        PB::Sender::GetInstance().Send(result);
    });

    // Statistic
    eventbus::EventBus::Subscribe([](const TeaEvent& event) {
        ls_state::Callback::Statistic::OnTeaEvent result{};
        result.set_name(event.name);
        result.set_parames(event.params);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const DevLostEvent& event) {
        ls_state::Callback::Statistic::OnDevLostEvent result{};
        result.set_str(event.str);
        result.set_error(event.error);
        result.set_remove_reason(event.removeReason);
        result.set_effect(event.effect);
        result.set_driver_date(event.driverDate);
        result.set_driver_name(event.driverName);
        result.set_driver_ver(event.driverVer);
        result.set_pre_driver_date(event.preDriverDate);
        result.set_pre_driver_name(event.preDriverName);
        result.set_pre_driver_ver(event.preDriverVer);
        result.set_display_change(event.displayChange);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const VqosDataReportEvent& event) {
        ls_state::Callback::Statistic::OnVqosDataReportEvent result{};
        result.set_data(event.data);
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ThreadMonitorEvent& event) {
        ls_state::Callback::OnThreadMonitorEvent result{};
        result.set_thread_id(static_cast<ls_state::THREAD_ID_INFO>(event.threadID));
        result.set_event_type(static_cast<ls_state::THREAD_MONITOR_EVENT_TYPE>(event.eventType));
        PB::Sender::GetInstance().Send(result);
    });

    eventbus::EventBus::Subscribe([](const ColorPickerPixelEvent& event) {
        ls_state::Callback::OnColorPickerPixelEvent result{};
        result.set_color(event.color);
        PB::Sender::GetInstance().Send(result);
    });
}
} // namespace MediaSDK
