// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_canvas;
import "ls_base.proto";
import "ls_basicenum.proto";
import "ls_canvasfilter.proto";
import "ls_audio.proto";

message CanvasInfo {
	uint32 video_model = 1;
	ls_base.RectF view_rect = 2;
    ls_base.RectF layout_view_rect= 3;
	ls_base.SizeF output_size = 4;
	float fps = 5;
	uint32 bk_color = 6;
}

message CreateCanvas {
	message Request {
		CanvasInfo canvas_info = 1;
	}

	message Response {
		string canvas_id = 1;
	}
}

message DestoryCanvas {
	message Request {
		repeated string canvas_ids = 1;
	}
}

message SetCanvasInfo {
	message Request {
		string canvas_id = 1;
		CanvasInfo canvas_info = 2;
	}
}

message GetCanvasInfo {
	message Request {
		string canvas_id = 1;
	}

	message Response {
		CanvasInfo canvas_info = 1;
	}
}

message AddLayers {
	message Request {
		string canvas_id = 1;
		repeated string layer_ids = 2;
	}
}

message RemoveLayers {
	message Request {
		string canvas_id = 1;
		repeated string layer_ids = 2;
	}
}

message AddFilter {
	message Request {
		string canvas_id = 1;
		string filter_id = 2;
	}
}

message RemoveFilter {
	message Request {
		string canvas_id = 1;
		string filter_id = 2;
	}
}

message VibeLayersZOrder {
	string action_id = 1;
	repeated string layer_ids = 2;
}

message VibeLayerTransform {
	string action_id = 1;
	string layer_id = 2;
	optional uint64 duration_ms = 3;
	optional ls_canvasfilter.TRANSITION_PROGRESS_FUNCTION_TYPE transition_progress_func_type = 4;
	ls_base.Transform target_transform = 5;
	ls_basicenum.VISUAL_LAYOUT layout = 6;
	ls_base.SizeF canvas_size = 7;
}

message VibeLayerVisible {
	string action_id = 1;
	string layer_id = 2;
	bool visible = 3;
	optional uint64 duration_ms = 4;
	optional ls_canvasfilter.TRANSITION_PROGRESS_FUNCTION_TYPE transition_progress_func_type = 5;
}

message VibeLayerPreprocess {
	string layer_id = 1;
	optional bool need_preprocess = 2;
}

message VibePreprocessInfo {
	string action_id = 1;
	repeated VibeLayerPreprocess layers_preprocess = 2;
}

enum VIBE_ACTION_TYPE {
	VIBE_ACTION_TYPE_NONE = 0;
	VIBE_ACTION_TYPE_CREATE = 1;
	VIBE_ACTION_TYPE_DESTROY = 2;
	VIBE_ACTION_TYPE_UPDATE = 3;
	VIBE_ACTION_TYPE_ACTIVE = 4;
	VIBE_ACTION_TYPE_PLAY = 5;
	VIBE_ACTION_TYPE_STOP = 6;
}

message VibeFilterInfo {
	string action_id = 1;
	VIBE_ACTION_TYPE action_type = 2;
	string filter_id = 3;
	string media_id = 4;   // include [layer_id, canvas_id]
}

message VibeAudioAmbient {
	string action_id = 1;
	VIBE_ACTION_TYPE action_type = 2;
	string audio_id = 3;
	ls_audio.AudioBuffer audio_data = 4;
	ls_audio.AudioCaptureParam audio_capture = 5;
}

message VibeTriggerEffect {
	message Request {
		string canvas_id = 1;
		VibeLayersZOrder layers_zorder = 2;
		repeated VibeLayerTransform layers_transform = 3;
		repeated VibeLayerVisible layers_visible = 4;
		repeated VibeFilterInfo filter_infos = 5;
		VibePreprocessInfo preprocess_info = 6;
		VibeAudioAmbient audio_ambient = 7;
	}
}