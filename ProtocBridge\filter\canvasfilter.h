#pragma once

#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_canvasfilter.pb.h"

namespace LS
{

class CanvasFilter : public PB::Module
{
private:
    virtual RequestList GetRequestHandlers() const override;

public:
    struct Create : public PB::RequestHandler<ls_canvasfilter::Create>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct Remove : public PB::RequestHandler<ls_canvasfilter::Remove>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetCanvasFilter : public PB::RequestHandler<ls_canvasfilter::SetCanvasFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetCanvasFilter : public PB::RequestHandler<ls_canvasfilter::GetCanvasFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    static void SetCanvasFilterInfo(CANVAS_FILTER& canvasFilterInfo, const ls_canvasfilter::CanvasFilter& canvas_filter, UINT64* cmd = NULL);
};
} // namespace LS