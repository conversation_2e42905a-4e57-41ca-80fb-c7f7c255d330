#include "FrameGrab.h"

#include "graphics_helper.h"

#include <SpriteBatch.h>
#include <ScreenGrab.h>
#include <SpriteFont.h>
#include <WICTextureLoader.h>
#include <DirectXPackedVector.h>
#include <PostProcess.h>

namespace v3
{

namespace DX = DirectX;

static std::string ToString(FrameGrabErrorCodeEnum errcode)
{
    std::string errmsg;

    switch (errcode)
    {
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK:
        errmsg = "";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter:
        errmsg = "InvalidParameter";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_ProcessModeInvalidParameter:
        errmsg = "ProcessModeInvalidParameter";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_AssertFail:
        errmsg = "AssertFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateSharedTextureFail:
        errmsg = "GpuCreateSharedTextureFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureFail:
        errmsg = "GpuCreateTextureFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureSRVFail:
        errmsg = "GpuCreateTextureSRVFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureRTVFail:
        errmsg = "GpuCreateTextureRTVFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GetSharedHandleFail:
        errmsg = "GetSharedHandleFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OpenSharedHandlerFail:
        errmsg = "OpenSharedHandlerFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GetSharedTextureKeyedMutexFail:
        errmsg = "GetSharedTextureKeyedMutexFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SharedTextureKeyedMutexOpFail:
        errmsg = "SharedTextureKeyedMutexOpFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SendFail:
        errmsg = "SendFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SetRenderTargetFail:
        errmsg = "SetRenderTargetFail";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GrabFrameTimeout:
        errmsg = "GrabFrameTimeout";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_AckTimeout:
        errmsg = "AckTimeout";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_ItemNotExist:
        errmsg = "ItemNotExist";
        break;
    case v3::FrameGrabErrorCodeEnum::kFrameGrabErrorCode_UnknownError:
    default:
        errmsg = "UnknownError";
        break;
    }

    return errmsg;
}

FrameGrab::FrameGrab()
{
    clean_up_ = std::make_shared<std::atomic_int64_t>();
}

FrameGrab::~FrameGrab()
{
}

bool FrameGrab::Init(ID3D11Device* sdk_dev, ID3D11DeviceContext* sdk_ctx, ID3D11Device* dev, ID3D11DeviceContext* ctx)
{
    sdk_dev_ = sdk_dev;
    sdk_ctx_ = sdk_ctx;
    dev_ = dev;
    ctx_ = ctx;

    try
    {
        post_process_ = std::make_unique<DirectX::ToneMapPostProcess>(sdk_dev_);
    }
    catch (std::exception& e)
    {
        LOG(INFO) << StringPrintf("[FrameGrab::Init] ToneMapPostProcess init failed, reason: %s", e.what());
    }
    catch (...)
    {
        LOG(INFO) << "[FrameGrab::Init] ToneMapPostProcess init failed.";
    }

    if (post_process_)
    {
        post_process_->SetOperator(DirectX::ToneMapPostProcess::Saturate);
        post_process_->SetExposure(-1.58496250072f);
    }

    render_ = std::make_unique<DirectX::SpriteBatch>(sdk_ctx_);

    return true;
}

void FrameGrab::Uninit()
{
    {
        std::unique_lock<std::mutex> lock(ctx_map_mutex_);
        ctx_map_.clear();
    }

    {
        std::unique_lock<std::mutex> lock(send_task_map_mutex_);
        send_task_map_.clear();
    }

    render_.reset();
    post_process_.reset();
}

void FrameGrab::OnCanvasItemFilteredTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
}

void FrameGrab::OnPreviewTextureCallback(const int64_t sink_id, ID3D11Texture2D* texture)
{
}

bool FrameGrab::CorrectParam(GrabFrameTask* task, int src_width, int src_height, int32_t& clip_x,
                             int32_t& clip_y, int32_t& clip_z, int32_t& clip_w,
                             ClipResizeOrderEnum& order)
{
    if (order == ClipResizeOrderEnum::kClipResizeOrder_ClipFirst)
    {
        if (clip_x + clip_z >= src_width)
        {
            clip_x = 0;
            clip_z = 0;
        }
        if (clip_y + clip_w >= src_height)
        {
            clip_y = 0;
            clip_w = 0;
        }

        if (task->output_width <= 0 || task->output_height <= 0)
        {
            if (task->resize_width > 0 && task->resize_height > 0)
            {
                task->output_width = task->resize_width;
                task->output_height = task->resize_height;
            }
            else
            {
                task->output_width = src_width - (clip_x + clip_z);
                task->output_height = src_height - (clip_y + clip_w);
            }
        }
    }
    else if (order == ClipResizeOrderEnum::kClipResizeOrder_ResizeFirst)
    {

        bool has_resize_size = task->resize_width > 0 && task->resize_height > 0;

        int resize_w = has_resize_size ? task->resize_width : src_width;
        int resize_h = has_resize_size ? task->resize_height : src_height;

        if (clip_x + clip_z > resize_w)
        {
            clip_x = 0;
            clip_z = 0;
        }
        if (clip_y + clip_w >= resize_h)
        {
            clip_y = 0;
            clip_w = 0;
        }

        if (task->output_width <= 0 || task->output_height <= 0)
        {
            task->output_width = resize_w - clip_x - clip_z;
            task->output_height = resize_h - clip_y - clip_w;
        }
    }
    else
    {
        return false;
    }

    return true;
}

void FrameGrab::OnCanvasItemOriginalTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
    do
    {
        if (!texture)
            break;

        auto ctx = GetGrabContext(canvas_item_id);
        if (!ctx)
            break;

        ctx->last_seen_tp = std::chrono::steady_clock::now();
        ctx->last_seen_render_count = render_count_;

        D3D11_TEXTURE2D_DESC src_tex_desc;
        texture->GetDesc(&src_tex_desc);

        if (src_tex_desc.Width <= 0 || src_tex_desc.Height <= 0)
        {
            break;
        }

        std::vector<GrabFrameTaskPtr> tmp_task_list;

        {
            std::unique_lock<std::mutex> task_list_lock(ctx->task_list_mutex);
            tmp_task_list = std::move(ctx->task_list);
        }

        for (auto task : tmp_task_list)
        {

            bool grab_frame_ok = false;

            do
            {
                ClipResizeOrderEnum order = task->clip_resize_order;
                int32_t             clip_x = task->clip_x;
                int32_t             clip_y = task->clip_y;
                int32_t             clip_z = task->clip_z;
                int32_t             clip_w = task->clip_w;

                bool correct = CorrectParam(task.get(), src_tex_desc.Width, src_tex_desc.Height, clip_x, clip_y, clip_z, clip_w, order);

                if (!correct)
                {
                    task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter;
                    break;
                }

                if (!task->texture_prepared)
                {
                    bool ok = PrepareTexture(task.get(), task->errcode);
                    if (!ok)
                        break;
                }

                HRESULT hr = task->shared_tex_mutex->AcquireSync(0, 0);

                if (hr == S_OK)
                {
                    grab_frame_ok = ProcessFrameInRenderThread(
                        task->shared_tex.Get(),
                        texture,
                        order,
                        task->fit_mode,
                        clip_x,
                        clip_y,
                        clip_z,
                        clip_w,
                        task->errcode);

                    hr = task->shared_tex_mutex->ReleaseSync(1);
                }

                if (hr != S_OK)
                {
                    task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SharedTextureKeyedMutexOpFail;
                }

            } while (0);

            task->grabed_ok = grab_frame_ok;
            task->grabed = true;

            if (grab_frame_ok)
            {
                task->transfer_tp = std::chrono::steady_clock::now();
                {
                    std::unique_lock<std::mutex> lock(send_task_map_mutex_);
                    send_task_map_[task->task_id] = task;
                }

                bool send_ok = false;
                task->sent = true;
                {
                    std::unique_lock<std::mutex> lock(send_cb_mutex_);
                    if (send_cb_)
                    {
                        send_ok = send_cb_(task->task_id, task->frame_id, (int64_t)task->shared_handle, task->output_width, task->output_height);
                    }
                }
                task->send_ok = send_ok;

                if (!send_ok)
                {
                    task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SendFail;

                    std::unique_lock<std::mutex> lock(send_task_map_mutex_);
                    send_task_map_.erase(task->task_id);
                }
            }
            else
            {
                if (task->errcode == FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK)
                    task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_UnknownError;
            }

            if (task->errcode != FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK)
            {
                if (task->errmsg.empty())
                {
                    task->errmsg = ToString(task->errcode);
                }

                NotifyGrabFrameResultCallback(task.get());
            }
        }

        tmp_task_list.clear();

        {
            std::unique_lock<std::mutex> ctx_map_lock(ctx_map_mutex_);
            std::unique_lock<std::mutex> task_list_lock(ctx->task_list_mutex);
            if (ctx->task_list.empty())
            {
                ctx_map_.erase(ctx->media_object_id);
            }
        }

    } while (0);
}

void FrameGrab::OnHookVideoAPILayerRenderLoopBegin(int64_t                                                render_count,
                                                   const std::map<uint32_t, std::vector<std::string>>&    canvas_map,
                                                   const std::map<std::string, std::vector<std::string>>& canvas_item_map,
                                                   const std::map<std::string, std::string>&              canvas_item_map2)
{
    render_count_ = render_count;

    {
        std::unique_lock<std::mutex> ctx_map_lock(ctx_map_mutex_);

        for (auto it = ctx_map_.begin(); it != ctx_map_.end();)
        {

            auto found = canvas_item_map2.find(it->second->media_object_id);

            if (found != canvas_item_map2.end())
            {
                ++it;
            }
            else
            {
                auto ctx = it->second;
                it = ctx_map_.erase(it);

                {
                    std::unique_lock<std::mutex> task_list_lock(ctx->task_list_mutex);
                    for (const auto& task : ctx->task_list)
                    {
                        task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_ItemNotExist;
                        task->errmsg = ToString(task->errcode);
                        NotifyGrabFrameResultCallback(task.get());
                    }
                    ctx->task_list.clear();
                }
            }
        }
    }
}

void FrameGrab::OnHookVideoAPILayerRenderLoopEnd()
{
}

void FrameGrab::OnWorkerThreadLoopTick(uint64_t tick_count)
{
    auto now = std::chrono::steady_clock::now();

    if (now >= check_tp_ + std::chrono::seconds(1))
    {
        check_tp_ = now;

        // clean timed out grab task
        ClearTimeoutGrabTask(now, 5000);

        // clean timed out sent task
        ClearTimeoutSendTask(now, 5000);

        // clear timed out post process cache
        ClearTimeoutPostProcessCache(now, 120 * 1000);
    }

    // clean up d3d resource
    if (clean_up_->exchange(0) > 0)
    {
        ctx_->Flush();
    }
}

bool FrameGrab::GrabFrame(
    const std::string&  media_object_id,
    const std::string&  frame_id,
    ClipResizeOrderEnum clip_resize_order,
    FitModeEnum         fit_mode,
    int32_t             resize_width,
    int32_t             resize_height,
    int32_t             clip_x,
    int32_t             clip_y,
    int32_t             clip_z,
    int32_t             clip_w)
{
    bool success = false;

    auto task = std::make_shared<GrabFrameTask>(clean_up_);

    do
    {

        task->media_object_id = media_object_id;
        task->frame_id = frame_id;
        task->task_id = task_id_++;

        task->task_create_tp = std::chrono::steady_clock::now();

        task->clip_resize_order = clip_resize_order;
        task->fit_mode = fit_mode;

        task->resize_width = resize_width;
        task->resize_height = resize_height;
        task->clip_x = clip_x;
        task->clip_y = clip_y;
        task->clip_z = clip_z;
        task->clip_w = clip_w;

        task->grabed = false;
        task->grabed_ok = false;

        if (clip_resize_order != ClipResizeOrderEnum::kClipResizeOrder_ClipFirst && clip_resize_order != ClipResizeOrderEnum::kClipResizeOrder_ResizeFirst)
        {
            task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter;
            break;
        }

        if (clip_x < 0 || clip_y < 0 || clip_z < 0 || clip_w < 0 || resize_width < 0 || resize_height < 0)
        {
            task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter;
            break;
        }

        if (task->clip_resize_order == ClipResizeOrderEnum::kClipResizeOrder_ClipFirst)
        {
            bool valid = (resize_width > 0 && resize_height > 0) || (resize_width == 0 && resize_height == 0);

            if (!valid)
            {
                task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter;
                break;
            }

            task->output_width = resize_width;
            task->output_height = resize_height;
        }
        else if (task->clip_resize_order == ClipResizeOrderEnum::kClipResizeOrder_ResizeFirst)
        {

            if (resize_width > 0 || resize_height > 0)
            {
                int32_t w = resize_width - clip_x - clip_z;
                int32_t h = resize_height - clip_y - clip_w;

                if (w <= 0)
                    w = resize_width;
                if (h <= 0)
                    h = resize_height;

                task->output_width = w;
                task->output_height = h;
            }
        }
        else
        {
            task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_InvalidParameter;
            break;
        }

        if (task->output_height > 0 && task->output_width > 0)
        {
            bool ok = PrepareTexture(task.get(), task->errcode);
            if (!ok)
                break;
        }

        {
            std::unique_lock<std::mutex> ctx_lock(ctx_map_mutex_);

            auto& ctx = ctx_map_[media_object_id];
            if (!ctx)
            {
                ctx = std::make_shared<GrabContext>();
                ctx->media_object_id = media_object_id;
                ctx->last_seen_render_count = render_count_;
                ctx->last_seen_tp = std::chrono::steady_clock::now();
            }

            {
                std::unique_lock<std::mutex> task_list_lock(ctx->task_list_mutex);
                ctx->task_list.push_back(task);
            }
        }

        success = true;

    } while (0);

    if (!success)
    {
        task->errmsg = ToString(task->errcode);
        NotifyGrabFrameResultCallback(task.get());
    }

    return success;
}

void FrameGrab::SetSendFrameCB(SendFrameCB cb)
{
    std::unique_lock<std::mutex> lock(send_cb_mutex_);
    send_cb_ = cb;
}

void FrameGrab::SendFrameAck(const int64_t task_id)
{
    GrabFrameTaskPtr task;

    {
        std::unique_lock<std::mutex> lock(send_task_map_mutex_);

        auto found = send_task_map_.find(task_id);
        if (found != send_task_map_.end())
        {
            task = found->second;
            send_task_map_.erase(found);
        }
    }

    if (task)
    {
        NotifyGrabFrameResultCallback(task.get());
    }
}

FrameGrab::GrabContextPtr FrameGrab::GetGrabContext(const std::string& media_object_id)
{
    std::unique_lock<std::mutex> lock(ctx_map_mutex_);

    auto found = ctx_map_.find(media_object_id);

    if (found != ctx_map_.end())
    {
        return found->second;
    }

    return NULL;
}

bool FrameGrab::ProcessFrameInRenderThread(
    ID3D11Texture2D*        dst,
    ID3D11Texture2D*        src,
    ClipResizeOrderEnum     clip_resize_order,
    FitModeEnum             fit_mode,
    int32_t                 clip_x,
    int32_t                 clip_y,
    int32_t                 clip_z,
    int32_t                 clip_w,
    FrameGrabErrorCodeEnum& errcode)
{
    using namespace DirectX;

    bool success = false;

    do
    {

        if (!dst || !src || !render_)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_AssertFail;
            break;
        }

        D3D11_TEXTURE2D_DESC src_desc;
        src->GetDesc(&src_desc);

        ComPtr<ID3D11Texture2D>          src_frame_tex(src);
        ComPtr<ID3D11ShaderResourceView> src_frame_srv = graphics_helper::CreateTexture2DSRV(sdk_dev_, src);

        if (!src_frame_srv)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureSRVFail;
            break;
        }

        // HDR
        if (post_process_ && (src_desc.Format == DXGI_FORMAT_R16G16B16A16_FLOAT || src_desc.Format == DXGI_FORMAT_R16G16B16A16_UNORM))
        {
            auto cache = GetPostProcessCache(src_desc.Width, src_desc.Height, errcode);

            if (!cache)
            {
                break;
            }

            graphics_helper::ScopeRenderTarget target(sdk_dev_, sdk_ctx_, cache->tex_rtv.Get(), cache->width, cache->height);

            if (!target.IsVaild())
            {
                errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SetRenderTargetFail;
                break;
            }

            post_process_->SetHDRSourceTexture(src_frame_srv.Get());
            post_process_->Process(sdk_ctx_);

            src_frame_tex = cache->tex;
            src_frame_srv = cache->tex_srv;
            cache->tex->GetDesc(&src_desc);
        }

        {

            D3D11_TEXTURE2D_DESC dst_desc;
            dst->GetDesc(&dst_desc);

            RECT src_rect;

            float dst_pos_x;
            float dst_pos_y;
            float src_scale_x;
            float src_scale_y;

            bool ok = ProcessMode(
                clip_resize_order,
                fit_mode,
                src_desc.Width,
                src_desc.Height,
                dst_desc.Width,
                dst_desc.Height,
                clip_x,
                clip_y,
                clip_z,
                clip_w,
                src_rect,
                dst_pos_x,
                dst_pos_y,
                src_scale_x,
                src_scale_y);

            if (!ok)
            {
                errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_ProcessModeInvalidParameter;
                break;
            }

            graphics_helper::ScopeRenderTarget target(sdk_dev_, sdk_ctx_, dst);

            if (!target.IsVaild())
            {
                errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_SetRenderTargetFail;
                break;
            }

            render_->Begin(DX::SpriteSortMode_Immediate);

            GXMVECTOR scale{src_scale_x, src_scale_y, 0.f, 0.f};

            render_->Draw(
                src_frame_srv.Get(), FXMVECTOR{dst_pos_x, dst_pos_y, 0.f, 0.f}, &src_rect, Colors::White, 0.f, FXMVECTOR{0.f, 0.f, 0.f, 0.f}, scale);

            render_->End();
        }

        success = true;

    } while (0);

    if (success)
        errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK;

    return success;
}

FrameGrab::PostProcessCachePtr FrameGrab::GetPostProcessCache(int32_t width, int32_t height, FrameGrabErrorCodeEnum& errcode)
{
    bool                success = false;
    PostProcessCachePtr cache;

    {
        std::unique_lock<std::mutex> lock(post_process_cache_map_mutex_);

        int64_t key = ((int64_t)(width) << 32) | height;

        auto found = post_process_cache_map_.find(key);
        if (found != post_process_cache_map_.end())
        {
            cache = found->second;
            success = true;
        }
        else
        {
            do
            {
                auto tex = graphics_helper::CreateGpuTexture2D(sdk_dev_, width, height, DXGI_FORMAT_B8G8R8A8_UNORM);
                if (!tex)
                {
                    errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureFail;
                    break;
                }

                auto srv = graphics_helper::CreateTexture2DSRV(sdk_dev_, tex.Get());
                if (!srv)
                {
                    errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureSRVFail;
                    break;
                }

                auto rvt = graphics_helper::CreateTexture2DRTV(sdk_dev_, tex.Get());
                if (!rvt)
                {
                    errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateTextureRTVFail;
                    break;
                }

                cache = std::make_shared<PostProcessCache>();
                cache->width = width;
                cache->height = height;
                cache->tex = tex;
                cache->tex_srv = srv;
                cache->tex_rtv = rvt;

                success = true;

            } while (0);

            if (success)
            {
                post_process_cache_map_[key] = cache;
            }
            else
            {
                cache.reset();
            }
        }
    }

    if (cache)
        cache->last_use_tp = std::chrono::steady_clock::now();

    if (success)
        errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK;

    return cache;
}

void FrameGrab::ClearPostProcessCache()
{
    std::unique_lock<std::mutex> lock(post_process_cache_map_mutex_);
    post_process_cache_map_.clear();
}

void FrameGrab::ClearTimeoutPostProcessCache(const std::chrono::steady_clock::time_point& now, const int64_t timeout_ms)
{
    std::unique_lock<std::mutex> lock(post_process_cache_map_mutex_);

    for (auto it = post_process_cache_map_.begin(); it != post_process_cache_map_.end();)
    {
        if (now >= it->second->last_use_tp + std::chrono::milliseconds(timeout_ms))
        {
            ++it;
        }
        else
        {
            it = post_process_cache_map_.erase(it);
        }
    }
}

void FrameGrab::ClearTimeoutSendTask(const std::chrono::steady_clock::time_point& now, const int64_t timeout_ms)
{
    std::vector<GrabFrameTaskPtr> remove_tasks;

    {
        std::unique_lock<std::mutex> lock(send_task_map_mutex_);
        for (auto task_it = send_task_map_.begin(); task_it != send_task_map_.end();)
        {
            auto& task = task_it->second;
            if (now >= task->transfer_tp + std::chrono::milliseconds(timeout_ms))
            {
                remove_tasks.push_back(task);
                task_it = send_task_map_.erase(task_it);
            }
            else
            {
                ++task_it;
            }
        }
    }

    for (auto task : remove_tasks)
    {
        task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_AckTimeout;
        task->errmsg = ToString(task->errcode);
        NotifyGrabFrameResultCallback(task.get());
    }
    remove_tasks.clear();
}

void FrameGrab::ClearTimeoutGrabTask(const std::chrono::steady_clock::time_point& now, const int64_t timeout_ms)
{
    std::vector<GrabFrameTaskPtr> remove_tasks;
    {
        std::unique_lock<std::mutex> lock(ctx_map_mutex_);

        for (auto ctx_it = ctx_map_.begin(); ctx_it != ctx_map_.end();)
        {
            auto& ctx = ctx_it->second;
            if (now >= ctx->last_seen_tp + std::chrono::milliseconds(timeout_ms))
            {
                {
                    std::unique_lock<std::mutex> task_list_lock(ctx->task_list_mutex);
                    remove_tasks.insert(remove_tasks.end(), ctx->task_list.begin(), ctx->task_list.end());
                }

                ctx_it = ctx_map_.erase(ctx_it);
            }
            else
            {
                ++ctx_it;
            }
        }
    }

    for (auto task : remove_tasks)
    {
        task->errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GrabFrameTimeout;
        task->errmsg = ToString(task->errcode);
        NotifyGrabFrameResultCallback(task.get());
    }
    remove_tasks.clear();
}

bool FrameGrab::ProcessMode(
    ClipResizeOrderEnum clip_resize_order,
    FitModeEnum         mode,
    int                 src_width,
    int                 src_height,
    int                 dst_width,
    int                 dst_height,
    int32_t             clip_x,
    int32_t             clip_y,
    int32_t             clip_z,
    int32_t             clip_w,
    RECT&               src_rect,
    float&              dst_pos_x,
    float&              dst_pos_y,
    float&              src_scale_x,
    float&              src_scale_y)
{

    if (clip_x < 0 || clip_y < 0 || clip_w < 0 || clip_z < 0)
        return false;

    if (src_width <= 0 || src_height <= 0 || dst_width <= 0 || dst_height <= 0)
        return false;

    if (clip_resize_order == ClipResizeOrderEnum::kClipResizeOrder_ClipFirst)
    {
        int32_t src_w = src_width - clip_x - clip_z;
        int32_t src_h = src_height - clip_y - clip_w;

        if (src_w <= 0)
        {
            src_w = src_width;
            clip_x = 0;
            clip_z = 0;
        }
        if (src_h <= 0)
        {
            src_h = src_height;
            clip_y = 0;
            clip_w = 0;
        }

        src_rect.left = clip_x;
        src_rect.top = clip_y;
        src_rect.right = src_width - clip_z;
        src_rect.bottom = src_height - clip_w;

        if (mode == FitModeEnum::kFitModeEnum_Cover)
        {
            src_scale_x = (float)dst_width / (float)src_w;
            src_scale_y = (float)dst_height / (float)src_h;
            dst_pos_x = 0.f;
            dst_pos_y = 0.f;
        }
        else
        {
            double tmp_src_scale_x = (double)dst_width / (double)src_w;
            double tmp_src_scale_y = (double)dst_height / (double)src_h;
            double scale = (std::min)(tmp_src_scale_x, tmp_src_scale_y);

            src_scale_x = (float)scale;
            src_scale_y = (float)scale;

            dst_pos_x = (float)(((double)dst_width - src_w * scale) * 0.5);
            dst_pos_y = (float)(((double)dst_height - src_h * scale) * 0.5);
        }

        return true;
    }
    else if (clip_resize_order == ClipResizeOrderEnum::kClipResizeOrder_ResizeFirst)
    {
        if (mode == FitModeEnum::kFitModeEnum_Cover)
        {
            int resize_w = dst_width + clip_x + clip_z;
            int resize_h = dst_height + clip_y + clip_w;

            double tmp_src_scale_x = (double)resize_w / (double)src_width;
            double tmp_src_scale_y = (double)resize_h / (double)src_height;

            src_scale_x = (float)tmp_src_scale_x;
            src_scale_y = (float)tmp_src_scale_y;

            src_rect.left = LONG(clip_x / tmp_src_scale_x);
            src_rect.top = LONG(clip_y / tmp_src_scale_y);
            src_rect.right = LONG(src_width - clip_z / tmp_src_scale_x);
            src_rect.bottom = LONG(src_height - clip_w / tmp_src_scale_y);

            dst_pos_x = 0.f;
            dst_pos_y = 0.f;
        }
        else
        {
            int resize_w = dst_width + clip_x + clip_z;
            int resize_h = dst_height + clip_y + clip_w;

            double tmp_src_scale_x = (double)resize_w / (double)src_width;
            double tmp_src_scale_y = (double)resize_h / (double)src_height;
            double scale = (std::min)(tmp_src_scale_x, tmp_src_scale_y);

            src_scale_x = (float)scale;
            src_scale_y = (float)scale;

            double pos_xx = (resize_w - src_width * scale) * 0.5;
            double pos_yy = (resize_h - src_height * scale) * 0.5;
            double pos_xx2 = pos_xx + src_width * scale;
            double pos_yy2 = pos_yy + src_height * scale;

            double tmp_dst_pos_x = (float)(clip_x > pos_xx ? clip_x : pos_xx);
            double tmp_dst_pos_y = (float)(clip_y > pos_yy ? clip_y : pos_yy);

            dst_pos_x = (float)(tmp_dst_pos_x - clip_x);
            dst_pos_y = (float)(tmp_dst_pos_y - clip_y);

            double dst_pos_x2 = ((int64_t)resize_w - (int64_t)clip_z) < pos_xx2 ? ((int64_t)resize_w - (int64_t)clip_z) : pos_xx2;
            double dst_pos_y2 = ((int64_t)resize_h - (int64_t)clip_w) < pos_yy2 ? ((int64_t)resize_h - (int64_t)clip_w) : pos_yy2;

            src_rect.left = LONG((tmp_dst_pos_x - pos_xx) / scale);
            src_rect.top = LONG((tmp_dst_pos_y - pos_yy) / scale);
            src_rect.right = LONG(src_width - (pos_xx2 - dst_pos_x2) / scale);
            src_rect.bottom = LONG(src_height - (pos_yy2 - dst_pos_y2) / scale);
        }

        return true;
    }
    else
    {
        return false;
    }

    return true;
}

void FrameGrab::SetGrabFrameResultCallback(GrabFrameResultCallback cb)
{
    std::unique_lock<std::mutex> lock(cb_mutex_);
    cb_ = cb;
}

void FrameGrab::NotifyGrabFrameResultCallback(GrabFrameTask* task)
{
    if (!task)
        return;

    if (task->notified_result)
        return;

    task->notified_result = true;

    {
        std::unique_lock<std::mutex> lock(cb_mutex_);
        if (cb_)
            cb_(task->frame_id, task->output_width, task->output_height, (int32_t)task->errcode, task->errmsg);
    }
}

bool FrameGrab::PrepareTexture(GrabFrameTask* task, FrameGrabErrorCodeEnum& errcode)
{
    bool success = false;

    do
    {

        auto cap_tex = graphics_helper::CreateGpuSharedTexture2D(dev_, task->output_width, task->output_height);
        if (!cap_tex)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GpuCreateSharedTextureFail;
            break;
        }

        auto shared_handle = graphics_helper::GetSharedHandler(cap_tex.Get());
        if (shared_handle == INVALID_HANDLE_VALUE)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GetSharedHandleFail;
            break;
        }

        auto shared_tex = graphics_helper::OpenSharedHandler(sdk_dev_, shared_handle);
        if (!shared_tex)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OpenSharedHandlerFail;
            break;
        }

        auto shared_tex_mutex = graphics_helper::GetSharedTextureKeyedMutex(shared_tex.Get());
        if (!shared_tex_mutex)
        {
            errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_GetSharedTextureKeyedMutexFail;
            break;
        }

        task->cap_tex = cap_tex;
        task->shared_handle = shared_handle;
        task->shared_tex = shared_tex;
        task->shared_tex_mutex = shared_tex_mutex;
        task->texture_prepared = true;

        success = true;

    } while (0);

    if (success)
        errcode = FrameGrabErrorCodeEnum::kFrameGrabErrorCode_OK;

    return success;
}

} // namespace v3