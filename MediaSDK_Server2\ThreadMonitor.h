﻿#pragma once

#include "LSPublicHeader.h"
#include <map>
#include <list>

struct TICKINFO
{
	UINT64 tick = 0;
	std::string desc;
};

struct THREADMONITORINFO
{
	DWORD tid = 0;
	std::string name;
	UINT64 threadBeginTick = 0;
	UINT64 threadEndTick = 0;
	UINT64 frameBeginTick = 0;
	UINT64 frameEndTick = 0;
	UINT64 secondCounter = 0;
	UINT64 fpsCounter = 0;
	UINT64 fps = 0;
	bool frameBegin = false;
	int reportTime = 0;
	std::list<TICKINFO> ticks;
};

class ThreadMonitor :public IThreadMonitor
{
public:
	ThreadMonitor() {}
	virtual ~ThreadMonitor() {}
	void CheckThread();
	//IThreadMonitor
	virtual void* OnThreadBegin(const char* name);
	virtual void OnThreadEnd(void* context);
	virtual void OnFrameBegin(void* context);
	virtual void OnFrameEnd(void* context);
	virtual void OnFrameTick(void* context, const char* desc);
	virtual void* GetContext();
	virtual void SetFrozenTime(UINT64 timeMS);

protected:
	TinyLock m_lock;
	std::map<DWORD, THREADMONITORINFO*> m_threadInfos;
	UINT64 m_frozenTime = 15ULL * 1000;
	UINT64 m_respondTime = 500;
	bool m_reported = false;
	UINT64 m_lastCheckTime = 0;
	UINT64 m_recoverTime = 0;
};