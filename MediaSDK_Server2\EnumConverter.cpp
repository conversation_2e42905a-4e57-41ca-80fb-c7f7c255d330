#include "EnumConverter.h"

AUDIO_DATA_FLOW MapAudioDataFlow2Native(EDataFlow in, bool* success)
{
    bool            ok = true;
	AUDIO_DATA_FLOW out = AUDIO_DATA_FLOW(in);

    switch (in)
    {
    case EDataFlow::eCapture:
        out = AUDIO_DATA_FLOW::AUDIO_DATA_FLOW_INPUT;
        break;
    case EDataFlow::eRender:
        out = AUDIO_DATA_FLOW::AUDIO_DATA_FLOW_OUTPUT;
        break;
    case EDataFlow::eAll:
        out = AUDIO_DATA_FLOW::AUDIO_DATA_FLOW_ALL;
        break;
    default:
        ok = false;
    }

    if (success)
        *success = ok;

    return out;
}

AUDIO_DEVICE_ROLE MapAudioDeviceRole2Native(ERole in, bool* success)
{
    AUDIO_DEVICE_ROLE out = AUDIO_DEVICE_ROLE(in);
    bool                            ok = true;

    switch (in)
    {
    case ERole::eConsole:
        out = AUDIO_DEVICE_ROLE::AUDIO_DEVICE_ROLE_CONSOLE;
        break;
    case ERole::eMultimedia:
        out = AUDIO_DEVICE_ROLE::AUDIO_DEVICE_ROLE_MULTI_MEDIA;
        break;
    case ERole::eCommunications:
        out = AUDIO_DEVICE_ROLE::AUDIO_DEVICE_ROLE_COMMUNICATIONS;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

AUDIO_DEVICE_STATE MapAudioDeviceState2Native(DWORD state, bool* success)
{
    AUDIO_DEVICE_STATE out = AUDIO_DEVICE_STATE(state);
    bool               ok = true;

    switch (state)
    {
    case DEVICE_STATE_ACTIVE:
        out = AUDIO_DEVICE_STATE::AUDIO_DEVICE_STATE_ACTIVE;
        break;
    case DEVICE_STATE_DISABLED:
        out = AUDIO_DEVICE_STATE::AUDIO_DEVICE_STATE_DISABLE;
        break;
    case DEVICE_STATE_NOTPRESENT:
        out = AUDIO_DEVICE_STATE::AUDIO_DEVICE_STATE_NOTPRESENT;
        break;
    case DEVICE_STATE_UNPLUGGED:
        out = AUDIO_DEVICE_STATE::AUDIO_DEVICE_STATE_UNPLUGGED;
        break;
    case DEVICE_STATEMASK_ALL:
        out = AUDIO_DEVICE_STATE::AUDIO_DEVICE_STATE_ALL;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

DEVICE_TRANSPORT_TYPE MapDeviceTransportType2Native(int in, bool* success)
{
    DEVICE_TRANSPORT_TYPE out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_UNKNOWN;
    bool ok = true;

    switch (in)
    {
    case 0:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_UNKNOWN;
        break;
    case 1:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_BUILT_IN;
        break;
    case 2:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_BLUETOOTH;
        break;
    case 3:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_VIRTUAL;
        break;
    case 4:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_USB;
        break;
    case 5:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_DISPLAY_AUDIO;
        break;
    case 6:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_PCI;
        break;
    case 7:
        out = DEVICE_TRANSPORT_TYPE::DEVICE_TRANSPORT_AIR_PLAY;
        break;
    default:
        ok = false;
        assert(false && "MapDeviceTransportType fail");
        break;
    }

    if (success)
        *success = ok;

    return out;
}

CASTMATE_EVENT_TYPE MapMobileProjectorEvent2Native(const bytelink_visual_source::CastMateEvent in, bool* success)
{
    CASTMATE_EVENT_TYPE out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_CLOSE;
    bool                                 ok = true;
    switch (in)
    {
    case bytelink_visual_source::CastMateEvent::kCastMateEventClose:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_CLOSE;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventFirstFrame:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_FIRSTFRAME;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventConnected:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_CONNECTED;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventDisconnected:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_DISCONNECTED;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventPlugin:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_PLUGIN;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventPlugout:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_PLUGOUT;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventEvent:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_CASTMATE_EVENT;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventOnError:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_ONERROR;
        break;
    case bytelink_visual_source::CastMateEvent::kCastMateEventOnSDKError:
        out = CASTMATE_EVENT_TYPE::CASTMATE_EVENT_ONSDKERROR;
        break;
    default:
        ok = false;
        break;
    }
    if (success)
        *success = ok;

    return out;
}

CASTMATE_PROTOCOL_TYPE MapMobileProjectorProtocol2Native(const bytelink_visual_source::CastmateProtocolType in, bool* success)
{
    CASTMATE_PROTOCOL_TYPE out = CASTMATE_PROTOCOL_TYPE::WIRELESS_ANDROID;
    bool                        ok = true;
    switch (in)
    {
    case bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWirelessAndroid:
        out = CASTMATE_PROTOCOL_TYPE::WIRELESS_ANDROID;
        break;
    case bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWirelessIOS:
        out = CASTMATE_PROTOCOL_TYPE::WIRELESS_IOS;
        break;
    case bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredAndroid:
        out = CASTMATE_PROTOCOL_TYPE::WIRED_ANDROID;
        break;
    case bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredIOS:
        out = CASTMATE_PROTOCOL_TYPE::WIRED_IOS;
        break;
    default:
        ok = false;
        break;
    }
    if (success)
        *success = ok;

    return out;
}

using namespace fav_visual_source;
FAV_EVENT_TYPE MapMediaSourceEvent2Native(const fav_visual_source::FAVEventType in, bool* success)
{
    FAV_EVENT_TYPE out = FAV_EVENT_TYPE::FAV_EVENT_UNKNOW;
    bool           ok = true;
    
    switch (in)
    {
    case FAVEventType::kEventOpened:
        out = FAV_EVENT_TYPE::FAV_EVENT_OPENED;
        break;
    case FAVEventType::kPauseChanged:
        out = FAV_EVENT_TYPE::FAV_EVENT_PAUSE_CHANGED;
        break;
    case FAVEventType::kPositionChanged:
        out = FAV_EVENT_TYPE::FAV_EVENT_POSITION_CHANGED;
        break;
    case FAVEventType::kReadEOF:
        out = FAV_EVENT_TYPE::FAV_EVENT_READ_EOF;
        break;
    case FAVEventType::kPlayEOF:
        out = FAV_EVENT_TYPE::FAV_EVENT_PLAY_EOF;
        break;
    case FAVEventType::kError:
        out = FAV_EVENT_TYPE::FAV_EVENT_ERROR;
        break;
	case FAVEventType::kReadPacketTimeOut:
		out = FAV_EVENT_TYPE::FAV_EVENT_READ_PACKET_TIMEOUT;
		break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

COLOR_RANGE MapColorRange2Native(mediasdk::VideoRange in, bool* success)
{
    COLOR_RANGE out = COLOR_RANGE::COLOR_RANGE_UNSPECIFIED;
    bool           ok = true;
    switch (in)
    {
    case mediasdk::VideoRange::kVideoRangeUnspecified:
        out = COLOR_RANGE::COLOR_RANGE_UNSPECIFIED;
        break;
    case mediasdk::VideoRange::kVideoRangePartial:
        out = COLOR_RANGE::COLOR_RANGE_PARTIAL;
        break;
    case mediasdk::VideoRange::kVideoRangeFull:
        out = COLOR_RANGE::COLOR_RANGE_FULL;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

STREAM_TYPE MapStreamType2Native(const std::string& t, bool* success)
{
    STREAM_TYPE out = STREAM_TYPE::STREAM_LIVE_RTMP;
    bool           ok = true;
    if (t == "RTMPStreamServiceSource")
    {
        out = STREAM_TYPE::STREAM_LIVE_RTMP;
    }
    else if (t == "RecordStreamServiceSource")
    {
        out = STREAM_TYPE::STREAM_RECORD_FFMPEG;
    }
    else if (t == "RTMPStreamServiceSource")
    {
        out = STREAM_TYPE::STREAM_LIVE_BYTE_RTMP;
    }
    else if (t == "RTMPQStreamServiceSource")
    {
        out = STREAM_TYPE::STREAM_LIVE_BYTE_RTMPQ;
    }
    else
    {
        ok = false;
    }
    if (success)
        *success = ok;
    return out;
}

REMOTE_LEAVE_REASON MapRemoteLeaveReason2Native(bytertc::UserOfflineReason in, bool* success)
{
    REMOTE_LEAVE_REASON out = REMOTE_LEAVE_REASON::REMOTE_LEAVE_REASON_QUIT;
    bool ok = true;

    switch (in)
    {
    case bytertc::UserOfflineReason::kUserOfflineReasonQuit:
        out = REMOTE_LEAVE_REASON::REMOTE_LEAVE_REASON_QUIT;
        break;
    case bytertc::UserOfflineReason::kUserOfflineReasonDropped:
        out = REMOTE_LEAVE_REASON::REMOTE_LEAVE_REASON_DROPPED;
        break;
    case bytertc::UserOfflineReason::kUserOfflineReasonSwitchToInvisible:
        out = REMOTE_LEAVE_REASON::REMOTE_LEAVE_REASON_SWITCH_TO_INVISIBLE;
        break;
    case bytertc::UserOfflineReason::kUserOfflineReasonKickedByAdmin:
        out = REMOTE_LEAVE_REASON::REMOTE_LEAVE_REASON_KICKED_BY_ADMIN;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

STREAM_MIXING_EVENT MapStreamMixingEvent2Native(int in, bool* success)
{
    STREAM_MIXING_EVENT out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_BASE;
    bool                               ok = true;

    switch (in)
    {
    case mediasdk::kStreamMixingEventBase:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_BASE;
        break;
    case mediasdk::kStreamMixingEventStart:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_START;
        break;
    case mediasdk::kStreamMixingEventStartSuccess:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_START_SUCCEED;
        break;
    case mediasdk::kStreamMixingEventStartFailed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_START_FAILED;
        break;
    case mediasdk::kStreamMixingEventUpdate:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_UPDATE;
        break;
    case mediasdk::kStreamMixingEventUpdateSuccess:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_UPDATE_SUCCEED;
        break;
    case mediasdk::kStreamMixingEventUpdateFailed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_UPDATE_FAILED;
        break;
    case mediasdk::kStreamMixingEventStop:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_STOP;
        break;
    case mediasdk::kStreamMixingEventStopSuccess:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_STOP_SUCCEED;
        break;
    case mediasdk::kStreamMixingEventStopFailed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_STOP_FAILED;
        break;
    case mediasdk::kStreamMixingEventChangeMixType:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_CHANGE_MIX_TYPE;
        break;
    case mediasdk::kStreamMixingEventFirstAudioFrameByClientMix:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_FIRST_AUDIO_FRAME_CLIENT_MIX;
        break;
    case mediasdk::kStreamMixingEventFirstVideoFrameByClientMix:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_FIRST_VIDEO_FRAME_CLIENT_MIX;
        break;
    case mediasdk::kStreamMixingEventUpdateTimeout:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_UPDATE_TIMEOUT;
        break;
    case mediasdk::kStreamMixingEventStartTimeout:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_START_TIMEOUT;
        break;
    case mediasdk::kStreamMixingEventRequestParamError:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_REQUEST_PARAM_ERROR;
        break;
    case mediasdk::kStreamMixingEventMixImageEvent:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_MIX_IMAGE_EVENT;
        break;
    case mediasdk::kStreamMixingEventMixSingleWayChorusEvent:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_MIX_SINGLE_WAY_CHORUS_EVENT;
        break;
    case mediasdk::kStreamMixingEventMixStreamMixingMax:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_MIX_STREAM_MIXING_MAX;
        break;
    case mediasdk::kStreamMixingEventAlternateImageSucceed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_ALTERNATE_IMAGE_SUCCEED;
        break;
    case mediasdk::kStreamMixingEventAlternateImageFailed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_ALTERNATE_IMAGE_FAILED;
        break;
    case mediasdk::kStreamMixingEventBackgroundUrlSucceed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_BACKGROUND_URL_SUCCEED;
        break;
    case mediasdk::kStreamMixingEventBackgroundUrlFailed:
        out = STREAM_MIXING_EVENT::STREAM_MIXING_EVENT_BACKGROUND_URL_FAILED;
        break;
    default:
        ok = false;
    }

    if (success)
        *success = ok;

    return out;
}

int32_t MapAudioDeviceMonitorType2SDK(const AUDIO_MONITOR_TYPE in, bool* success)
{
	int32_t out = mediasdk::AudioMonitorType::kAudioMonitorNone;
	bool    ok = true;

	switch (in)
	{
	case AUDIO_MONITOR_TYPE::AUDIO_MONITOR_NONE:
		out = mediasdk::AudioMonitorType::kAudioMonitorNone;
		break;
	case AUDIO_MONITOR_TYPE::AUDIO_MONITOR_DEVICE:
		out = mediasdk::AudioMonitorType::kAudioMonitorRender;
		break;
	case AUDIO_MONITOR_TYPE::AUDIO_MONITOR_STREAM:
		out = mediasdk::AudioMonitorType::kAudioMonitorOutput;
		break;
	case AUDIO_MONITOR_TYPE::AUDIO_MONITOR_STREAM_AND_DEVICE:
		out = mediasdk::AudioMonitorType::kAudioMonitorRender | mediasdk::AudioMonitorType::kAudioMonitorOutput;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

AUDIO_MONITOR_TYPE MapAudioDeviceMonitorType2Native(const int32_t in, bool* success)
{
    AUDIO_MONITOR_TYPE out = AUDIO_MONITOR_TYPE::AUDIO_MONITOR_NONE;
	bool               ok = true;

	if (in == mediasdk::AudioMonitorType::kAudioMonitorNone)
	{
		out = AUDIO_MONITOR_TYPE::AUDIO_MONITOR_NONE;
	}
	else if (in == mediasdk::AudioMonitorType::kAudioMonitorRender)
	{
		out = AUDIO_MONITOR_TYPE::AUDIO_MONITOR_DEVICE;
	}
	else if (in == mediasdk::AudioMonitorType::kAudioMonitorOutput)
	{
		out = AUDIO_MONITOR_TYPE::AUDIO_MONITOR_STREAM;
	}
	else if (in == (mediasdk::AudioMonitorType::kAudioMonitorRender | mediasdk::AudioMonitorType::kAudioMonitorOutput))
	{
		out = AUDIO_MONITOR_TYPE::AUDIO_MONITOR_STREAM_AND_DEVICE;
	}
	else
	{
		ok = false;
	}

	if (success)
		*success = ok;

	return out;
}

mediasdk::ColorSpace MapColorSpace2SDK(COLOR_SPACE in, bool* success)
{
	mediasdk::ColorSpace out = mediasdk::ColorSpace::kColorSpaceUnspecified;
	bool                 ok = true;
	switch (in)
	{
	case COLOR_SPACE::COLOR_SPACE_UNSPECIFIED:
		out = mediasdk::ColorSpace::kColorSpaceUnspecified;
		break;
	case COLOR_SPACE::COLOR_SPACE_BT709:
		out = mediasdk::ColorSpace::kColorSpaceBT709;
		break;
	case COLOR_SPACE::COLOR_SPACE_BT601:
		out = mediasdk::ColorSpace::kColorSpaceBT601;
		break;
	case COLOR_SPACE::COLOR_SPACE_BT2020:
		out = mediasdk::ColorSpace::kColorSpaceBT2020;
		break;
	case COLOR_SPACE::COLOR_SPACE_BT2100:
		out = mediasdk::ColorSpace::kColorSpaceBT2100;
		break;
	case COLOR_SPACE::COLOR_SPACE_FCC:
	case COLOR_SPACE::COLOR_SPACE_SMPTE240M:
	case COLOR_SPACE::COLOR_SPACE_BT470:
		ok = false;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

mediasdk::VideoRange MapColorRange2SDK(COLOR_RANGE in, bool* success)
{
	mediasdk::VideoRange out = mediasdk::VideoRange::kVideoRangeUnspecified;
	bool                 ok = true;
	switch (in)
	{
	case COLOR_RANGE::COLOR_RANGE_UNSPECIFIED:
		out = mediasdk::VideoRange::kVideoRangeUnspecified;
		break;
	case COLOR_RANGE::COLOR_RANGE_PARTIAL:
		out = mediasdk::VideoRange::kVideoRangePartial;
		break;
	case COLOR_RANGE::COLOR_RANGE_FULL:
		out = mediasdk::VideoRange::kVideoRangeFull;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

mediasdk::PixelFormat MapVideoFormat2SDK(VIDEO_PIXEL_FORMAT in, bool* success)
{
	mediasdk::PixelFormat out = mediasdk::PixelFormat::kPixelFormatUnspecified;
	bool                  ok = true;
	switch (in)
	{
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_UNKNOWN:
		out = mediasdk::PixelFormat::kPixelFormatUnspecified;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I420:
		out = mediasdk::PixelFormat::kPixelFormatI420;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I420A:
		out = mediasdk::PixelFormat::kPixelFormatI420A;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YV12:
		out = mediasdk::PixelFormat::kPixelFormatYV12;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_NV12:
		out = mediasdk::PixelFormat::kPixelFormatNV12;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_NV21:
		out = mediasdk::PixelFormat::kPixelFormatNV21;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_UYVY:
		out = mediasdk::PixelFormat::kPixelFormatUYVY;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YUY2:
		out = mediasdk::PixelFormat::kPixelFormatYUY2;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_ARGB:
		out = mediasdk::PixelFormat::kPixelFormatARGB;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_XRGB:
		out = mediasdk::PixelFormat::kPixelFormatXRGB;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_RGB24:
		out = mediasdk::PixelFormat::kPixelFormatRGB24;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_RGBA:
		out = mediasdk::PixelFormat::kPixelFormatRGBA;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_BGR24:
		out = mediasdk::PixelFormat::kPixelFormatBGR24;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_BGRA:
		out = mediasdk::PixelFormat::kPixelFormatBGRA;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_MJPEG:
		out = mediasdk::PixelFormat::kPixelFormatMJPEG;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I444:
		out = mediasdk::PixelFormat::kPixelFormatI444;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I444A:
		out = mediasdk::PixelFormat::kPixelFormatI444A;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I422:
		out = mediasdk::PixelFormat::kPixelFormatI422;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I422A:
		out = mediasdk::PixelFormat::kPixelFormatI422A;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YVYU:
		out = mediasdk::PixelFormat::kPixelFormatYVYU;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_HDYC:
		out = mediasdk::PixelFormat::kPixelFormatHDYC;
		break;
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_P010:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_P016:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_NV12_MS:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_P010_MS:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_P016_MS:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I010:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_V210:
	case VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I210:
		out = mediasdk::PixelFormat::kPixelFormatUnspecified;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

VIDEO_PIXEL_FORMAT MapVideoFormat2Native(mediasdk::PixelFormat in, bool* success)
{
	VIDEO_PIXEL_FORMAT out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_UNKNOWN;
	bool                 ok = true;
	switch (in)
	{
	case mediasdk::kPixelFormatUnspecified:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_UNKNOWN;
		break;
	case mediasdk::kPixelFormatNV12:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_NV12;
		break;
	case mediasdk::kPixelFormatI420:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I420;
		break;
	case mediasdk::kPixelFormatI444:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I444;
		break;
	case mediasdk::kPixelFormatI422:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I422;
		break;
	case mediasdk::kPixelFormatYV12:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YV12;
		break;
	case mediasdk::kPixelFormatNV21:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_NV21;
		break;
	case mediasdk::kPixelFormatUYVY:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_UYVY;
		break;
	case mediasdk::kPixelFormatYUY2:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YUY2;
		break;
	case mediasdk::kPixelFormatARGB:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_ARGB;
		break;
	case mediasdk::kPixelFormatXRGB:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_XRGB;
		break;
	case mediasdk::kPixelFormatRGB24:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_RGB24;
		break;
	case mediasdk::kPixelFormatRGBA:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_RGBA;
		break;
	case mediasdk::kPixelFormatBGR24:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_BGR24;
		break;
	case mediasdk::kPixelFormatBGRA:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_BGRA;
		break;
	case mediasdk::kPixelFormatMJPEG:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_MJPEG;
		break;
	case mediasdk::kPixelFormatI444A:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I444A;
		break;
	case mediasdk::kPixelFormatI420A:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I420A;
		break;
	case mediasdk::kPixelFormatI422A:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_I422A;
		break;
	case mediasdk::kPixelFormatYVYU:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_YVYU;
		break;
	case mediasdk::kPixelFormatHDYC:
		out = VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_HDYC;
		break;
	case mediasdk::kPixelFormatI440:
	case mediasdk::kPixelFormatI410:
	case mediasdk::kPixelFormatI411:
	case mediasdk::kPixelFormatI400:
	case mediasdk::kPixelFormatNV12TEXTURE:
	case mediasdk::kPixelFormatY8:
	case mediasdk::kPixelFormatY8TEXTURE:
	case mediasdk::kPixelFormatU8V8:
	case mediasdk::kPixelFormatRGBA16:
		LOG(ERROR) << "Unsupported format: " << in;
		ok = false;
		break;
	case mediasdk::kPixelFormatMax:
		LOG(ERROR) << "Out of rang format: " << mediasdk::kPixelFormatMax;
		ok = false;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

bytelink_visual_source::CastMateOptionType MapCastMateOptionType2SDK(const CASTMATE_OPTION_TYPE in, bool* success)
{
	bytelink_visual_source::CastMateOptionType out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeLatency;
	bool                                       ok = true;

	switch (in)
	{
	case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_LATENCY:
		out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeLatency;
		break;
	case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_ENABLE_RANDOM_PORT:
		out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeEnableRandomPort;
		break;
	case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_AUDIO_SOURCE:
		out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetAudioSource;
		break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_RESET_HOST:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeResetHost;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_ENABLE_SERVER:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeEnableServer;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_DEVICE_INFO:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetDeviceInfo;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_FRAME_STUCK:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetFrameStuck;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_PERFORMANCE_CALLBACK:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetPerformanceCallback;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_VIDEO_OUTPUT_TYPE:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeVideoOuputTypt;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_AUDIO_OUTPUT_TYPE:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeAudioOuputType;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_ENABLE_NEW_MUXD:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeEnableNewMuxd;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_ENABLE_NEW_ANDROID_WIREDCAST:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeEnableNewAndroidWiredcast;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_ENABLE_FAST_MIRROR:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeEnableFastMirror;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_DECODE_MODE:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetDecodeMode;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_PERFORMANCE_EVENT:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetPerformanceEvent;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_AB_CONFIG:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetABConfig;
        break;
    case CASTMATE_OPTION_TYPE::CASTMATE_OPTION_TYPE_SET_VOUT_MODE:
        out = bytelink_visual_source::CastMateOptionType::kCastMateOptionTypeSetVoutMode;
        break;
	default:
		ok = false;
	}

	if (success)
		*success = ok;

	return out;
}

bytelink_visual_source::CastmateProtocolType MapCastmateProtocolType2SDK(const CASTMATE_PROTOCOL_TYPE in, bool* success)
{
	bytelink_visual_source::CastmateProtocolType out = bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWirelessAndroid;
	bool                                         ok = true;
	switch (in)
	{
	case CASTMATE_PROTOCOL_TYPE::WIRELESS_ANDROID:
		out = bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWirelessAndroid;
		break;
	case CASTMATE_PROTOCOL_TYPE::WIRELESS_IOS:
		out = bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWirelessIOS;
		break;
	case CASTMATE_PROTOCOL_TYPE::WIRED_ANDROID:
		out = bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredAndroid;
		break;
	case CASTMATE_PROTOCOL_TYPE::WIRED_IOS:
		out = bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredIOS;
		break;
	default:
		ok = false;
	}

	if (success)
		*success = ok;

	return out;
}

int MapANSOption2SDK(const AUDIO_ANS_OPTION in, bool* success)
{
	int  out = 4; // default AUTO
	bool ok = true;
	switch (in)
	{
	case ANS_OPTION_CLOSE:
		out = 0;
		break;
	case ANS_OPTION_LOW:
		out = 1;
		break;
	case ANS_OPTION_MEDIUM:
		out = 2;
		break;
	case ANS_OPTION_HIGH:
		out = 3;
		break;
	case ANS_OPTION_AUTO:
		out = 4;
		break;
	default:
		ok = false;
		assert(false && "MapANSOption fail");
		break;
	}

	if (success)
		*success = ok;

	return out;
}

std::string MapStreamType2SDK(const STREAM_TYPE& t)
{
	std::string out;
	switch (t)
	{
	case STREAM_LIVE_RTMP:
		out = "RTMPStreamServiceSource";
		break;
	case STREAM_NONE:
	case STREAM_RECORD_FFMPEG:
	case STREAM_RECORD_FLV:
		out = "RecordStreamServiceSource";
		break;
	case STREAM_LIVE_BYTE_RTMP:
		out = "RTMPStreamServiceSource";
		break;
	case STREAM_LIVE_BYTE_RTMPQ:
		out = "RTMPQStreamServiceSource";
		break;
	case STREAM_LIVE_FFMPEG:
	case STREAM_LIVE_RTS:
	case STREAM_LIVE_BYTE_RTMPS:
	case STREAM_RTC:
	case STREAM_NDI:
	case STREAM_VIRTUAL_CAMERA:
	default:
		break;
	};
	return out;
}

bytertc::MediaStreamType MapRTCControllerMediaStreamType2SDK(MEDIA_STREAM_TYPE type, bool* success)
{
	bytertc::MediaStreamType out = bytertc::MediaStreamType::kMediaStreamTypeBoth;
	bool                     ok = true;
	switch (type)
	{
	case MEDIA_STREAM_TYPE::MEDIA_STREAM_AUDIO:
		out = bytertc::MediaStreamType::kMediaStreamTypeAudio;
		break;
	case MEDIA_STREAM_TYPE::MEDIA_STREAM_VIDEO:
		out = bytertc::MediaStreamType::kMediaStreamTypeVideo;
		break;
	case MEDIA_STREAM_TYPE::MEDIA_STREAM_BOTH:
		out = bytertc::MediaStreamType::kMediaStreamTypeBoth;
		break;
	default:
		ok = false;
		break;
	}
	if (success)
		*success = ok;

	return out;
}

mediasdk::VirtualCameraObjectFitMode MapObjectFitMode2SDK(OBJECT_FIT_MODE mode, bool* success)
{
    mediasdk::VirtualCameraObjectFitMode out = mediasdk::VirtualCameraObjectFitMode::kVirtualCameraObjectFitModeContain;
    bool                                 ok = true;
    switch (mode)
    {
    case OBJECT_FIT_MODE::OBJECT_FIT_MODE_CONTAIN:
		out = mediasdk::VirtualCameraObjectFitMode::kVirtualCameraObjectFitModeContain;
        break;
    case OBJECT_FIT_MODE::OBJECT_FIT_MODE_COVER:
		out = mediasdk::VirtualCameraObjectFitMode::kVirtualCameraObjectFitModeCover;
        break;
    default:
		ok = false;
        break;
    }

    if (success)
        *success = ok;

	return out;
}

mediasdk::TransitionType MapTransitionType2SDK(TRANSITION_TYPE type, bool* success)
{
	mediasdk::TransitionType out = mediasdk::TransitionType::kTransitionNone;
	bool                     ok = true;
	switch (type)
	{
	case TRANSITION_TYPE_NONE:
		out = mediasdk::TransitionType::kTransitionNone;
		break;
	case TRANSITION_TYPE_SLIDE:
		out = mediasdk::TransitionType::kTransitionSlide;
		break;
	case TRANSITION_TYPE_SWIPE:
		out = mediasdk::TransitionType::kTransitionSwipe;
		break;
	case TRANSITION_TYPE_CARTOON:
		out = mediasdk::TransitionType::kTransitionCartoon;
		break;
	case TRANSITION_TYPE_FADE:
		out = mediasdk::TransitionType::kTransitionFade;
		break;
	case TRANSITION_TYPE_FADE2COLOR:
		out = mediasdk::TransitionType::kTransitionFade2Color;
		break;
	case TRANSITION_TYPE_LUMINANCE_WIDE:
		out = mediasdk::TransitionType::kTransitionLuminanceWide;
		break;
	case TRANSITION_TYPE_MOVE:
		out = mediasdk::TransitionType::kTransitionMove;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

mediasdk::TransitionProgressFunctionType MapTransitionProgressFunctionType2SDK(TRANSITION_PROGRESS_FUNCTION_TYPE type, bool* success)
{
	mediasdk::TransitionProgressFunctionType out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeLinear;
	bool                                     ok = true;
	switch (type)
	{
	case TRANSITION_PROGRESS_FUNCTION_LINEAR:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeLinear;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_SINE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInSine;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_SINE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutSine;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_SINE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutSine;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUAD:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInQuad;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUAD:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutQuad;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUAD:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutQuad;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_CUBIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInCubic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CUBIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutCubic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutCubic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUART:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInQuart;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUART:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutQuart;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUART:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutQuart;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUINT:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInQuint;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUINT:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutQuint;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUINT:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutQuint;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_EXPO:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInExpo;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_EXPO:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutExpo;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_EXPO:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutExpo;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_CIRC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInCirc;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CIRC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutCirc;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CIRC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutCirc;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_BACK:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInBack;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BACK:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutBack;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BACK:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutBack;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_ELASTIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInElastic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_ELASTIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutElastic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_ELASTIC:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutElastic;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_BOUNCE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInBounce;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BOUNCE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseOutBounce;
		break;
	case TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BOUNCE:
		out = mediasdk::TransitionProgressFunctionType::kTransitionProgressFunctionTypeEaseInOutBounce;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
        *success = ok;

    return out;
}

mediasdk::TransitionDirection MapTransitionDirection2SDK(TRANSITION_DIRECTION direction, bool* success)
{
	mediasdk::TransitionDirection out = mediasdk::TransitionDirection::kTransitionDirectionLeft;
	bool                          ok = true;
	switch (direction)
	{
	case TRANSITION_DIRECTION_LEFT:
		out = mediasdk::TransitionDirection::kTransitionDirectionLeft;
		break;
	case TRANSITION_DIRECTION_RIGHT:
		out = mediasdk::TransitionDirection::kTransitionDirectionRight;
		break;
	case TRANSITION_DIRECTION_UP:
		out = mediasdk::TransitionDirection::kTransitionDirectionUp;
		break;
	case TRANSITION_DIRECTION_DOWN:
		out = mediasdk::TransitionDirection::kTransitionDirectionDown;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
        *success = ok;

    return out;
}

mediasdk::TransitionMoveType MapTransitionMoveType2SDK(TRANSITION_MOVE_TYPE type, bool* success)
{
	mediasdk::TransitionMoveType out = mediasdk::TransitionMoveType::kTransitionMoveTypeSlide;
	bool                         ok = true;
	switch (type)
	{
	case TRANSITION_MOVE_NONE:
		ok = false;
		break;
	case TRANSITION_MOVE_SLIDE:
		out = mediasdk::kTransitionMoveTypeSlide;
		break;
	case TRANSITION_MOVE_SCALE:
		out = mediasdk::kTransitionMoveTypeScale;
		break;
	case TRANSITION_MOVE_FADE:
		out = mediasdk::kTransitionMoveTypeFade;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
        *success = ok;

    return out;
}

std::string MapGraffitiType2SDK(const GRAFFITI_TYPE in)
{
	std::string out = "";

	switch (in)
	{
	case GRAFFITI_TYPE::GRAFFITI_NONE:
		out = "";
		break;
	case GRAFFITI_TYPE::GRAFFITI_RECT:
		out = "";
		break;
	case GRAFFITI_TYPE::GRAFFITI_ARROW:
		out = "Arrow";
		break;
	case GRAFFITI_TYPE::GRAFFITI_ELLIPSE:
		out = "";
		break;
	case GRAFFITI_TYPE::GRAFFITI_CURVE:
		out = "Curve";
		break;
	case GRAFFITI_TYPE::GRAFFITI_TEXT:
		out = "";
		break;
	case GRAFFITI_TYPE::GRAFFITI_LINE:
		out = "Line";
		break;
	case GRAFFITI_TYPE::GRAFFITI_ERASE:
		out = "Eraser";
		break;
	case GRAFFITI_TYPE::GRAFFITI_RECT_FILL:
		out = "Rectangle";
		break;
	case GRAFFITI_TYPE::GRAFFITI_ELLIPSE_FILL:
		out = "";
		break;
	default:
		break;
	}
	return out;
}

GRAFFITI_TYPE MapGraffitiType2Native(const std::string in)
{
	std::map<std::string, GRAFFITI_TYPE> mp = { {"Arrow", GRAFFITI_TYPE::GRAFFITI_ARROW},
												  {"Curve", GRAFFITI_TYPE::GRAFFITI_CURVE},
												  {"Line", GRAFFITI_TYPE::GRAFFITI_LINE},
												  {"Eraser", GRAFFITI_TYPE::GRAFFITI_ERASE},
												  {"Rectangle", GRAFFITI_TYPE::GRAFFITI_RECT_FILL} };
	if (mp.find(in) == mp.end())
		return GRAFFITI_TYPE::GRAFFITI_NONE;
	return mp[in];
}

CURSOR_HIT_POSITION MapCursorHitPosition2Native(mediasdk::HittestCursorPos in, bool* success)
{
    CURSOR_HIT_POSITION out = CURSOR_HIT_POSITION::HIT_POSITION_NOTHING;
    bool                ok = true;

    switch (in)
    {
    case mediasdk::kHittestCursorPosNothing:
        out = CURSOR_HIT_POSITION::HIT_POSITION_NOTHING;
        break;
    case mediasdk::kHittestCursorPosLeftTop:
        out = CURSOR_HIT_POSITION::HIT_POSITION_TOP_LEFT;
        break;
    case mediasdk::kHittestCursorPosRightTop:
        out = CURSOR_HIT_POSITION::HIT_POSITION_TOP_RIGHT;
        break;
    case mediasdk::kHittestCursorPosRightBottom:
        out = CURSOR_HIT_POSITION::HIT_POSITION_BOTTOM_RIGHT;
        break;
    case mediasdk::kHittestCursorPosLeftBottom:
        out = CURSOR_HIT_POSITION::HIT_POSITION_BOTTOM_LEFT;
        break;
    case mediasdk::kHittestCursorPosTop:
        out = CURSOR_HIT_POSITION::HIT_POSITION_TOP;
        break;
    case mediasdk::kHittestCursorPosRight:
        out = CURSOR_HIT_POSITION::HIT_POSITION_RIGHT;
        break;
    case mediasdk::kHittestCursorPosBottom:
        out = CURSOR_HIT_POSITION::HIT_POSITION_BOTTOM;
        break;
    case mediasdk::kHittestCursorPosLeft:
        out = CURSOR_HIT_POSITION::HIT_POSITION_LEFT;
        break;
    case mediasdk::kHittestCursorPosMiddle:
        out = CURSOR_HIT_POSITION::HIT_POSITION_MIDDLE;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

SHORT_CUT_ACTION MapShortCutAction2Native(mediasdk::ShortcutAction in, bool* success)
{
    SHORT_CUT_ACTION out = SHORT_CUT_ACTION::ACTION_KEY_NONE;
    bool ok = true;

    switch (in)
    {
    case mediasdk::kShortcutActionNothing:
        out = SHORT_CUT_ACTION::ACTION_KEY_NONE;
        break;
    case mediasdk::kShortcutActionKeyUp:
        out = SHORT_CUT_ACTION::ACTION_KEY_UP;
        break;
    case mediasdk::kShortcutActionKeyDown:
        out = SHORT_CUT_ACTION::ACTION_KEY_DOWN;
        break;
    case mediasdk::kShortcutActionKeyLeft:
        out = SHORT_CUT_ACTION::ACTION_KEY_LEFT;
        break;
    case mediasdk::kShortcutActionKeyRight:
        out = SHORT_CUT_ACTION::ACTION_KEY_RIGHT;
        break;
    case mediasdk::kShortcutActionClip:
        out = SHORT_CUT_ACTION::ACTION_KEY_ALT_CLIP;
        break;
    case mediasdk::kShortcutActionStretch:
        out = SHORT_CUT_ACTION::ACTION_KEY_SHIFT_STRETCH;
        break;
    default:
        ok = false;
        break;
    }

    if (success)
        *success = ok;

    return out;
}

VISUAL_CAPTURE_TYPE MapWindowCaptureMethod2Native(desktop_capture_visual_source::WindowCaptureMethod in, bool* success)
{
    auto out = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_NONE;
	bool ok = true;

    switch (in)
    {
    case desktop_capture_visual_source::WindowCaptureMethod::kWindowCaptureMethodNone:
        out = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_NONE;
        break;
    case desktop_capture_visual_source::WindowCaptureMethod::kWindowCaptureMethodWGC:
        out = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_WGC;
        break;
    case desktop_capture_visual_source::WindowCaptureMethod::kWindowCaptureMethodDuplicator:
        out = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_DDA;
        break;
    case desktop_capture_visual_source::WindowCaptureMethod::kWindowCaptureMethodBitblt:
        out = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_BITBLT;
        break;
    default:
		ok = false;
        break;
    }

	if (success)
        *success = ok;

    return out;
}

GAME_CAPTURE_EVENT_TYPE MapGameCaptureEventType2Native(mediasdk::GameCaptureEventType in, bool* success)
{
	GAME_CAPTURE_EVENT_TYPE out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_MEM;
	bool ok = true;

	switch (in)
	{
	case mediasdk::kCaptureEventNil:
		ok = false;
		break;
	case mediasdk::kCaptureSuccessBuffer:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_MEM;
		break;
	case mediasdk::kCaptureSuccessTexture:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_TEXTURE;
		break;
	case mediasdk::kCaptureErrorTimeOut:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_TIME_OUT;
		break;
	case mediasdk::kCaptureErrorTexture:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_TEXTURE;
		break;
	case mediasdk::kCaptureErrorInitialize:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_INITIALIZE;
		break;
	case mediasdk::kCaptureErrorInternal:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_INTERNAL;
		break;
	case mediasdk::kCaptureErrorHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_HOOK;
		break;
	case mediasdk::kCaptureErrorDevice:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_DEVICE;
		break;
	case mediasdk::kCaptureEventCommon:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_COMMON;
		break;
	case mediasdk::kCaptureEventInject:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_INJECT;
		break;
	case mediasdk::kCaptureEventFirstFrame:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_FIRSTFRAME;
		break;
	case mediasdk::kCaptureEventDx9CPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX9CPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx9GPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX9GPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx10CPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX10CPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx10GPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX10GPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx101CPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX101CPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx101GPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX101GPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx11CPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX11CPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx11GPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX11GPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx12CPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX12CPU_HOOK;
		break;
	case mediasdk::kCaptureEventDx12GPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_DX12GPU_HOOK;
		break;
	case mediasdk::kCaptureEventGlCPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_GLCPU_HOOK;
		break;
	case mediasdk::kCaptureEventGlGPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_GLGPU_HOOK;
		break;
	case mediasdk::kCaptureEventVulkanCPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_VULKANCPU_HOOK;
		break;
	case mediasdk::kCaptureEventVulkanGPUHook:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_VULKANGPU_HOOK;
		break;
	case mediasdk::kCaptureEventStop:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_STOP;
		break;
	case mediasdk::kCaptureErrorMemCreateError:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_MEM_CREATE_ERROR;
		break;
	case mediasdk::kCaptureEventVulkanCPUHookFail:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_VULKANCPU_HOOK_FAIL;
		break;
	case mediasdk::kCaptureEventVulkanGPUHookFail:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_VULKANGPU_HOOK_FAIL;
		break;
	case mediasdk::kCaptureEventVulkanAllHookFail:
		out = GAME_CAPTURE_EVENT_TYPE::CAPTURE_EVENT_VULKANALL_HOOK_FAIL;
		break;
	case mediasdk::kCaptureEventErrorException:
		ok = false;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
		*success = ok;

	return out;
}

CAMERA_CONTROL_TYPE MapCameraControlType2Native(int in, bool* success)
{
	CAMERA_CONTROL_TYPE out = CAMERA_CONTROL_NONE;
	bool ok = true;

	switch (in)
	{
	case 0:
		out = CAMERA_CONTROL_PAN;
		break;
	case 1:
		out = CAMERA_CONTROL_TILT;
		break;
	case 2:
		out = CAMERA_CONTROL_ROLL;
		break;
	case 3:
		out = CAMERA_CONTROL_ZOOM;
		break;
	case 4:
		out = CAMERA_CONTROL_EXPOSURE;
		break;
	case 5:
		out = CAMERA_CONTROL_IRIS;
		break;
	case 6:
		out = CAMERA_CONTROL_FOCUS;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
        *success = ok;

    return out;
}

int MapCameraControlType2SDK(CAMERA_CONTROL_TYPE in, bool* success)
{
	int out = -1;
	bool ok = true;

	switch (in)
	{
	case CAMERA_CONTROL_NONE:
		ok = true;
		break;
	case CAMERA_CONTROL_PAN:
		out = 0;
		break;
	case CAMERA_CONTROL_TILT:
		out = 1;
		break;
	case CAMERA_CONTROL_ROLL:
		out = 2;
		break;
	case CAMERA_CONTROL_ZOOM:
		out = 3;
		break;
	case CAMERA_CONTROL_EXPOSURE:
		out = 4;
		break;
	case CAMERA_CONTROL_IRIS:
		out = 5;
		break;
	case CAMERA_CONTROL_FOCUS:
		out = 6;
		break;
	default:
		ok = false;
		break;
	}

    if (success)
        *success = ok;

    return out;
}

VIDEO_PROCAMP_TYPE MapVideoProcAmpType2Native(int in, bool* success)
{
	VIDEO_PROCAMP_TYPE out = VIDEO_PROCAMP_NONE;
	bool ok = true;

	switch (in)
	{
	case 0:
		out = VIDEO_PROCAMP_BRIGHTNESS;
		break;
	case 1:
		out = VIDEO_PROCAMP_CONTRAST;
		break;
	case 2:
		out = VIDEO_PROCAMP_HUE;
		break;
	case 3:
		out = VIDEO_PROCAMP_SATURATION;
		break;
	case 4:
		out = VIDEO_PROCAMP_SHARPNESS;
		break;
	case 5:
		out = VIDEO_PROCAMP_GAMMA;
		break;
	case 6:
		out = VIDEO_PROCAMP_COLORENABLE;
		break;
	case 7:
		out = VIDEO_PROCAMP_WHITEBALANCE;
		break;
	case 8:
		out = VIDEO_PROCAMP_BACKLIGHTCOMPENSATION;
		break;
	case 9:
		out = VIDEO_PROCAMP_GAIN;
		break;
	default:
		ok = false;
		break;
	}

	if (success)
        *success = ok;

    return out;
}

int MapVideoProcAmpType2SDK(VIDEO_PROCAMP_TYPE in, bool* success)
{
	int out = -1;
	bool ok = true;

	switch (in)
	{
	case VIDEO_PROCAMP_NONE:
		ok = false;
		break;
	case VIDEO_PROCAMP_BRIGHTNESS:
		out = 0;
		break;
	case VIDEO_PROCAMP_CONTRAST:
		out = 1;
		break;
	case VIDEO_PROCAMP_HUE:
		out = 2;
		break;
	case VIDEO_PROCAMP_SATURATION:
		out = 3;
		break;
	case VIDEO_PROCAMP_SHARPNESS:
		out = 4;
		break;
	case VIDEO_PROCAMP_GAMMA:
		out = 5;
		break;
	case VIDEO_PROCAMP_COLORENABLE:
		out = 6;
		break;
	case VIDEO_PROCAMP_WHITEBALANCE:
		out = 7;
		break;
	case VIDEO_PROCAMP_BACKLIGHTCOMPENSATION:
		out = 8;
		break;
	case VIDEO_PROCAMP_GAIN:
		out = 9;
		break;
	default:
		ok = false;
		break;
    }

    if (success)
        *success = ok;

    return out;
}

std::string MapAudioAACProfile2SDK(const PROFILE_AAC in, bool* success)
{
	std::string out = "";
	bool ok = true;

	switch (in)
	{
	case PROFILE_AAC::PROFILE_AAC_NONE:
		out = "";
		break;
	case PROFILE_AAC::PROFILE_AAC_MAIN:
		out = "main";
		break;
	case PROFILE_AAC::PROFILE_AAC_LOW:
		out = "low";
		break;
	case PROFILE_AAC::PROFILE_AAC_SSR:
		out = "ssr";
		break;
	case PROFILE_AAC::PROFILE_AAC_LTP:
		out = "ltp";
		break;
	case PROFILE_AAC::PROFILE_AAC_HE:
		out = "he";
		break;
	case PROFILE_AAC::PROFILE_AAC_HE_V2:
		out = "he_v2";
		break;
	case PROFILE_AAC::PROFILE_AAC_LD:
		out = "ld";
		break;
	case PROFILE_AAC::PROFILE_AAC_ELD:
		out = "eld";
		break;
	default:
		ok = false;
		assert(false && "MapAudioAACProfile2SDK failed");
		break;
	}

	if (success)
		*success = ok;

	return out;
}

std::string MapEncoderName2SDK(std::string in)
{
	const std::map<std::string, std::string> mp = {
		{"ByteVC0", "ByteVC0VideoEncoderSource"},
		{"ByteVC1", "ByteVC1VideoEncoderSource"},
		{"H264_QSV", "QSVH264BufferVideoEncoderSource"},
		{"HEVC_QSV", "QSVHevcBufferVideoEncoderSource"},
		{"HEVC_NVENC_EX", "NVHevcVideoEncoderSource"},
		{"HEVC_NVENC", "NVHevcBufferVideoEncoderSource"},
		{"H264_NVENC_EX", "NVH264VideoEncoderSource"},
		{"H264_NVENC", "NVH264BufferVideoEncoderSource"},
		{"HEVC_AMF", "AMFHEVCBufferVideoEncoderSource"},
		{"HEVC_AMF_EX", "AMFHEVCVideoEncoderSource"},
		{"H264_AMF", "AMFH264BufferVideoEncoderSource"},
		{"H264_AMF_EX", "AMFH264VideoEncoderSource"},
		{"FFMPEG_AAC", "FFmpegAACAudioEncoderSource"},
		{"FDK_AAC", "FDKAACAudioEncoderSource"},
	};
	if (mp.find(in) == mp.end())
		return "";
	return mp.at(in);
}

std::string MapEncoderName2Native(std::string in)
{
	const std::map<std::string, std::string> mp = {
		{"ByteVC0VideoEncoderSource", "ByteVC0"},
		{"ByteVC1VideoEncoderSource", "ByteVC1"},
		{"QSVH264BufferVideoEncoderSource", "H264_QSV"},
		{"QSVHevcBufferVideoEncoderSource", "HEVC_QSV"},
		{"NVHevcVideoEncoderSource", "HEVC_NVENC_EX"},
		{"NVHevcBufferVideoEncoderSource", "HEVC_NVENC"},
		{"NVH264VideoEncoderSource", "H264_NVENC_EX"},
		{"NVH264BufferVideoEncoderSource", "H264_NVENC"},
		{"AMFHEVCBufferVideoEncoderSource", "HEVC_AMF"},
		{"AMFHEVCVideoEncoderSource", "HEVC_AMF_EX"},
		{"AMFH264BufferVideoEncoderSource", "H264_AMF"},
		{"AMFH264VideoEncoderSource", "H264_AMF_EX"} };
	if (mp.find(in) == mp.end())
		return "";
	return mp.at(in);
}

mediasdk::MSTransform MapTransform2SDK(TRANSFORM transform)
{
	mediasdk::MSTransform info{};
	info.angle = transform.angle;
	info.clip.x = transform.clipRange.x;
	info.clip.y = transform.clipRange.y;
	info.clip.z = transform.clipRange.z;
	info.clip.w = transform.clipRange.w;
	info.flip_h = transform.hFlip;
	info.flip_v = transform.vFlip;
	info.scale.x = transform.scale.X;
	info.scale.y = transform.scale.Y;
	info.translate.x = transform.translate.X;
	info.translate.y = transform.translate.Y;
	return info;
}

TRANSFORM MapTransform2Native(mediasdk::MSTransform transform)
{
    TRANSFORM info{};
    info.translate.X = transform.translate.x;
    info.translate.Y = transform.translate.y;
    info.hFlip = transform.flip_h;
    info.vFlip = transform.flip_v;
    info.angle = transform.angle;
    info.scale.X = transform.scale.x;
    info.scale.Y = transform.scale.y;
    info.clipRange.x = transform.clip.x;
    info.clipRange.y = transform.clip.y;
    info.clipRange.z = transform.clip.z;
    info.clipRange.w = transform.clip.w;
    return info;
}
