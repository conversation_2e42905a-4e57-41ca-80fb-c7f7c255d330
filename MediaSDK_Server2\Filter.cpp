#include "Filter.h"
#include "MediaMgr.h"
#include "AudioMgr.h"
#include "ModeSceneMgr.h"
#include "Layer.h"
#include "stringutil.h"

extern media_mgr::MediaMgr* g_mediaMgr;

Filter::Filter(){}

Filter::~Filter(){}

void Filter::ControlFilter(const FILTER& filterInfo, FILTER_CONTROL_CMD cmd)
{
    SetFilterInfo(&filterInfo);
    bool onceCanvasFilter = false;
    for (const auto& mediaID : filterInfo.mediaIDs)
    {
        bool mediaHasCreated = false;
        if (filterInfo.type == FILTER_VISUAL || filterInfo.type == FILTER_EFFECT)
        {
            Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
            if (pLayer)
            {
                LAYER_INFO layerInfo{};
                pLayer->GetLayerInfo(&layerInfo);
                auto it = std::find_if(layerInfo.filters.begin(), layerInfo.filters.end(), [filterInfo](FILTER info) {
                    return filterInfo.id == info.id;
                });
                if (it != layerInfo.filters.end())
                {
                    *it = filterInfo;
                }
                pLayer->SetLayerInfo(&layerInfo);
                mediaHasCreated = layerInfo.isCreated;
            }
            else
            {
                LOG(ERROR) << "[Filter::ControlFilter] mediaID: " << mediaID << " not exist!";
            }
        }
        else if (filterInfo.type == FILTER_AUDIO)
        {
            Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
            if (pAudio)
            {
                AUDIO_INFO audioInfo{};
                pAudio->GetAudioInfo(&audioInfo);
                auto it = std::find_if(audioInfo.filters.begin(), audioInfo.filters.end(), [filterInfo](FILTER info) {
                    return filterInfo.id == info.id;
                });
                if (it != audioInfo.filters.end())
                {
                    *it = filterInfo;
                }
                pAudio->SetAudioInfo(&audioInfo);
                mediaHasCreated = audioInfo.isCreated;
            }
            else
            {
                LOG(ERROR) << "[Filter::ControlFilter] mediaID: " << mediaID << " not exist!";
            }
        }
        else if (filterInfo.type == FILTER_CANVAS)
        {
            Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
            if (pCanvas)
            {
                CANVAS_INFO_EX canvasInfoEx{};
                pCanvas->GetCanvasInfo(&canvasInfoEx);
                if (canvasInfoEx.filter.id == filterInfo.id)
                {
                    canvasInfoEx.filter = filterInfo;
                }
                pCanvas->SetCanvasInfo(&canvasInfoEx);
                mediaHasCreated = canvasInfoEx.isCreated;
                onceCanvasFilter = true;
            }
        }

        bool success = false;
        if (mediaHasCreated && m_filterInfo.isCreated)
            success = g_mediaMgr->ControlFilter(mediaID, m_filterInfo, cmd);

        if (success && onceCanvasFilter)
            break;
    }
}

void Filter::SetFilterInfo(const FILTER* info)
{
    m_filterInfo = *info;
}

void Filter::GetFilterInfo(FILTER* info, FILTER_INFO_CMD cmd)
{
    *info = m_filterInfo;
    
    if (cmd != FILTER_INFO_NONE)
    {
        std::string filter_id = "";
        Util::NumToString(info->id, &filter_id);
        g_mediaMgr->GetFilterInfo(filter_id, info, cmd);
    }
}

void Filter::SetFilterIsCreated(bool isCreated)
{
    m_filterInfo.isCreated = isCreated;
}
