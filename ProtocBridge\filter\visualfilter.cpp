#include "stdafx.h"
#include "visualfilter.h"

namespace LS
{
VisualFilter::RequestList VisualFilter::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<VisualFilter::Create>());
    list.push_back(std::make_unique<VisualFilter::Remove>());

    list.push_back(std::make_unique<VisualFilter::SetVisualFilter>());
    list.push_back(std::make_unique<VisualFilter::GetVisualFilter>());
    return list;
}

void VisualFilter::SetVisualFilterInfo(VISUAL_FILTER& visualFilterInfo, const ls_visualfilter::VisualFilter& visual_filter, UINT64* cmd /* = NULL */)
{
    if (visualFilterInfo.filterType == VISUAL_FILTER_EDGE && visual_filter.has_edge_filter())
    {
        const ls_visualfilter::EdgeFilter& edge_filter = visual_filter.edge_filter();
        if (edge_filter.has_edge_color())
        {
            visualFilterInfo.edgeFilter.edgeColor.x = edge_filter.edge_color().x();
            visualFilterInfo.edgeFilter.edgeColor.y = edge_filter.edge_color().y();
            visualFilterInfo.edgeFilter.edgeColor.z = edge_filter.edge_color().z();
            visualFilterInfo.edgeFilter.edgeColor.w = edge_filter.edge_color().w();
        }
        if (edge_filter.has_edge_width())
        {
            visualFilterInfo.edgeFilter.edgeWidth = edge_filter.edge_width();
        }

        if(cmd) *cmd |= FILTER_CONTROL_SET_EDGE;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_CORNER && visual_filter.has_corner_filter())
    {
        const ls_visualfilter::CornerFilter& corner_filter = visual_filter.corner_filter();
        if (corner_filter.has_corner_info())
        {
            visualFilterInfo.cornerFilter.cornerInfo.x = corner_filter.corner_info().x();
            visualFilterInfo.cornerFilter.cornerInfo.y = corner_filter.corner_info().y();
            visualFilterInfo.cornerFilter.cornerInfo.z = corner_filter.corner_info().z();
            visualFilterInfo.cornerFilter.cornerInfo.w = corner_filter.corner_info().w();
        }
        if (corner_filter.has_fixed_radius())
        {
            visualFilterInfo.cornerFilter.fixedRadius = corner_filter.fixed_radius();
        }
        if (corner_filter.has_reference_border_width())
        {
            visualFilterInfo.cornerFilter.referenceBorderWidth = corner_filter.reference_border_width();
        }

        if (cmd) *cmd |= FILTER_CONTROL_SET_CORNER;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_OVERLAY && visual_filter.has_overlay_filter())
    {
        const ls_visualfilter::OverlayFilter& overlay_filter = visual_filter.overlay_filter();
        if (overlay_filter.has_bk_image())
        {
            visualFilterInfo.overlayFilter.bkImage = overlay_filter.bk_image();
        }
        if (overlay_filter.has_scale())
        {
            visualFilterInfo.overlayFilter.bkScale = overlay_filter.scale();
        }
        if (overlay_filter.has_point_nine())
        {
            visualFilterInfo.overlayFilter.pointNine.isBorder = overlay_filter.point_nine().is_border();
            visualFilterInfo.overlayFilter.pointNine.left.X = overlay_filter.point_nine().left().x();
            visualFilterInfo.overlayFilter.pointNine.left.Y = overlay_filter.point_nine().left().y();
            visualFilterInfo.overlayFilter.pointNine.top.X = overlay_filter.point_nine().top().x();
            visualFilterInfo.overlayFilter.pointNine.top.Y = overlay_filter.point_nine().top().y();
        }

        if (cmd) *cmd |= FILTER_CONTROL_SET_OVERLAY;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_HINT && visual_filter.has_hint_filter())
    {
        const ls_visualfilter::HintFilter& hint_filter = visual_filter.hint_filter();
        if (hint_filter.has_gap())
        {
            visualFilterInfo.hintFilter.gap = hint_filter.gap();
        }
        if (hint_filter.has_image_path())
        {
            visualFilterInfo.hintFilter.imagePath = hint_filter.image_path();
            visualFilterInfo.hintFilter.imageWidth = hint_filter.image_width();
            visualFilterInfo.hintFilter.imageHeight = hint_filter.image_height();
        }
        if (hint_filter.has_animation_iteration_count())
        {
            visualFilterInfo.hintFilter.animationIterationCnt = hint_filter.animation_iteration_count();
        }
        if (hint_filter.has_text())
        {
            visualFilterInfo.hintFilter.text = hint_filter.text();
            visualFilterInfo.hintFilter.textWidth = hint_filter.text_width();
            visualFilterInfo.hintFilter.fontSize = hint_filter.font_size();
            visualFilterInfo.hintFilter.fontFamily = hint_filter.font_family();
            visualFilterInfo.hintFilter.fontFilePath = hint_filter.font_file_path();
            visualFilterInfo.hintFilter.fontColor = hint_filter.font_color();
        }

        if (cmd) *cmd |= FILTER_CONTROL_SET_HINT;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_COLOR_ADJUST && visual_filter.has_color_adjust_filter())
    {
        const ls_visualfilter::ColorAdjustFilter& color_adjust_filter = visual_filter.color_adjust_filter();
        if (color_adjust_filter.has_brightness() &&
            std::abs(visualFilterInfo.colorAdjustFilter.brightness - color_adjust_filter.brightness()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.brightness = color_adjust_filter.brightness();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_BRIGHTNESS;
        }
        if (color_adjust_filter.has_contrast() &&
            std::abs(visualFilterInfo.colorAdjustFilter.contrast - color_adjust_filter.contrast()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.contrast = color_adjust_filter.contrast();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_CONTRAST;
        }
        if (color_adjust_filter.has_gamma() &&
            std::abs(visualFilterInfo.colorAdjustFilter.gamma - color_adjust_filter.gamma()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.gamma = color_adjust_filter.gamma();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_GAMMA;
        }
        if (color_adjust_filter.has_hue_shift() &&
            std::abs(visualFilterInfo.colorAdjustFilter.hueShift - color_adjust_filter.hue_shift()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.hueShift = color_adjust_filter.hue_shift();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_HUE_SHIFT;
        }
        if (color_adjust_filter.has_opacity() &&
            std::abs(visualFilterInfo.colorAdjustFilter.opacity - color_adjust_filter.opacity()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.opacity = color_adjust_filter.opacity();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_OPACITY;
        }
        if (color_adjust_filter.has_saturation() &&
            std::abs(visualFilterInfo.colorAdjustFilter.saturation - color_adjust_filter.saturation()) > EPS)
        {
            visualFilterInfo.colorAdjustFilter.saturation = color_adjust_filter.saturation();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_SATURATION;
        }
        if (color_adjust_filter.has_add_color() &&
            visualFilterInfo.colorAdjustFilter.addColor != color_adjust_filter.add_color())
        {
            visualFilterInfo.colorAdjustFilter.addColor = color_adjust_filter.add_color();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_ADD_COLOR;
        }
        if (color_adjust_filter.has_mul_color() &&
            visualFilterInfo.colorAdjustFilter.mulColor != color_adjust_filter.mul_color())
        {
            visualFilterInfo.colorAdjustFilter.mulColor = color_adjust_filter.mul_color();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_MUL_COLOR;
        }
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_CHROMA_KEY && visual_filter.has_chroma_key_filter())
    {
        ls_visualfilter::ChromaKeyFilter chroma_key_filter = visual_filter.chroma_key_filter();
        if (chroma_key_filter.has_brightness() &&
            std::abs(visualFilterInfo.chromaKeyFilter.brightness - chroma_key_filter.brightness()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.brightness = chroma_key_filter.brightness();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_BRIGHTNESS;
        }
        if (chroma_key_filter.has_contrast() &&
            std::abs(visualFilterInfo.chromaKeyFilter.contrast - chroma_key_filter.contrast()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.contrast = chroma_key_filter.contrast();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_CONTRAST;
        }
        if (chroma_key_filter.has_gamma() &&
            std::abs(visualFilterInfo.chromaKeyFilter.gamma - chroma_key_filter.gamma()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.gamma = chroma_key_filter.gamma();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_GAMMA;
        }
        if (chroma_key_filter.has_opacity() &&
            std::abs(visualFilterInfo.chromaKeyFilter.opacity - chroma_key_filter.opacity()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.opacity = chroma_key_filter.opacity();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_OPACITY;
        }
        if (chroma_key_filter.has_color() &&
            visualFilterInfo.chromaKeyFilter.chroma != chroma_key_filter.color())
        {
            visualFilterInfo.chromaKeyFilter.chroma = chroma_key_filter.color();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_CHROMA;
        }
        if (chroma_key_filter.has_similarity() &&
            std::abs(visualFilterInfo.chromaKeyFilter.similarity - chroma_key_filter.similarity()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.similarity = chroma_key_filter.similarity();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_SIMILARITY;
        }
        if (chroma_key_filter.has_smoothness() &&
            std::abs(visualFilterInfo.chromaKeyFilter.smoothness - chroma_key_filter.smoothness()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.smoothness = chroma_key_filter.smoothness();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_SMOOTHNESS;
        }
        if (chroma_key_filter.has_spill() &&
            std::abs(visualFilterInfo.chromaKeyFilter.spill - chroma_key_filter.spill()) > EPS)
        {
            visualFilterInfo.chromaKeyFilter.spill = chroma_key_filter.spill();
            if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_SPILL;
        }
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_COLOR_LUT && visual_filter.has_color_lut_filter())
    {
        const ls_visualfilter::ColorLutFilter& lut_filter = visual_filter.color_lut_filter();
        if (lut_filter.has_file_path())
            visualFilterInfo.colorLutFilter.file_path = lut_filter.file_path();

        if (lut_filter.has_amount())
            visualFilterInfo.colorLutFilter.amount = lut_filter.amount();

        if (lut_filter.has_pass_through_alpha())
            visualFilterInfo.colorLutFilter.pass_through_alpha = lut_filter.pass_through_alpha();
        if (cmd) *cmd |= FILTER_CONTROL_SET_COLOR_LUT;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SCALE && visual_filter.has_scale_filter())
    {
        const ls_visualfilter::ScaleFilter& scale_filter = visual_filter.scale_filter();
        if (scale_filter.has_stretch_starting_point())
            visualFilterInfo.scaleFilter.startPoint = scale_filter.stretch_starting_point();

        if (scale_filter.has_stretch_ratio())
            visualFilterInfo.scaleFilter.ratio = scale_filter.stretch_ratio();

        if (cmd)
            *cmd |= FILTER_CONTROL_SET_SCALE;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SHAPE && visual_filter.has_shape_filter())
    {
        const ls_visualfilter::ShapeFilter& shape_filter = visual_filter.shape_filter();
        if (shape_filter.has_stretch_starting_point())
            visualFilterInfo.shapeFilter.startPoint = shape_filter.stretch_starting_point();

        if (cmd)
            *cmd |= FILTER_CONTROL_SET_SHAPE;
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SHARPNESS && visual_filter.has_sharpness_filter())
    {
        const ls_visualfilter::SharpnessFilter& filter = visual_filter.sharpness_filter();
        visualFilterInfo.sharpnessFilter.sharpness = filter.sharpness();
        if (cmd) *cmd |= FILTER_CONTROL_SET_SHARPNESS;
    }
}

bool VisualFilter::Create::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	FILTER filterInfo{};
	filterInfo.type = FILTER_VISUAL;

	VISUAL_FILTER visualFilterInfo{};
	visualFilterInfo.filterType = static_cast<VISUAL_FILTER_TYPE>(req.filter_type());

	if (req.has_visual_filter())
	{
		SetVisualFilterInfo(visualFilterInfo, req.visual_filter());
	    if (req.visual_filter().has_enable())
	    {
	        filterInfo.enable = req.visual_filter().enable();
	    }
	}
	filterInfo.filter = visualFilterInfo;

	UINT64 filterID = controller->CreateFilter(&filterInfo);
	std::string filter_id = "";
	Util::NumToString(filterID, &filter_id);
	rsp.set_filter_id(filter_id);

	if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::Create] CreateFilter failed, filterID: " << filterID;
        return false;
    }
    return true;
}

bool VisualFilter::Remove::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
	if (!controller->FindFilterByID(filterID))
	{
		LOG(ERROR) << "[VisualFilter::Remove] filter not exist, filterID: " << filterID;
		return true;
	}

    controller->DeleteFilter(filterID);

    return true;
}

bool VisualFilter::SetVisualFilter::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::SetVisualFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    if (filterInfo.type == FILTER_VISUAL)
    {
        UINT64        cmd = FILTER_CONTROL_NONE;
        VISUAL_FILTER visualFilterInfo = std::get<VISUAL_FILTER>(filterInfo.filter);
        if (req.has_visual_filter())
        {
            SetVisualFilterInfo(visualFilterInfo, req.visual_filter(), &cmd);
            if (req.visual_filter().has_enable())
            {
                filterInfo.enable = req.visual_filter().enable();
                cmd |= FILTER_CONTROL_SET_FILTER_ENABLE;
            }
        }

        filterInfo.filter = visualFilterInfo;
        controller->ControlFilter(filterID, filterInfo, static_cast<FILTER_CONTROL_CMD>(cmd));
    }

    return true;
}

bool VisualFilter::GetVisualFilter::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[VisualFilter::GetVisualFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    VISUAL_FILTER visualFilterInfo = std::get<VISUAL_FILTER>(filterInfo.filter);
    ls_visualfilter::VisualFilter visual_filter{};

    if (visualFilterInfo.filterType == VISUAL_FILTER_CORNER)
    {
        ls_visualfilter::CornerFilter corner_filter{};
        ls_base::ClipF                corner_info{};
        corner_info.set_x(visualFilterInfo.cornerFilter.cornerInfo.x);
        corner_info.set_y(visualFilterInfo.cornerFilter.cornerInfo.y);
        corner_info.set_z(visualFilterInfo.cornerFilter.cornerInfo.z);
        corner_info.set_w(visualFilterInfo.cornerFilter.cornerInfo.w);
        corner_filter.mutable_corner_info()->CopyFrom(corner_info);
        corner_filter.set_fixed_radius(visualFilterInfo.cornerFilter.fixedRadius);
        corner_filter.set_reference_border_width(visualFilterInfo.cornerFilter.referenceBorderWidth);
        visual_filter.mutable_corner_filter()->CopyFrom(corner_filter);
    }
	else if (visualFilterInfo.filterType == VISUAL_FILTER_EDGE)
	{
        ls_visualfilter::EdgeFilter edge_filter{};
		ls_base::ClipF edge_color{};
		edge_color.set_x(visualFilterInfo.edgeFilter.edgeColor.x);
		edge_color.set_y(visualFilterInfo.edgeFilter.edgeColor.y);
		edge_color.set_z(visualFilterInfo.edgeFilter.edgeColor.z);
		edge_color.set_w(visualFilterInfo.edgeFilter.edgeColor.w);
        edge_filter.mutable_edge_color()->CopyFrom(edge_color);
        edge_filter.set_edge_width(visualFilterInfo.edgeFilter.edgeWidth);
        visual_filter.mutable_edge_filter()->CopyFrom(edge_filter);
	}
    else if (visualFilterInfo.filterType == VISUAL_FILTER_OVERLAY)
    {
        ls_visualfilter::OverlayFilter overlay_filter{};
        ls_visualfilter::PointNineInfo point_nine_info{};
        point_nine_info.set_is_border(visualFilterInfo.overlayFilter.pointNine.isBorder);
        ls_base::PointF point_left{};
        point_left.set_x(visualFilterInfo.overlayFilter.pointNine.left.X);
        point_left.set_x(visualFilterInfo.overlayFilter.pointNine.left.Y);
        point_nine_info.mutable_left()->CopyFrom(point_left);
        ls_base::PointF point_top{};
        point_top.set_x(visualFilterInfo.overlayFilter.pointNine.top.X);
        point_top.set_x(visualFilterInfo.overlayFilter.pointNine.top.Y);
        point_nine_info.mutable_top()->CopyFrom(point_top);
        overlay_filter.mutable_point_nine()->CopyFrom(point_nine_info);
        overlay_filter.set_bk_image(visualFilterInfo.overlayFilter.bkImage);
        overlay_filter.set_scale(visualFilterInfo.overlayFilter.bkScale);
        visual_filter.mutable_overlay_filter()->CopyFrom(overlay_filter);
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_COLOR_ADJUST)
    {
        ls_visualfilter::ColorAdjustFilter color_adjust_filter{};
        color_adjust_filter.set_brightness(visualFilterInfo.colorAdjustFilter.brightness);
        color_adjust_filter.set_contrast(visualFilterInfo.colorAdjustFilter.contrast);
        color_adjust_filter.set_gamma(visualFilterInfo.colorAdjustFilter.gamma);
        color_adjust_filter.set_hue_shift(visualFilterInfo.colorAdjustFilter.hueShift);
        color_adjust_filter.set_opacity(visualFilterInfo.colorAdjustFilter.opacity);
        color_adjust_filter.set_saturation(visualFilterInfo.colorAdjustFilter.saturation);
        color_adjust_filter.set_add_color(visualFilterInfo.colorAdjustFilter.addColor);
        color_adjust_filter.set_mul_color(visualFilterInfo.colorAdjustFilter.mulColor);
        visual_filter.mutable_color_adjust_filter()->CopyFrom(color_adjust_filter);
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_CHROMA_KEY)
    {
        ls_visualfilter::ChromaKeyFilter chroma_key_filter{};
        chroma_key_filter.set_brightness(visualFilterInfo.chromaKeyFilter.brightness);
        chroma_key_filter.set_contrast(visualFilterInfo.chromaKeyFilter.contrast);
        chroma_key_filter.set_gamma(visualFilterInfo.chromaKeyFilter.gamma);
        chroma_key_filter.set_opacity(visualFilterInfo.chromaKeyFilter.opacity);
        chroma_key_filter.set_color(visualFilterInfo.chromaKeyFilter.chroma);
        chroma_key_filter.set_similarity(visualFilterInfo.chromaKeyFilter.similarity);
        chroma_key_filter.set_smoothness(visualFilterInfo.chromaKeyFilter.smoothness);
        chroma_key_filter.set_spill(visualFilterInfo.chromaKeyFilter.spill);
        visual_filter.mutable_chroma_key_filter()->CopyFrom(chroma_key_filter);
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_HINT)
    {
        // TODO: @xuwanhui
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_COLOR_LUT)
    {
        // TODO: @xuwanhui
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SHARPNESS)
    {
        // TODO: @xuwanhui
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SCALE)
    {
        // TODO: @xuwanhui
    }
    else if (visualFilterInfo.filterType == VISUAL_FILTER_SHAPE)
    {
        // TODO: @xuwanhui
    }
    
    rsp.mutable_visual_filter()->CopyFrom(visual_filter);
    rsp.set_type(static_cast<ls_visualfilter::VISUAL_FILTER_TYPE>(visualFilterInfo.filterType));
    return true;
}
} // namespace MediaSDK