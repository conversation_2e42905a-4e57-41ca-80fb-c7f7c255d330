﻿#include "Layer.h"
#include "ModeSceneMgr.h"
#include "MediaMgr.h"
#include "AudioMgr.h"
#include "FilterMgr.h"
#include "SourceMgr.h"

extern media_mgr::MediaMgr* g_mediaMgr;

Layer::Layer() {}

Layer::~Layer() {}

void Layer::SetLayerInfo(const LAYER_INFO* info)
{
    m_layerInfo = *info;
}

void Layer::SetParent(UINT64 id)
{
    m_layerInfo.canvasID = id;
}

void Layer::ControlLayer(const LAYER_INFO& layerInfo, LAYER_CONTROL_CMD cmd)
{
    SetLayerInfo(&layerInfo);

    UINT64 oCmd = static_cast<UINT64>(cmd);
    CalLayerTransform(oCmd);

    if (m_syncFlag)
    {
        g_mediaMgr->ControlLayer(m_layerInfo, static_cast<LAYER_CONTROL_CMD>(oCmd));
    }
}

void Layer::GetLayerInfo(LAYER_INFO* layerInfo)
{
    *layerInfo = m_layerInfo;
}

bool Layer::SyncToMediaCanvas(uint64_t canvasID, bool add)
{
    bool success = false;
    if (add)
    {
        if (!SourceMgr::GetInstance()->CheckSourceExist(m_layerInfo.sourceID))
        {
            SOURCE_INFO sourceInfo{};
            SourceMgr::GetInstance()->GetSourceInfoByID(m_layerInfo.sourceID, &sourceInfo);
            if (sourceInfo.type == VISUAL_BROWSER)
            {
                BROWSER_SOURCE browser = std::get<BROWSER_SOURCE>(sourceInfo.source);
                if (browser.token.empty() && browser.captureDLL.empty() && browser.deviceID == 0)
                {
                    LOG(WARNING) << "[Layer::SyncToMediaCanvas] browser fake exist!";
                    return true;
                }
            }
            else if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                LOG(WARNING) << "[Layer::SyncToMediaCanvas] virtual camera has not source";
            }
            else
            {
                LOG(ERROR) << "[Layer::SyncToMediaCanvas] check source is not created, source_id: " << sourceInfo.id;
                return false;
            }
        }

        // create real layer
        if (!m_layerInfo.isCreated)
        {
            success = g_mediaMgr->AddLayer(canvasID, m_layerInfo);
            if (!success)
            {
                LOG(ERROR) << "[Layer::SyncToMediaCanvas] AddLayer failed, layer_id: " << m_layerInfo.id;
                return false;
            }
            m_layerInfo.isCreated = true;
        }

        // add real filter to layer
        for (auto& filterInfo : m_layerInfo.filters)
        {
            bool ret = g_mediaMgr->AddFilter(m_layerInfo.id, filterInfo);
            if (!ret && filterInfo.type != FILTER_EFFECT)
            {
                LOG(ERROR) << "[Layer::SyncToMediaCanvas] layer AddFilter failed, layer_id: " << m_layerInfo.id;
                continue;
            }
            filterInfo.isCreated = true;

            // update layer's filter information
            if (Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterInfo.id))
                pFilter->SetFilterIsCreated(true);
        }
    }
    else
    {
        success = g_mediaMgr->RemoveLayer(m_layerInfo.id);
        if (!success)
        {
            LOG(ERROR) << "[Layer::SyncToMediaCanvas] RemoveLayer failed, layer_id: " << m_layerInfo.id;
            return false;
        }
        m_layerInfo.isCreated = false;
    }

    m_syncFlag = add;
    return success;
}

void Layer::GetCanvasLayoutRect(Gdiplus::RectF* rect)
{
    CANVAS_INFO_EX info;
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(m_layerInfo.canvasID);
    if (pCanvas)
    {
        pCanvas->GetCanvasInfo(&info);
    }
    *rect = { info.layoutRect.X, info.layoutRect.Y , info.layoutRect.Width, info.layoutRect.Height };
}

void Layer::AddFilter(UINT64 id, FILTER* info)
{
    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), id);
    if (st == info->mediaIDs.end())
    {
        info->mediaIDs.push_back(id);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(WARNING) << "[Layer::AddFilter] same filter add to same layer";
        return;
    }

    auto it = std::find_if(m_layerInfo.filters.begin(), m_layerInfo.filters.end(), [info](FILTER filterInfo) {
        return info->id == filterInfo.id;
        });
    if (it == m_layerInfo.filters.end())
    {
        m_layerInfo.filters.push_back(*info);
    }
    else
    {
        LOG(WARNING) << "[Layer::AddFilter] same layer added same filter";
        return;
    }

    if (m_layerInfo.isCreated)
    {
        bool ret = g_mediaMgr->AddFilter(id, *info);
        if (!ret && info->type != FILTER_EFFECT)
        {
            LOG(ERROR) << "[Layer::AddFilter] AddFilter failed, filterID: " << id << " filterType: " << info->type;
            info->isCreated = false;
            return;
        }

        info->isCreated = true;
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
}

void Layer::RemoveFilter(UINT64 id, FILTER* info)
{
    auto it = std::find_if(m_layerInfo.filters.begin(), m_layerInfo.filters.end(), [info](const FILTER& filterInfo){
        return info->id == filterInfo.id;
        });
    if (it != m_layerInfo.filters.end())
    {
        m_layerInfo.filters.erase(it);
    }
    else
    {
        LOG(ERROR) << "[Layer::RemoveFilter] layer have not filter";
        return;
    }

    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), id);
    if (st != info->mediaIDs.end())
    {
        info->mediaIDs.erase(st);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(ERROR) << "[Layer::RemoveFilter] filter do not belong to any layer";
        return;
    }

    if (info->isCreated)
        g_mediaMgr->RemoveFilter(id, *info);
}

void Layer::SetLayerPreview(UINT64 previewID, const LAYER_PREVIEW* preview)
{
    m_layerPreview[previewID] = *preview;
}

void Layer::GetLayerPreview(UINT64 previewID, LAYER_PREVIEW* preview)
{
    *preview = m_layerPreview[previewID];
}

void Layer::SetLayerLayout(const Gdiplus::RectF* curRect)
{
    if (m_layerInfo.layout == LAYOUT_COVER_RATIO_FILL)
    {
        SetLayerCoverRatioFill(curRect);
    }
    else if (m_layerInfo.layout == LAYOUT_CONTAIN_RATIO_FILL)
    {
        SetLayerContainRatioFill(curRect);
    }
    else if (m_layerInfo.layout == LAYOUT_TILE_FILL)
    {
        SetLayerTileFill(curRect);
    }
    else if (m_layerInfo.layout == LAYOUT_CENTERED)
    {
        SetLayerToCenter(curRect);
    }
    else if (m_layerInfo.layout == LAYOUT_HORIZONTAL_CENTERED)
    {
        SetLayerToHorizontalCenter(curRect);
    }
    else if (m_layerInfo.layout == LAYOUT_VERTICAL_CENTERED)
    {
        SetLayerToVerticalCenter(curRect);
    }
}

void Layer::SetLayerSize(const Gdiplus::SizeF* oldSize, const Gdiplus::SizeF* newSize)
{
    float ratiox = 1.0f;
    if (std::abs(oldSize->Width - newSize->Width) > EPS)
    {
        ratiox = newSize->Width * 1.0f / oldSize->Width;
        m_layerInfo.transform.scale.X *= ratiox;
        m_layerInfo.transform.scale.Y *= ratiox;
        m_layerInfo.transform.translate.X *= ratiox;
        m_layerInfo.transform.translate.Y *= ratiox;
    }
}

void Layer::UpdateLayerTransform(const TRANSFORM* transform)
{
    bool transformChanged = false;
    TRANSFORM& trans = m_layerInfo.transform;
    if (trans.hFlip != transform->hFlip)
    {
        trans.hFlip = transform->hFlip;
        transformChanged = true;
    }
    if (trans.vFlip != transform->vFlip)
    {
        trans.vFlip = transform->vFlip;
        transformChanged = true;
    }
    if (std::abs(trans.angle - transform->angle) > EPS)
    {
        trans.angle = transform->angle;
        transformChanged = true;
    }
    if (std::abs(trans.scale.X - transform->scale.X) > EPS)
    {
        trans.scale.X = transform->scale.X;
        transformChanged = true;
    }
    if (std::abs(trans.scale.Y - transform->scale.Y) > EPS)
    {
        trans.scale.Y = transform->scale.Y;
        transformChanged = true;
    }
    if (std::abs(trans.translate.X - transform->translate.X) > EPS)
    {
        trans.translate.X = transform->translate.X;
        transformChanged = true;
    }
    if (std::abs(trans.translate.Y - transform->translate.Y) > EPS)
    {
        trans.translate.Y = transform->translate.Y;
        transformChanged = true;
    }
    if (std::abs(trans.clipRange.x - transform->clipRange.x) > EPS)
    {
        trans.clipRange.x = transform->clipRange.x;
        transformChanged = true;
    }
    if (std::abs(trans.clipRange.y - transform->clipRange.y) > EPS)
    {
        trans.clipRange.y = transform->clipRange.y;
        transformChanged = true;
    }
    if (std::abs(trans.clipRange.z - transform->clipRange.z) > EPS)
    {
        trans.clipRange.z = transform->clipRange.z;
        transformChanged = true;
    }
    if (std::abs(trans.clipRange.w - transform->clipRange.w) > EPS)
    {
        trans.clipRange.w = transform->clipRange.w;
        transformChanged = true;
    }

    if (transformChanged)
        m_layerInfo.layout = LAYOUT_NONE;
}

void Layer::GetLayerDescAngle(float* angle)
{
	SOURCE_INFO sourceInfo{};
	SourceMgr::GetInstance()->GetSourceInfoByID(m_layerInfo.sourceID, &sourceInfo);
    if (sourceInfo.type == VISUAL_FAV)
    {
        FAV_SOURCE source = std::get<FAV_SOURCE>(sourceInfo.source);
        *angle = source.materialDesc.angle;
    }
    else if (sourceInfo.type == VISUAL_IMAGE)
    {
        IMAGE_SOURCE source = std::get<IMAGE_SOURCE>(sourceInfo.source);
        *angle = source.materialDesc.angle;
    }
}

void Layer::UpdateVISAudioInfo(const AUDIO_INFO* info)
{
    SOURCE_INFO sourceInfo{};
    SourceMgr::GetInstance()->GetSourceInfoByID(m_layerInfo.sourceID, &sourceInfo);
    if (sourceInfo.type == VISUAL_FAV)
    {
        FAV_SOURCE source = std::get<FAV_SOURCE>(sourceInfo.source);
        source.audioTrack = info->audioTrack;
        source.audioSetting = info->audioSetting;
        sourceInfo.source = source;
    }
    else if (sourceInfo.type == VISUAL_ANALOG/* || sourceInfo.type == VISUAL_CAMERA*/)
    {
        ANALOG_SOURCE source = std::get<ANALOG_SOURCE>(sourceInfo.source);
        source.audioTrack = info->audioTrack;
        source.audioSetting = info->audioSetting;
       sourceInfo.source = source;
    }
    else if (sourceInfo.type == VISUAL_BYTELINK)
    {
        BYTELINK_SOURCE source = std::get<BYTELINK_SOURCE>(sourceInfo.source);
        source.audioTrack = info->audioTrack;
        source.audioSetting = info->audioSetting;
        sourceInfo.source = source;
    }
    else if (sourceInfo.type == VISUAL_RTC)
    {
        RTC_SOURCE source = std::get<RTC_SOURCE>(sourceInfo.source);
        source.audioTrack = info->audioTrack;
        sourceInfo.source = source;
    }
    SourceMgr::GetInstance()->SetSourceInfoByID(m_layerInfo.sourceID, sourceInfo);
}

void Layer::CalLayerTransform(UINT64& cmd, UINT64 canvasID /* = 0 */)
{
    UINT64 tmpCanvasID = m_layerInfo.canvasID;
    if (canvasID != 0)
        m_layerInfo.canvasID = canvasID;

    Gdiplus::RectF canvasLayoutRect{};
    GetCanvasLayoutRect(&canvasLayoutRect);
    if (canvasLayoutRect.Width > EPS && canvasLayoutRect.Height > EPS)
    {
        if (cmd & LAYER_CONTROL_SET_TARGET_SIZE)
        {
            Gdiplus::SizeF tarLayoutSize = {m_layerInfo.targetSize.Width, m_layerInfo.targetSize.Height};
            if (tarLayoutSize.Width > EPS && tarLayoutSize.Height > EPS &&
                (std::abs(canvasLayoutRect.Width - tarLayoutSize.Width) > EPS || std::abs(canvasLayoutRect.Height - tarLayoutSize.Height) > EPS))
            {
                Gdiplus::SizeF canvasLayoutSize = {canvasLayoutRect.Width, canvasLayoutRect.Height};
                SetLayerSize(&tarLayoutSize, &canvasLayoutSize);
                cmd |= LAYER_CONTROL_SET_TRANSLATE | LAYER_CONTROL_SET_SCALE;
            }
        }

        if (cmd & LAYER_CONTROL_SET_LAYOUT)
        {
            if (m_layerInfo.transform.size.Width > EPS && m_layerInfo.transform.size.Height > EPS && m_layerInfo.layout != LAYOUT_NONE)
            {
                if (cmd & LAYER_CONTROL_SET_REF_LAYOUT && m_layerInfo.refLayout.z > EPS && m_layerInfo.refLayout.w > EPS)
                {
                    canvasLayoutRect.X = m_layerInfo.refLayout.x * canvasLayoutRect.Width;
                    canvasLayoutRect.Y = m_layerInfo.refLayout.y * canvasLayoutRect.Height;
                    canvasLayoutRect.Width = canvasLayoutRect.Width * m_layerInfo.refLayout.z;
                    canvasLayoutRect.Height = canvasLayoutRect.Height * m_layerInfo.refLayout.w;
                }

                SetLayerLayout(&canvasLayoutRect);
                cmd |= LAYER_CONTROL_SET_TRANSLATE | LAYER_CONTROL_SET_SCALE;
            }
        }
    }

    m_layerInfo.canvasID = tmpCanvasID;
}

void Layer::ReCalLayerTransform(float width, float height)
{
    m_layerInfo.isReady = true;
    UINT64 cmd = LAYER_CONTROL_NONE;

    bool sizeChange = (width > EPS && height > EPS) && (std::abs(width - m_layerInfo.transform.size.Width) > EPS || std::abs(height - m_layerInfo.transform.size.Height) > EPS);

    SOURCE_INFO sourceInfo{};
    SourceMgr::GetInstance()->GetSourceInfoByID(m_layerInfo.sourceID, &sourceInfo);
    if (sourceInfo.type == VISUAL_BYTELINK)
    {
        if (m_layerInfo.transform.clipRange.x > EPS || m_layerInfo.transform.clipRange.y > EPS || m_layerInfo.transform.clipRange.z > EPS || m_layerInfo.transform.clipRange.w > EPS)
        {
            m_layerInfo.transform.clipRange = {.0f, .0f, .0f, .0f};
            cmd |= LAYER_CONTROL_SET_CLIP;
        }

        cmd |= LAYER_CONTROL_SET_ROTATE;
        m_layerInfo.layout = LAYOUT_CONTAIN_RATIO_FILL;

        sizeChange = true;
    }

    if (sizeChange)
    {
        if (m_layerInfo.transform.size.Width > EPS && m_layerInfo.transform.size.Height > EPS && m_layerInfo.fixedEdge != FIXED_EDGE_NONE)
        {
            float previewWidth = m_layerInfo.transform.size.Width * m_layerInfo.transform.scale.X;
            float previewHeight = m_layerInfo.transform.size.Height * m_layerInfo.transform.scale.Y;
            float fixedScale = 1.0f;
            if (m_layerInfo.fixedEdge == FIXED_EDGE_WIDTH)
            {
                fixedScale = previewWidth / width;
            }
            else if (m_layerInfo.fixedEdge == FIXED_EDGE_HEIGHT)
            {
                fixedScale = previewHeight / height;
            }
            else if (m_layerInfo.fixedEdge == FIXED_EDGE_LONGEST)
            {
                float oldLongest = (previewWidth - previewHeight) > EPS ? previewWidth : previewHeight;
                float newLongest = (width - height) > EPS ? width : height;
                fixedScale = oldLongest / newLongest;
            }
            else if (m_layerInfo.fixedEdge == FIXED_EDGE_SHORTEST)
            {
                float old_shortest = (previewWidth - previewHeight) > EPS ? previewHeight : previewWidth;
                float new_shortest = (width - height) > EPS ? height : width;
                fixedScale = old_shortest / new_shortest;
            }
            m_layerInfo.transform.scale.X = fixedScale;
            m_layerInfo.transform.scale.Y = fixedScale;
            cmd |= LAYER_CONTROL_SET_SCALE | LAYER_CONTROL_SET_MIN_SCALE | LAYER_CONTROL_SET_MAX_SCALE;

            if (m_layerInfo.transform.clipRange.x > EPS || m_layerInfo.transform.clipRange.y > EPS ||
                m_layerInfo.transform.clipRange.z > EPS || m_layerInfo.transform.clipRange.w > EPS)
            {
                float clip_scale = width / m_layerInfo.transform.size.Width;
                m_layerInfo.transform.clipRange.x *= clip_scale;
                m_layerInfo.transform.clipRange.y *= clip_scale;
                m_layerInfo.transform.clipRange.z *= clip_scale;
                m_layerInfo.transform.clipRange.w *= clip_scale;
                cmd |= LAYER_CONTROL_SET_CLIP;
            }
        }

        m_layerInfo.transform.size.Width = width;
        m_layerInfo.transform.size.Height = height;

        if (m_layerInfo.prepareClip.x > EPS || m_layerInfo.prepareClip.y > EPS || m_layerInfo.prepareClip.z > EPS || m_layerInfo.prepareClip.w > EPS)
            cmd |= LAYER_CONTROL_SET_PREPARE_CLIP;

        if (m_layerInfo.layout != LAYOUT_NONE)
            cmd |= LAYER_CONTROL_SET_LAYOUT;

        if (m_layerInfo.refLayout.z > 0 && m_layerInfo.refLayout.w > 0)
            cmd |= LAYER_CONTROL_SET_REF_LAYOUT;

        ControlLayer(m_layerInfo, static_cast<LAYER_CONTROL_CMD>(cmd));
    }
}

void Layer::GetClippedSize(Gdiplus::SizeF* csize)
{
    CLIPF          clip = m_layerInfo.transform.clipRange;
    Gdiplus::SizeF clipSize = { clip.x + clip.z, clip.y + clip.w };

    *csize = { m_layerInfo.transform.size.Width, m_layerInfo.transform.size.Height };
    csize->Width = clipSize.Width > csize->Width ? csize->Width : csize->Width - clipSize.Width;
    csize->Height = clipSize.Height > csize->Height ? csize->Height : csize->Height - clipSize.Height;
}

bool Layer::GetRotatedSize(const Gdiplus::SizeF* size, Gdiplus::SizeF* rsize)
{
    UINT32 angle = static_cast<UINT32>(std::abs(m_layerInfo.transform.angle)) % 360;
    *rsize = *size;
    if (angle == 90 || angle == 270)
    {
        rsize->Width = size->Height;
        rsize->Height = size->Width;
        return true;
    }
    return false;
}

void Layer::SetLayerToCenter(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);
    Gdiplus::PointF scale = m_layerInfo.transform.scale;

    float translateX = (rect->Width - cSize.Width * scale.X) / 2.0f + rect->X;
    float translateY = (rect->Height - cSize.Height * scale.Y) / 2.0f + rect->Y;
    m_layerInfo.transform.translate = { translateX, translateY };
}

void Layer::SetLayerToHorizontalCenter(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);
    Gdiplus::PointF scale = m_layerInfo.transform.scale;

    float translateX = (rect->Width - cSize.Width * scale.X) / 2.0f + rect->X;
    m_layerInfo.transform.translate.X = translateX;
}

void Layer::SetLayerToVerticalCenter(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);
    Gdiplus::PointF scale = m_layerInfo.transform.scale;

    float translateY = (rect->Height - cSize.Height * scale.Y) / 2.0f + rect->Y;
    m_layerInfo.transform.translate.Y = translateY;
}

void Layer::SetLayerCoverRatioFill(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);

    Gdiplus::SizeF rSize;
    GetRotatedSize(&cSize, &rSize);

    float cRatio = rect->Width * 1.0f / rect->Height;
    float lRatio = rSize.Width * 1.0f / rSize.Height;

    float scale = 1.0f;
    if (cRatio - lRatio > EPS)
        scale = rect->Width * 1.0f / rSize.Width;
    else
        scale = rect->Height * 1.0f / rSize.Height;

    float translateX = (rect->Width - cSize.Width * scale) / 2.0f + rect->X;
    float translateY = (rect->Height - cSize.Height * scale) / 2.0f + rect->Y;

    m_layerInfo.transform.scale = { scale, scale };
    m_layerInfo.transform.translate = { translateX, translateY };
}

void Layer::SetLayerContainRatioFill(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);

    Gdiplus::SizeF rSize;
    GetRotatedSize(&cSize, &rSize);

    float canvasRatio = rect->Width * 1.0f / rect->Height;
    float layerRatio = rSize.Width * 1.0f / rSize.Height;
    float scale = 1.0f;
    if (canvasRatio - layerRatio > EPS)
        scale = rect->Height * 1.0f / rSize.Height;
    else
        scale = rect->Width * 1.0f / rSize.Width;
    float translateX = (rect->Width - cSize.Width * scale) / 2.0f + rect->X;
    float translateY = (rect->Height - cSize.Height * scale) / 2.0f + rect->Y;

    m_layerInfo.transform.scale = { scale, scale };
    m_layerInfo.transform.translate = { translateX, translateY };
}

void Layer::SetLayerTileFill(const Gdiplus::RectF* rect)
{
    Gdiplus::SizeF cSize;
    GetClippedSize(&cSize);

    Gdiplus::SizeF rSize;
    bool rotated = GetRotatedSize(&cSize, &rSize);

    Gdiplus::PointF scale;
    scale.X = rect->Width * 1.0f / rSize.Width;
    scale.Y = rect->Height * 1.0f / rSize.Height;
    if (rotated)
    {
        float tmp = scale.X;
        scale.X = scale.Y;
        scale.Y = tmp;
    }

    float translateX = (rect->Width - cSize.Width * scale.X) / 2.0f + rect->X;
    float translateY = (rect->Height - cSize.Height * scale.Y) / 2.0f + rect->Y;

    m_layerInfo.transform.scale = scale;
    m_layerInfo.transform.translate = { translateX, translateY };
}
