#include "stdafx.h"
#include "analog.h"
#include "visual.h"
#include "audio/audio.h"
#include "../BaseLib/event_bus.h"

namespace LS
{
Analog::RequestList Analog::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Analog::EnumVideoAnalogs>());
    list.push_back(std::make_unique<Analog::EnumAudioAnalogs>());
    list.push_back(std::make_unique<Analog::EnumFormats>());
    list.push_back(std::make_unique<Analog::EnumAudioCapFormatWithDShowName>());

    list.push_back(std::make_unique<Analog::Create>());
    list.push_back(std::make_unique<Analog::Reopen>());
    list.push_back(std::make_unique<Analog::GetAnalogInfo>());
    return list;
}

bool Analog::EnumVideoAnalogs::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> oVideoDevices{};
    controller->EnumVideoDevices(&oVideoDevices);
    for (int i = 0; i < oVideoDevices.size(); ++i)
    {
        DSHOW device = oVideoDevices[i];
        auto ref = rsp.add_video_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
    }

    return true;
}

bool Analog::EnumAudioAnalogs::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> oAudioDevices{};
    controller->EnumAudioDevices(&oAudioDevices);
    for (int i = 0; i < oAudioDevices.size(); ++i)
    {
        DSHOW device = oAudioDevices[i];
        auto  ref = rsp.add_audio_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
    }

    return true;
}

bool Analog::EnumFormats::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    DSHOW device{};
    device.id = req.device().id();
    device.name = req.device().name();

    std::vector<VIDEO_CAPTURE_FORMAT> oVideoCapFormats{};
    std::vector<AUDIO_CAPTURE_FORMAT> oAudioCapFormats{};
    controller->EnumCaptureFormats(device, &oVideoCapFormats, &oAudioCapFormats);
    for (int i = 0; i < oVideoCapFormats.size(); ++i)
    {
        VIDEO_CAPTURE_FORMAT videoCapFormat = oVideoCapFormats[i];
        auto                ref = rsp.add_video_cap_formats();
        ref->set_format((ls_basicenum::VIDEO_PIXEL_FORMAT)videoCapFormat.format);
        ls_base::SizeF size{};
        size.set_x(videoCapFormat.width);
        size.set_y(videoCapFormat.height);
        ref->mutable_size()->CopyFrom(size);
        ref->set_rate(videoCapFormat.rate);
        ref->set_max_rate(videoCapFormat.maxRate);
        ref->set_min_rate(videoCapFormat.minRate);
    }

    for (int i = 0; i < oAudioCapFormats.size(); ++i)
    {
        AUDIO_CAPTURE_FORMAT audioFormat = oAudioCapFormats[i];
        auto                ref = rsp.add_audio_cap_formats();
        ref->set_render_type((ls_basicenum::ANALOG_RENDER_TYPE)audioFormat.renderType);
        ref->set_sample_rate(audioFormat.sampleRate);
        ref->set_channels(audioFormat.channels);
        ref->set_bits_per_sample(audioFormat.bitsPerSample);
    }

    return true;
}

bool Analog::EnumAudioCapFormatWithDShowName::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    DSHOW device{};
    device.id = req.device().id();
    device.name = req.device().name();

    std::vector<AUDIO_CAPTURE_FORMAT> oAudioCapFormats{};
    controller->EnumAudioCapFormatWithDShowName(device, &oAudioCapFormats);
    for (int i = 0; i < oAudioCapFormats.size(); ++i)
    {
        AUDIO_CAPTURE_FORMAT audioFormat = oAudioCapFormats[i];
        auto                ref = rsp.add_audio_cap_formats();
        ref->set_render_type(static_cast<ls_basicenum::ANALOG_RENDER_TYPE>(audioFormat.renderType));
        ref->set_sample_rate(audioFormat.sampleRate);
        ref->set_channels(audioFormat.channels);
        ref->set_bits_per_sample(audioFormat.bitsPerSample);
    }

    return true;
}

void Analog::GetAnalogSource(ANALOG_SOURCE& analogSource, const ls_analog::CreateParam& create_param)
{
    analogSource.openCuda = create_param.open_cuda();

    if (create_param.has_video_cap_format())
    {
        VIDEO_CAPTURE_FORMAT videoCapFormat{};
        const ls_base::VideoCaptureFormat& video_cap_format = create_param.video_cap_format();
        videoCapFormat.format = (VIDEO_PIXEL_FORMAT)video_cap_format.format();
        videoCapFormat.width = video_cap_format.size().x();
        videoCapFormat.height = video_cap_format.size().y();
        videoCapFormat.rate = video_cap_format.rate();
        videoCapFormat.maxRate = video_cap_format.max_rate();
        videoCapFormat.minRate = video_cap_format.min_rate();
        analogSource.videoCapFormat = videoCapFormat;
    }

    if (create_param.has_video_device())
    {
        DSHOW video_device{};
        video_device.id = create_param.video_device().id();
        video_device.name = create_param.video_device().name();
        analogSource.videoDevice = video_device;
    }

    if (create_param.has_color_config())
    {
        analogSource.colorConfig.colorSpace = (COLOR_SPACE)create_param.color_config().color_space();
        analogSource.colorConfig.colorTransfer = (COLOR_TRANSFER)create_param.color_config().color_transfer();
        analogSource.colorConfig.colorRange = (COLOR_RANGE)create_param.color_config().color_range();
    }

    if (create_param.has_audio_track())
    {
        analogSource.audioTrack = create_param.audio_track();
    }

    if (create_param.has_audio_device())
    {
        DSHOW audio_device{};
        audio_device.id = create_param.audio_device().id();
        audio_device.name = create_param.audio_device().name();
        analogSource.audioDevice = audio_device;
    }

    if (create_param.has_audio_cap_format())
    {
        ls_base::AudioCaptureFormat audio_cap_format = create_param.audio_cap_format();
        AUDIO_CAPTURE_FORMAT          audioCapFormat{};
        audioCapFormat.renderType = (ANALOG_RENDER_TYPE)audio_cap_format.render_type();
        audioCapFormat.sampleRate = audio_cap_format.sample_rate();
        audioCapFormat.channels = audio_cap_format.channels();
        audioCapFormat.bitsPerSample = audio_cap_format.bits_per_sample();
        analogSource.audioCapFormat = audioCapFormat;
    }

    if (create_param.has_audio_setting())
    {
        AUDIO_SETTING& audioSetting = analogSource.audioSetting;
        const ls_audio::AudioSettingParam& audio_setting_param = create_param.audio_setting();
        Audio::SetVISAudioInfo(audioSetting, audio_setting_param);
    }
}

bool Analog::Create::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    LAYER_INFO iLayerInfo{};
    SOURCE_INFO iSourceInfo{};
    Visual::SetVisualUniformInfo(iLayerInfo, iSourceInfo, req.visual_uni_attr());

    ANALOG_SOURCE analogSource{};
    Analog::GetAnalogSource(analogSource, req.create_param());

    iSourceInfo.type = VISUAL_TYPE::VISUAL_ANALOG;
    iSourceInfo.source = analogSource;

    if (!controller->CreateSource(iSourceInfo))
    {
        LOG(ERROR) << "[Analog::Create] CreateSource failed, sourceID: " << iSourceInfo.id;
        return false;
    }

	std::string source_id = "";
	Util::NumToString(iSourceInfo.id, &source_id);
	rsp.set_source_id(source_id);

    iLayerInfo.sourceID = iSourceInfo.id;
    controller->CreateLayer(&iLayerInfo);

    iSourceInfo.layerIDs.push_back(iLayerInfo.id);
    controller->SetSourceInfo(iSourceInfo.id, &iSourceInfo);

    std::string visual_id = "";
    Util::NumToString(iLayerInfo.id, &visual_id);
    rsp.set_visual_id(visual_id);

    if (!controller->FindLayerByID(iLayerInfo.id))
    {
        LOG(ERROR) << "[Analog::Create] AddLayer failed, visualID: " << iLayerInfo.id;
        return false;
    }

    return true;
}

bool Analog::Reopen::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Analog::Reopen] visual not exist, visualID: " << visualID;
        return false;
    }

	LAYER_INFO iLayerInfo{};
	controller->GetLayerInfo(visualID, &iLayerInfo);

    SOURCE_INFO iSourceInfo{};
    controller->GetSourceInfo(iLayerInfo.sourceID, &iSourceInfo);

    ANALOG_SOURCE analogSource = std::get<ANALOG_SOURCE>(iSourceInfo.source);
    Analog::GetAnalogSource(analogSource, req.create_param());
    iSourceInfo.source = analogSource;

    return controller->ReopenSource(iSourceInfo.id, iSourceInfo);
}

bool Analog::GetAnalogInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 visualID = 0;
    Util::StringToNum(req.visual_id(), &visualID);
    if (!controller->FindLayerByID(visualID))
    {
        LOG(ERROR) << "[Analog::GetAnalogInfo] visual not exist, visualID: " << visualID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(visualID, &oLayerInfo);

    SOURCE_INFO oSourceInfo{};
    controller->GetSourceInfo(oLayerInfo.sourceID, &oSourceInfo);

    ANALOG_SOURCE analogSource = std::get<ANALOG_SOURCE>(oSourceInfo.source);
    ls_analog::CreateParam analog_info;
    analog_info.set_open_cuda(analogSource.openCuda);

    ls_base::DShow video_device{};
    video_device.set_id(analogSource.videoDevice.id);
    video_device.set_name(analogSource.videoDevice.name);
    analog_info.mutable_video_device()->CopyFrom(video_device);

    ls_base::VideoCaptureFormat video_cap_foramt{};
    ls_base::SizeF size{};
    size.set_x(analogSource.videoCapFormat.width);
    size.set_y(analogSource.videoCapFormat.height);
    video_cap_foramt.mutable_size()->CopyFrom(size);

    video_cap_foramt.set_format((ls_basicenum::VIDEO_PIXEL_FORMAT)analogSource.videoCapFormat.format);
    video_cap_foramt.set_rate(analogSource.videoCapFormat.rate);
    video_cap_foramt.set_min_rate(analogSource.videoCapFormat.minRate);
    video_cap_foramt.set_max_rate(analogSource.videoCapFormat.maxRate);
    analog_info.mutable_video_cap_format()->CopyFrom(video_cap_foramt);
    
    ls_base::AudioCaptureFormat audio_cap_format{};
    audio_cap_format.set_render_type((ls_basicenum::ANALOG_RENDER_TYPE)analogSource.audioCapFormat.renderType);
    audio_cap_format.set_channels(analogSource.audioCapFormat.channels);
    audio_cap_format.set_sample_rate(analogSource.audioCapFormat.sampleRate);
    audio_cap_format.set_bits_per_sample(analogSource.audioCapFormat.bitsPerSample);
    analog_info.mutable_audio_cap_format()->CopyFrom(audio_cap_format);

    ls_base::DShow audio_device{};
    audio_device.set_id(analogSource.audioDevice.id);
    audio_device.set_name(analogSource.audioDevice.name);
    analog_info.mutable_audio_device()->CopyFrom(audio_device);

    ls_base::ColorConfig color_config{};
    color_config.set_color_space((ls_basicenum::COLOR_SPACE)analogSource.colorConfig.colorSpace);
    color_config.set_color_transfer((ls_basicenum::COLOR_TRANSFER)analogSource.colorConfig.colorTransfer);
    color_config.set_color_range((ls_basicenum::VIDEO_RANGE)analogSource.colorConfig.colorRange);
    analog_info.mutable_color_config()->CopyFrom(color_config);

    analog_info.set_audio_track(analogSource.audioTrack);
    ls_audio::AudioSettingParam setting_param{};
    setting_param.set_balanceing(analogSource.audioSetting.balanceing);
    setting_param.set_down_mix_mono(analogSource.audioSetting.downMixMono);
    setting_param.set_interval(analogSource.audioSetting.interval);
    setting_param.set_monitor_type((ls_audio::AUDIO_MONITOR_TYPE)analogSource.audioSetting.monitorType);
    setting_param.set_mute(analogSource.audioSetting.mute);
    setting_param.set_sync_offset(analogSource.audioSetting.syncOffset);
    setting_param.set_volume(analogSource.audioSetting.volume);
    analog_info.mutable_audio_setting()->CopyFrom(setting_param);

    rsp.mutable_source_info()->CopyFrom(analog_info);
    return true;
}
} // namespace MediaSDK