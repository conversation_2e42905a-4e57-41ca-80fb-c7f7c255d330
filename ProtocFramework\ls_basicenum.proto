// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_basicenum;


enum VIDEO_ENGINE_MODE
{
    VIDEO_ENGINE_NORMAL = 0;
    VIDEO_ENGINE_STUDIO = 1;
};

// LOG
enum LOG_LEVEL {
	INFO = 0;
	WARNING = 1;
	ERROR = 2;
	FATAL = 3;
};

enum LOG_TYPE {
	CONNECT_START = 0;
	CONNECT_STOP = 1;
	PUSH_STREAM = 2;
	PUSH_STREAM_FAIL= 3;
};

// ERROR
enum ERROR_TYPE {
	SUCCESS = 0;
	METHOD_NOT_FOUND = 1;
	ERROR_UNKNOWN = 2;
	ALREADY_RUNNING = 3;
	DEPRECETED = 4;
	PLUGIN_NOT_FOUND = 5;

	AUDIO_OUTPUT_CREATE_FAILED = 6;
	AUDIO_OUTPUT_DESTROY_FAILED = 7;
	AUDIO_INPUT_CREATE_FAILED = 8;
	AUDIO_INPUT_DESTROY_FAILED = 9;
    AUDIO_FILTER_CREATE_FAILED = 10;
	AUDIO_FILTER_DESTROY_FAILED = 11;

	VISUAL_CREATE_FAILED = 12;
	VISUAL_DESTROY_FAILED = 13;
	VISUAL_FILTER_CREATE_FAILED = 14;
	VISUAL_FILTER_DESTORY_FAILED = 15;

	// MODEL
	MODEL_NOT_FOUND = 1001;

	// screen open result
	SCREENSHOT_OPEN_SUCCESS = 10100;
	SCREENSHOT_ERROR_RECTANGLE = 10101;
	SCREENSHOT_OTHER_ERROR = 10102;

	// motion capture open result
	MOTION_CAPTURE_OPEN_SUCCESS = 10200;
	MOTION_CAPTURE_PORT_OCCUPIED = 10201;
	MOTION_CAPTURE_OTHER_ERROR = 10202;

	// image
    IMAGE_OPEN_RESULT = 10300;
    IMAGE_OPEN_FAILED = 10301;
    IMAGE_GIF_TOO_BIG = 10302;
};

enum VIDEO_PIXEL_FORMAT
{
    PIXEL_FORMAT_UNKNOWN = 0;
    PIXEL_FORMAT_I420 = 1;
    PIXEL_FORMAT_YV12 = 2;
    PIXEL_FORMAT_NV12 = 3;
    PIXEL_FORMAT_NV21 = 4;
    PIXEL_FORMAT_UYVY = 5;
    PIXEL_FORMAT_YUY2 = 6;
    PIXEL_FORMAT_ARGB = 7;
    PIXEL_FORMAT_XRGB = 8;
    PIXEL_FORMAT_RGB24 = 9;
    PIXEL_FORMAT_RGBA = 10;
    PIXEL_FORMAT_BGR24 = 11;
    PIXEL_FORMAT_BGRA = 12;
    PIXEL_FORMAT_MJPEG = 13;
    PIXEL_FORMAT_I444 = 14;
    PIXEL_FORMAT_I444A = 15;
    PIXEL_FORMAT_I420A = 16;
    PIXEL_FORMAT_I422 = 17;
    PIXEL_FORMAT_I422A = 18;
    PIXEL_FORMAT_YVYU = 19;
    PIXEL_FORMAT_P010 = 20;
    PIXEL_FORMAT_P016 = 21;
    PIXEL_FORMAT_NV12_MS = 22;
    PIXEL_FORMAT_P010_MS = 23;
    PIXEL_FORMAT_P016_MS = 24;
    PIXEL_FORMAT_I010 = 25;
    PIXEL_FORMAT_V210 = 26;
    PIXEL_FORMAT_I210 = 27;
    PIXEL_FORMAT_HDYC = 28;
};

enum COLOR_SPACE
{
    COLOR_SPACE_UNSPECIFIED = 0;
    COLOR_SPACE_BT709 = 1;
    COLOR_SPACE_BT601 = 2;
    COLOR_SPACE_FCC = 3;
    COLOR_SPACE_SMPTE240M = 4;
    COLOR_SPACE_BT470 = 5;
    COLOR_SPACE_BT2020 = 6; // HDR
    COLOR_SPACE_BT2100 = 7; // HDR10
};

enum COLOR_TRANSFER
{
    COLOR_TRANSFER_UNSPECIFIED = 0;
    COLOR_TRANSFER_LINEAR = 1;
    COLOR_TRANSFER_IEC61966_2_1 = 2; 
    COLOR_TRANSFER_GAMMA22 = 3;
    COLOR_TRANSFER_GAMMA28 = 4;
    COLOR_TRANSFER_SMPTE170M = 5;     // BT601
    COLOR_TRANSFER_SMPTE240M = 6;     // BT601
    COLOR_TRANSFER_BT709 = 7;         // BT709
    COLOR_TRANSFER_BT2020_10 = 8;     // BT2010 10BIT
    COLOR_TRANSFER_BT2020_12 = 9;     // BT2010 12BIT
    COLOR_TRANSFER_SMPTE2084 = 10;    // BT2100-PQ
    COLOR_TRANSFER_ARIB_STD_B67 = 11; // BT2100-HLG
};

enum VIDEO_RANGE
{
    VIDEO_RANGE_UNSPECIFIED = 0;
    VIDEO_RANGE_PARTIAL = 1;
    VIDEO_RANGE_FULL = 2;
};

enum FILTER_TYPE {
	FILTER_NONE = 0;

    // VideoFilter
    FILTER_COLOR_KEY = 1;
    FILTER_CHROMA_KEY = 2;
    FILTER_COLOR_CORRECTION = 3;
    FILTER_WATER_MARK = 4;
    FILTER_TEXTWATER_MARK = 5;
    FILTER_SOBELEDGE = 6;
    FILTER_LUT_MAP = 7;
    FILTER_COLOR_RGB = 8;
    FILTER_COLOR_Y = 9;
	FILTER_ERP = 10;
    FILTER_SCROLL = 11;
    FILTER_COLOR_GRADE = 12;
    FILTER_GAUSSIAN = 13;
    FILTER_CROP = 14;
	FILTER_CORNER = 15;

    // AudioFilter
    FILTER_GAIN = 50;
    FILTER_SPEEX_NOISE_SUPPRESS = 51;
    FILTER_SAMI = 52;
    FILTER_SAMI_NOISE_SUPPRESS = 53;
    FILTER_PITCH_SHIFT = 54;

    // EffectFilter
    FILTER_COMPOSER = 100;
    FILTER_BACKGROUND_IMAGE = 101;
};

enum IMAGE_FILE_FORMAT
{
    BMP = 0;
    JPG = 1;
    PNG = 2;
    TIFF = 3;
    GIF = 4;
    WMP = 5;
};

// RTC Stream
enum MEDIA_STREAM_TYPE 
{
    MEDIA_STREAM_NULL = 0;
    MEDIA_STREAM_AUDIO = 1;
    MEDIA_STREAM_VIDEO = 2;
    MEDIA_STREAM_BOTH = 3;
};

enum VIDEO_SOURCE_TYPE 
{
    VIDEO_SOURCE_EXTERNAL = 0;                       // Custom capture video source
    VIDEO_SOURCE_INTERNAL = 1;                       // Internal video capture source
    VIDEO_SOURCE_ENCODED_WITH_AUTO_SIMULCAST = 2;    // Custom encoded video source, only need to push the highest resolution video stream, SDK will automatically transcode to generate a multi-channel small stream
    VIDEO_SOURCE_ENCODED_WITHOUT_AUTO_SIMULCAST = 3; // Custom encoded video source, SDK don't automatically generate multi-streams, generate and push multi-streams by yourself
};

enum CREATE_SOURCE_FAILED_REASON
{
	CREATE_NOT_FAIL = 0;
	CREATE_INIT_FAIL = 1;
	CREATE_ALLOCATE_FAIL = 2;
	CREATE_EFFECT_NOT_SUPPORT_FAIL = 3;
	CREATE_START_FAIL = 4;
	CREATE_VISUAL_NOT_FIND = 5;
	CREATE_MODEL_NOT_FIND = 6;
    CREATE_PENDING = 7;
};

enum AUDIO_FORMAT
{
    AUDIO_FORMAT_UNKNOWN = 0;
    AUDIO_FORMAT_U8 = 1;
    AUDIO_FORMAT_S16 = 2;
    AUDIO_FORMAT_S32 = 3;
    AUDIO_FORMAT_FLOAT = 4;
    AUDIO_FORMAT_U8_PLANAR=5;
    AUDIO_FORMAT_S16_PLANAR = 6;
    AUDIO_FORMAT_S32_PLANAR = 7;
    AUDIO_FORMAT_FLOAT_PLANAR = 8;
}

enum AUDIO_CHANNEL_LAYOUT {
  CHANNEL_UNKNOWN = 0;
  CHANNEL_MONO = 1;
  CHANNEL_STEREO = 2;
  CHANNEL_2POINT1 = 3;
  CHANNEL_2_1 = 4;
  CHANNEL_2_2 = 5;
  CHANNEL_QUAD = 6;
  CHANNEL_4POINT0 = 7;
  CHANNEL_4POINT1 = 8;
  CHANNEL_5POINT0 = 9;
  CHANNEL_5POINT1 = 10;
  CHANNEL_5POINT0_BACK = 11;
  CHANNEL_5POINT1_BACK = 12;
  CHANNEL_6POINT0 = 13;
  CHANNEL_6POINT1 = 14;
  CHANNEL_7POINT0 = 15;
  CHANNEL_7POINT0_FRONT = 16;
  CHANNEL_7POINT1 = 17;
  CHANNEL_7POINT1_WIDE = 18;
  CHANNEL_7POINT1_WIDE_BACK = 19;
  CHANNEL_SURROUND = 20;
};

enum MOVE_ORDER
{
	MOVE_NONE = 0;
    MOVE_UP = 1;        // up one layer
    MOVE_DOWN = 2;      // down one layer
    MOVE_TOP = 3;       // top layer
    MOVE_BOTTOM = 4;    // bottom layer
};

enum VISUAL_LAYOUT
{
	LAYOUT_NONE = 0;
	LAYOUT_CONTAIN_RATIO_FILL = 1;
	LAYOUT_TILE_FILL = 2;
	LAYOUT_CENTERED = 3;
	LAYOUT_COVER_RATIO_FILL = 4;
	LAYOUT_HORIZONTAL_CENTERED = 5;
	LAYOUT_VERTICAL_CENTERED = 6;
};

enum OBJECT_FIT_MODE {
    OBJECT_FIT_MODE_CONTAIN = 0;
	OBJECT_FIT_MODE_COVER = 1;
}

enum ANALOG_RENDER_TYPE
{
    ANALOG_RENDER_NONE = 0;
    ANALOG_RENDER_WAVEOUT = 1;
    ANALOG_RENDER_DSOUND = 2;
};