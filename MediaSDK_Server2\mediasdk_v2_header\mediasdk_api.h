#pragma once

#include "hook_api/hook_api.h"
#include "mediasdk_audio_status_observer.h"
#include "mediasdk_callback_defines.h"
#include "mediasdk_defines.h"
#include "mediasdk_export.h"
#include "mediasdk_global_event_observer.h"
#include "mediasdk_stream_status_observer.h"
#include "mediasdk_window_event_observer.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

namespace mediasdk {

// Get MediaSDK version
// Synchronous result, char*, The return value does not need to be released
MEDIASDK_EXPORT const char* MS_API MediaSDKVersion();

// Synchronous return LogHandlerFunc is fun address
MEDIASDK_EXPORT void MS_API SetLogHandlerFunc(LogHandlerFunc func);

// Initialize the SDK, the observer is used to listen for global events in the
// SDK's internal callbacks, json_params is used to pass in additional
// initialization parameters (like:
// '{
//    "global_config":
//    {
//      "abr":
//      {
//        "disable_quic_strategy":true
//      }
//    },
//    "version":"0.66.6",
//    "project_key":"tiktok_live_studio",
//    "uid":"123456",
//    "did":"234567",
//    "parfait_host": "www.xxx.com",
//    "lyrax_audio_config"{...}
//   }'
// )
// , closure is an asynchronous callback, Please refer to the definitions of
// Closure and Callback Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API Initialize(MediaSDKGlobalEventObserver* observer,
                                       const char* json_params,
                                       hook_api::HookApi* hook_api,
                                       Closure closure);

// Uninitialize the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API Uninitialize(Closure closure);

// Register a object named MediaSDKWindowEventObserver to SDK, When event happed
// it will notify Ensure observer is valid before you call the
// UnregisterWindowEventObserver return Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterWindowEventObserver(MediaSDKWindowEventObserver* observer,
                            Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterWindowEventObserver(MediaSDKWindowEventObserver* observer,
                              Closure closure);

// Register observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterGlobalEventObserver(MediaSDKGlobalEventObserver* observer,
                            Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterGlobalEventObserver(MediaSDKGlobalEventObserver* observer,
                              Closure closure);

// Register a object named MediaSDKAudioStatusObserver to SDK, When event happed
// it will notify Ensure observer is valid before you call the
// UnregisterAudioObserver return Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterAudioObserver(MediaSDKAudioStatusObserver* observer, Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterAudioObserver(MediaSDKAudioStatusObserver* observer, Closure closure);

// Global Config json_info.
// The value of json will override the initialization parameters in the
// Initialize interface
/*
 */
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API UpdateGlobalConfig(const char* json_info,
                                               Closure closure);

// A\B Test Config json_info
/*
 */
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API UpdateABConfig(const char* json_info,
                                           Closure closure);

// Show Preview Window by video_model_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API ShowPreviewWindow(uint32_t video_model_id,
                                              Closure closure);

// Hide Preview Window by video_model_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API HidePreviewWindow(uint32_t video_model_id,
                                              Closure closure);

// Get Preview Window position by video_model_id
// Asynchronous callback result, ResultBoolMSRect
MEDIASDK_EXPORT void MS_API GetPreviewPosition(uint32_t video_model_id,
                                               Closure closure);

// Set Preview Window position by video_model_id, and move to rect
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetPreviewPosition(uint32_t video_model_id,
                                               const MSRect& rect,
                                               Closure closure);

// Set and Clip Preview Window position by video_model_id, and move to pos_rect,
// clip according to the clip_rect region, and you can restore to the state
// before clipping by setting clip_rect to {0, 0, 0, 0} Asynchronous callback
// result, bool
MEDIASDK_EXPORT void MS_API SetAndClipPreviewPosition(uint32_t video_model_id,
                                                      const MSRect& pos_rect,
                                                      const MSClip& clip_rect,
                                                      Closure closure);

// Test all preview window is enable
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API IsAllPreviewEnable(Closure closure);

// Enable all Preview Render
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EnableAllPreview(bool enable, Closure closure);

// Test preview window is enable
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API
IsPreviewEnableWithVideoModelId(uint32_t video_model_id, Closure closure);

// Enable Preview Render by video_model_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
EnablePreviewWithVideoModelId(uint32_t video_model_id,
                              bool enable,
                              Closure closure);

// Enable VideoModel enter video pump if enable is true,
// or not enter video pump if enable is false.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVideoModelActive(uint32_t video_model_id,
                                                bool enable,
                                                Closure closure);

// Get VideoModel active status
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetVideoModelActive(uint32_t video_model_id,
                                                Closure closure);

// Create video_model_id's Projector window
// which parent is hwnd_parent and position is rect
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CreateProjector(const char* canvas_project_id,
                                            uint64_t hwnd_parent,
                                            uint32_t video_model_id,
                                            const MSRect& rect,
                                            const WndParams& params,
                                            Closure closure);

// Close video_model_id's Projector window
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CloseProjector(const char* canvas_project_id,
                                           uint32_t video_model_id,
                                           Closure closure);

// update sink_id's Projector window params
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetProjectorWndParams(const char* canvas_project_id,
                                                  uint32_t video_model_id,
                                                  const WndParams& params,
                                                  Closure closure);

// update sink_id's Projector window position and size
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetProjectorPosition(const char* canvas_project_id,
                                                 uint32_t video_model_id,
                                                 const MSRect& rect,
                                                 Closure closure);

// Set ui config by PreviewUIConfig with params
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetUIConfig(uint32_t video_model_id,
                                        const PreviewUIConfig& params,
                                        Closure closure);

// Enable video_model_id can track(move clip scale)
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EnableTrack(uint32_t video_model_id,
                                        bool enable,
                                        Closure closure);

// Enable sink_id can fine tuning
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EnableFineTuning(uint32_t sink_id,
                                             bool enbale,
                                             Closure closure);

// Add one model identifier with video_model_id and ModelParams
/* e.g
  params.output_size = kOutputSize;
  params.fps = kModelFps;
  params.color_space = mediasdk::kColorSpaceBT601;
  params.color_transfer = mediasdk::kColorTransferUnspecified;
  params.video_range = mediasdk::kVideoRangeFull;
  params.output_format = mediasdk::kPixelFormatNV12;
  params.output_stage = mediasdk::kOutputStageDouble;
  params.hwnd_parent = hwnd;
  params.window_rect.x = rect.x(); preview window pos
  params.window_rect.y = rect.y();
  params.window_rect.cx = rect.width();
  params.window_rect.cy = rect.height();
*/
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CreateModel(uint32_t video_model_id,
                                        const ModelParams& params,
                                        Closure closure);

// Remove one model identifier with video_model_id
// Asynchronous callback result, ModelParams
MEDIASDK_EXPORT void MS_API GetModelParams(uint32_t video_model_id,
                                           Closure closure);

// Get one model identifier with video_model_id
// Asynchronous callback result, MSRect
MEDIASDK_EXPORT void MS_API GetModelWindowPos(uint32_t video_model_id,
                                              Closure closure);

// Scale all canvas item in the specific video model
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetScaleForVideoModel(uint32_t video_model_id,
                                                  float scale,
                                                  Closure closure);

// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API UpdateModelFPS(uint32_t video_model_id,
                                           float fps,
                                           Closure closure);

// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API UpdateModelOutputSize(uint32_t video_model_id,
                                                  MSSize size,
                                                  Closure closure);

// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API
UpdateModelColorSpaceAndVideoRange(uint32_t video_model_id,
                                   ColorSpace cs,
                                   VideoRange vr,
                                   Closure closure);

// Remove one model identifier with video_model_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API RemoveModel(uint32_t video_model_id,
                                        Closure closure);

// Get Monitor Information
// Synchronous callback result
MEDIASDK_EXPORT void MS_API
GetMonitorInformation(MediaSDKArray<MonitorInfo>& monitor_infos);

// Asynchronous callback result, bool.
// return true if the monitor is mediasdk running on
MEDIASDK_EXPORT void MS_API IsCurrentMonitor(const MonitorInfo& info,
                                             Closure closure);

// Get driver info
// Asynchronous callback result, MediaSDKArray<DeviceInfo>
MEDIASDK_EXPORT void MS_API GetDeviceInfo(Closure closure);

// Get cpu info
// Asynchronous callback result, CpuInfo
MEDIASDK_EXPORT void MS_API GetCpuInfo(Closure closure);

// Get memory info
// Asynchronous callback result, MemoryInfo
MEDIASDK_EXPORT void MS_API GetMemoryInfo(Closure closure);

// Get Push Stream info
// Asynchronous callback result, StatisticInfo
MEDIASDK_EXPORT void MS_API GetStatisticInfo(const char* stream_id,
                                             Closure closure);

// Get Encoder Statistic Info
// This interface should not be called after calling unintialize, as there is a
// risk of crash. result is EncoderStatisticInfo
MEDIASDK_EXPORT void MS_API GetEncoderStatisticInfo(const char* stream_id,
                                                    EncoderStatisticInfo& info);

// Get Encode pts and dts by stream_id
// Asynchronous callback result, EncoderTsInfo
MEDIASDK_EXPORT void MS_API GetEncoderPtsByStreamId(const char* stream_id,
                                                    Closure closure);

// Set real time priority, if realtime_priority is request real time,
// nvidia device used nvidia_priority
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetRealTimePriority(bool realtime_priority,
                                                int32_t nvidia_priority,
                                                Closure closure);

// Set GPU thread priority with priority
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetGPUThreadPriority(int32_t priority,
                                                 Closure closure);

// Begin GPU detect summary
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API BeginGPUDetect(Closure closure);

// End GPU detect summary
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EndGPUDetect(Closure closure);

// Enum SDK current service plug ins can support for streaming
// Asynchronous callback result need cast to mediasdk::PluginInfoArray
MEDIASDK_EXPORT void MS_API EnumServiceSource(Closure closure);

// Notify that streaming or recording will start
MEDIASDK_EXPORT void MS_API StartPushStream(const StreamContext& context);

// Start a streaming service, the results and status returns are received by
// MediaSDKStreamStatusObserver. The success or failure of StartStream is
// defined by OnStartStreamResult.
// MediaSDKStreamStatusObserver must remain alive until the stream stop
MEDIASDK_EXPORT void MS_API StartStream(const StreamParams& stream_params,
                                        MediaSDKStreamStatusObserver* observer);

// Get the current stream by using the stream id.
// Asynchronous callback result, MSCallbackBool
MEDIASDK_EXPORT void MS_API IsStreamInProcess(const char* stream_id,
                                              Closure closure);

// Notify that streaming or recording will start
MEDIASDK_EXPORT void MS_API StopPushStream(const StreamContext& context);

// Turn off the streaming service, the results and status returns by
// MediaSDKStreamStatusObserver::OnStreamStopped
// Calling StopStream in the process of StartStream will stop the process of
// StartStream
MEDIASDK_EXPORT void MS_API StopStream(const char* stream_id);

// Update the ABR code rate parameters of the stream.
// When the stream does not have an ABR policy set, it does nothing.
MEDIASDK_EXPORT void MS_API UpdateStreamAbrOffset(const char* stream_id,
                                                  int offset);

// Turn off the streaming service, the results and status returns by
// MediaSDKStreamStatusObserver::OnStreamStopped
// Calling StopStream in the process of StartStream will stop the process of
// StartStream
// In the StreamParams of the speed test,
// audio_encoder_config and video_encoder_config will not be referenced,
// and speed_test_target_bitrate must be set.
MEDIASDK_EXPORT void MS_API
StartSpeedTestStream(const StreamParams& stream_params,
                     MediaSDKStreamStatusObserver* observer);

// Turn off the streaming service, the results and status returns by
// MediaSDKStreamStatusObserver::OnStreamStopped
// Calling StopStream in the process of StartStream will stop the process of
// StartStream
MEDIASDK_EXPORT void MS_API StopSpeedTestStream(const char* stream_id);

// Output sink_id's thumbnail save as file_path with pixel format Asynchronous
// callback result, bool
MEDIASDK_EXPORT void MS_API OutputThumbnailSaveAs(uint32_t sink_id,
                                                  const char* file_path,
                                                  ImageFileFormat format,
                                                  Closure closure);

// Get current adapter info
// Asynchronous callback result, MediaSDKString
// the json result as:
/*
  {
    "name":"name",
    "vendor_id":uint32_t,
    "device_id":uint32_t
  }
*/
MEDIASDK_EXPORT void MS_API GetCurrentAdapterInfo(Closure closure);

// Set stream sei info with json_info
MEDIASDK_EXPORT void MS_API SetStreamSEI(const char* json_info);

// clear stream sei info
MEDIASDK_EXPORT void MS_API ClearStreamSEI(const char* json_info);

// Get encoder session with encode_name, max test index is session_count
// Asynchronous callback result, ResultBoolInt
// The result is different test h264 and hevc, but texture and buffer is same.
MEDIASDK_EXPORT void MS_API
TestEncoderSessionCountSupported(const char* encoder_name,
                                 uint32_t session_count,
                                 Closure closure);

// Get current fps, present no ready fps
// This interface should not be called after calling unintialize, as there is a
// risk of crash. result, RenderInfo
MEDIASDK_EXPORT void MS_API GetRenderInfo(RenderInfo& info);

// get video file flip,size info like ff probe
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API GetVideoFileFrameInfo(const char*, Closure closure);

// Enum SDK current encoder's plugins can support encoder
// Asynchronous callback result need cast to mediasdk::PluginInfoArray
/*
PluginInfo::desc
{
  "hardware_accelerate": int, // 0 = false, !0 = true
  "encoder_type": int // mediasdk::EncoderType
}
*/
MEDIASDK_EXPORT void MS_API EnumEncoderSource(Closure closure);

// timer call this function to collect perf data
MEDIASDK_EXPORT void MS_API StartRenderProfiler();

//  timer call this function to collect more detail perf data
MEDIASDK_EXPORT void MS_API
StartCollectPerformanceMatrics(const char* json_info);

// Get the current bit rate of the video encoder, input codec_id(
// stream params "video_encoder_config" -> "id").
// Asynchronous callback result, EncoderBitrate
MEDIASDK_EXPORT void MS_API VideoEncoderTargetBitrate(const char* codec_id,
                                                      Closure closure);

// Set tt npt for MediaSDK ntp_ms is the ntp timestamp
MEDIASDK_EXPORT void MS_API SetTTNtpMS(int64_t tt_ntp_ms, int64_t tt_local_ms);

// mock render hung by ms
MEDIASDK_EXPORT void MS_API MockRenderHung(int32_t hung_ms);

// Reconfigure video output parameters (output canvas size, encoder frame
// rate, encoding output size, and bitrate).
MEDIASDK_EXPORT void MS_API ReconfigVideoOutput(const VideoOutputParams& params,
                                                const char* reason,
                                                Closure closure);

// Downgrade the encoder. The parameters passed in are the parameters of the
// encoder that is downgraded to.
// Return the result through MediaSDKStreamStatusObserver::OnEncodeEvent
// When there is recording, there will be no downgrade;
// Conversion between h264 and h265 is prohibited
MEDIASDK_EXPORT void MS_API
FallbackVideoEncoder(const MediaSDKString& codec_id,
                     const FallbackVideoEncoderConfigParams& params,
                     Closure closure);

// Enable the adaptive gear strategy. Currently, MediaSDK is not aware of the
// adaptive gear. In order to monitor the online adaptive performance and
// provide direction for subsequent optimization, this interface is added.
// When calling, during the streaming process, the business considers that this
// stream belongs to the adaptive experiment group, that is, calling
// adaptive_config: "{"strategy": "v1", "up_limit": 10000, "down_limit": 1000}"
MEDIASDK_EXPORT void MS_API
StartAdaptiveGearStrategyReport(const MediaSDKString& stream_id,
                                const MediaSDKString& adaptive_config);

// Get the current time in nanoseconds
MEDIASDK_EXPORT int64_t MS_API NanoNow();

// param:
// {
//     "canvas_item_z_order": [{
//         "action_id" : "xxxxxx",
//         "canvas_id:": "canvas_id_1",
//         "canvas_items_z_order":["item_id_1", "item_id_2"]
//      }],
//      "canvas_item_transform": [{
//         "action_id": "xxxxx",
//         "canvas_item_id": "item_id_1",
//         "duration_ms": 800,
//         "progress_function": 1, //see TransitionProgressFunctionType
//         "target_transform": {
//             "translate": {"x": 0.0, "y": 0.0},
//             "scale": {"x": 0.0, "y": 0.0},
//             "crop": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 0.0},
//             "flip": {"h": false, "v": true},
//             "rotate": 90.0
//         }
//      }],
//      "canvas_item_visible":[{
//          "action_id": "xxx",
//          "canvas_item_id": "xxx",
//          "visible": true,
//          "duration_ms": 800,
//          "progress_function": 1
//      }],
//      "canvas_item_preprocess": {
//          "action_id": "xxxxx",
//          "canvas_item_id_1": false,
//          "canvas_item_id_2": true
//      },
//      "canvas_item_filter":[{
//          "action_id": "xxxxx",
//          "action_type": "create",
//          "canvas_item_id": "item_id_1",
//          "filter_id": "filter_id_1",
//          "filter_name": "EffectVisualFilter",
//          "filter_params":"xxx", //string，content is json
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "destroy",
//          "canvas_item_id": "item_id_1",
//          "filter_id": "filter_id_1"
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "set_property",
//          "canvas_item_id": "item_id_1",
//          "filter_id": "filter_id_1",
//          "property_key": "key",
//          "property_value": "xxx" //STRING，CONTENT IS JSON
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "set_active",
//          "canvas_item_id": "item_id_1",
//          "filter_id": "filter_id_1",
//          "active": false
//      }],
//      "canvas_filter":[{
//          "action_id": "xxxxx",
//          "action_type": "create",
//          "filter_id": "canvas_filter_id_1",
//          "canvas_id": "canvas_id_1",
//          "filter_name": "ColorAdjustmentCanvasFilter",
//          "filter_params":"xxx", //STRING，CONTENT IS JSON
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "destroy",
//          "filer_id": "canvas_filter_id_1",
//          "canvas_id": "canvas_id_1"
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "set_property",
//          "canvas_id": "canvas_id_1",
//          "filter_id": "filter_id_1",
//          "property_key": "key",
//          "property_value": "xxx" //STRING，CONTENT IS JSON
//      }, {
//          "action_id": "xxxxx",
//          "action_type": "set_active",
//          "canvas_id": "canvas_id_1",
//          "filter_id": "filter_id_1",
//          "active": false
//      }],
//      "audio_ambient": {
//          "action_id": "xxxxx",
//          "action_type": "play",
//          "audio_input_id": "audio_input_id_1",
//          "sample_rate": 44100, //int
//          "channel_num": 2,     //int
//          "layout": 2,          //see CHANNEL_LAYOUT
//          "format": 8,          //see AUDIO_FORMAT
//          "left_channel_datas": "xxx", //string
//          "right_channel_datas": "xxx" //string
//      },
//      //"audio_ambient": {
//      //    "action_id": "xxxxx",
//      //    "action_type": "stop",
//      //    "audio_input_id": "audio_input_id_1"
//      //}
// }

// closure: MediaSDKString, json
// {
//   "result": true,
//   "action_id_xxx": true,
//   "action_id_xxx1": false,
//   "action_id_xxxn": true,
// }
MEDIASDK_EXPORT void MS_API AmbientEffect(const MediaSDKString& param,
                                          Closure closure);

}  // namespace mediasdk

#ifdef __cplusplus
}
#endif  // __cplusplus
