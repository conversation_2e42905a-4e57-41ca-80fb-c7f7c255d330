﻿#pragma once

#include "IController.h"
#include "MediaMgr.h"
#include "Mode.h"
#include <map>

class Controller : public IController, public ITimerHandler
{
public:
    Controller();
    ~Controller();
    void   OnParallelInitialize(void* param);
    void   Close();
    //////////App operation
    bool   Initialize(INITIALIZE_INFO* info) override;
    void   Quit() override;
    void   EnumWindows(BOOL icon, std::vector<WINDOW_DESC>* oWindowDescs) override;
    void   EnumMonitors(std::vector<MONITOR_DESC>* oMonitorDescs) override;
    void   EnumVideoDevices(std::vector<DSHOW>* oDevices) override;
    void   EnumAudioDevices(std::vector<DSHOW>* oDevices) override;
    void   EnumCaptureFormats(const DSHOW& videoDevice, std::vector<VIDEO_CAPTURE_FORMAT>* videoCapFormats, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats) override;
    void   EnumAudioCapFormatWithDShowName(const DSHOW& device, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats) override;
    void   IsForegroundFullScreen(bool* full) override;
    void   WindowOverlapped(HWND hwnd, bool* overlapped) override;
    void   GetWindowOverlappedMonitors(HWND hwnd, std::vector<std::string>* monitorDids, std::string* mainDid, std::vector<std::string>* allDidList) override;
    void   BonjourCheck(bool* oChecked) override;
    void   SetDisplayMasks(const std::string& portraitFile, const std::string& landscapeFile) override;
    void   PlayAnimation(const std::string& portraitVideo, const std::string& landscapeVideo) override;
    void   EnableAllPreview(bool enable) override;
    void   EnablePreviewByVideoModel(UINT32 videoModel, bool enable) override;
    void   EnableInteract(bool enable) override;
    void   CreateFullScreenDetector(const std::string& detectorID) override;
    void   DestroyFullScreenDetector(const std::string& detectorID) override;
    void   SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exeNames) override;
    void   GetFontFamilies(std::vector<std::string>* fonts) override;
    void   UpdateDynamicConfig(const std::string& json_config, bool enableTransition) override;
    void   CheckEncoderSession(const std::string& name, UINT32 count, INT32* result) override;
    bool   ResetParfait(PARFAIT_PARAM param) override;
    void   SetParfaitContextInfo(const std::string& key, const std::string& val) override;
    void   StartColorPicker(HWND hwnd) override;
    UINT64 AllocPreviewID() override;
    // effect platform
	void   InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect) override;
	void   UnInitEffectPlatform() override;
	void   LoadEffectModels(const std::string& requestID, const std::string& modelName, const std::vector<std::string>& requirements) override;
	void   UpdateEffectConfig(const std::string& userID, const std::string& ttlsHardwareLevel) override;
    //////////Watchdog
    bool   StartWatchdog(const char* pipeName, int timeout) override;
    void   StopWatchdog() override;
    void   EnableFrozenMonitor(int timeout) override;
    void   ServerCrash() override;
    void   MockFrozen(int ipcTimeout, int renderTimeout) override;
    //////////View operation
    // mode
    bool   SelectMode(LIVE_MODE mode) override;
    UINT64 AddSceneWithInfo(LIVE_MODE mode, SCENE_INFO* sceneInfo) override;
    void   AddSceneByID(LIVE_MODE mode, UINT64 sceneID) override;
    void   RemoveScene(LIVE_MODE mode, UINT64 sceneID) override;
    // scene
    UINT64 CreateScene(SCENE_INFO* sceneInfo) override;
    void   DeleteScene(UINT64 sceneID) override;
    bool   FindSceneByID(UINT64 sceneID) override;
    void   SelectScene(UINT64 sceneID, bool reloadWhenSwitchMode) override;
    void   GetSceneInfo(UINT64 sceneID, SCENE_INFO* info) override;
    void   PreLoadScene(UINT64 sceneID, bool preloadWhenSwitchMode) override;
    // Canvas
    UINT64 CreateCanvas(CANVAS_INFO* info) override;
    UINT64 GetCanvasID(UINT64 sceneID, UINT32 videoModel) override;
    bool   DestroyCanvas(UINT64 canvasID) override;
    void   PreviewCanvas(UINT64 canvasID, CANVAS_INFO* info) override;
    void   DestroyPreview(UINT64 canvasID) override;
    UINT64 GetCanvasIDByPreviewID(UINT64 previewID) override;
    void   SetPreviewIDMapCanvasID(UINT64 previewID, UINT64 canvasID) override;
    void   UpdateCanvasLayout(UINT64 canvasID, CANVAS_INFO* info) override;
    void   GetCanvasInfo(UINT64 canvasID, CANVAS_INFO* info) override;
    void   SetCanvasInfo(UINT64 canvasID, const CANVAS_INFO* info) override;
    void   EnumLayers(UINT64 canvasID, CANVAS_INFO_EX* infoEx) override;
    bool   FindCanvasByID(UINT64 canvasID) override;
    bool   CheckCanvasByID(UINT64 canvasID) override;
    void   UpdateLayersOrder(UINT64 canvasID, const std::vector<UINT64>& layerIDs, std::vector<UINT64>* moreLayerIDs, std::vector<UINT64>* lessLayerIDs) override;
    void   MoveLayerOrder(UINT64 canvasID, UINT64 layerID, MOVE_ORDER move) override;
    UINT64 AddLayerWithInfo(UINT64 canvasID, LAYER_INFO* layerInfo) override;
    bool   AddLayerByID(UINT64 canvasID, UINT64 layerID) override;
    void   RemoveLayer(UINT64 canvasID, UINT64 layerID) override;
    void   OpenMobilePreview(UINT32 videoModel, UINT64 hwnd, Gdiplus::RectF rect) override;
    void   CloseMobilePreview(UINT32 videoModel) override;
    bool   OpenCanvasPreview(const std::string& previewId, UINT32 videoModel, HWND hwnd, Gdiplus::RectF rect, const PREVIEW_PARAMS& params) override;
    bool   CloseCanvasPreview(const std::string& previewId, UINT32 videoModel) override;
    bool   SetCanvasPreviewParams(const std::string& previewID, UINT32 videoModel, const PREVIEW_PARAMS* params) override;
    bool   SetCanvasPreviewPosition(const std::string& previewID, UINT32 videoModel, const Gdiplus::RectF& rect) override;
    void   SetDisplay(bool showView, UINT32 videoModel = UINT32_MAX) override;
    bool   GetDisplay(UINT32 videoModel) override;
    void   VibeTriggerEffect(const VIBE_TRIGGER_EFFECT& vibeTriggerEffect) override;
    // layer
    bool   CreateLayer(LAYER_INFO* layerInfo) override;
    void   DeleteLayer(UINT64 layerID) override;
    void   ControlLayer(UINT64 layerID, LAYER_INFO& layerInfo, LAYER_CONTROL_CMD cmd) override;
    void   GetLayerInfo(UINT64 layerID, LAYER_INFO* layerInfo) override;
    void   SetLayerInfo(UINT64 layerID, const LAYER_INFO& layerInfo) override;
    bool   FindLayerByID(UINT64 layerID) override;
    void   ReopenLayer(UINT64 canvasID, LAYER_INFO* layerInfo) override;
    void   UnSelectLayer() override;
    void   AddBrowserLayer(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo) override;
    void   RemoveGraffiti(UINT64 layerID) override;
    void   RemoveAllGraffiti(UINT64 layerID, bool enable) override;
    void   GetMediaFileInfo(const std::string& path, MATERIAL_DESC* desc) override;
    void   GetLayerSnapshot(UINT64 layerID, const std::string& path) override;
    bool   GetLayerSnapshot2(UINT64 layerID, const std::string& path) override;
    void   GetEffectProfiler(UINT64 layerID, UINT64 sourceID, EffectProfilerInfo* effectProfilerInfo) override;
    void   UpdateLayerSource(UINT64 layerID, UINT64 sourceID) override;
    bool   CreateFrame(UINT64 mediaID, Gdiplus::SizeF resize, CLIPF clip, OBJECT_FIT_MODE fitMode, UINT64* frameID) override;
    void   HandleLayerSizeChange(UINT64 layerID) override;
    void   CalLayerTransform(const LAYER_INFO& layerInfo, UINT64 cmd) override;
    // layer preview
	bool   StartLayerPreview(UINT64 layerID, UINT64 parent, const LAYER_PREVIEW* preview, UINT64* previewID, const PREVIEW_PARAMS& params) override;
	bool   StopLayerPreview(UINT64 layerID, UINT64 previewID) override;
	bool   SetLayerPreviewLayout(UINT64 layerID, UINT64 previewID, const LAYER_PREVIEW* preview) override;
	void   GetLayerPreview(UINT64 layerID, UINT64 previewID, LAYER_PREVIEW* preview) override;
	bool   SetLayerPreviewParams(const std::string& layerID, const std::string& previewID, const PREVIEW_PARAMS* params) override;
	// source
    bool   CreateSource(SOURCE_INFO& sourceInfo) override;
    bool   PauseSource(const std::string& sourceID) override;
    bool   ResumeSource(const string& sourceID) override;
    bool   ReopenSource(UINT64 sourceID, SOURCE_INFO& sourceInfo) override;
    void   DestroySource(UINT64 sourceID) override;
    void   ControlSource(UINT64 sourceID, SOURCE_INFO& sourceInfo, SOURCE_CONTROL_CMD cmd) override;
    void   SetSourceInfo(UINT64 sourceID, const SOURCE_INFO* sourceInfo) override;
    bool   FindSourceByID(UINT64 sourceID) override;
    void   GetSourceInfo(UINT64 sourceID, SOURCE_INFO* info, SOURCE_INFO_CMD cmd = SOURCE_INFO_CMD::SOURCE_INFO_NONE) override;
    void   SetCompositeMetas(const std::unordered_map<UINT64, COMPOSITE_SOURCE_META>& metas) override;
    std::unordered_map<UINT64, COMPOSITE_SOURCE_META> GetCompositeMetas() override;
    bool   IsMatchedSource(SOURCE_INFO& sourceInfo) override;
    // audio
    void   EnumAppAudio(std::vector<DSHOW>* devices) override;
    void   EnumCaptureAudio(std::vector<DSHOW>* devices) override;
    void   EnumRenderAudio(std::vector<DSHOW>* devices) override;
    void   GetDefaultInput(DSHOW* device) override;
    void   GetDefaultOutput(DSHOW* device) override;
    void   SystemSupportAppAudio(bool* support) override;
    UINT64 AddAudio(AUDIO_INFO* audioInfo) override;
    void   DeleteAudio(UINT64 audioID) override;
    bool   ControlAudio(UINT64 audioID, AUDIO_CONTROL_INFO* info) override;
    void   SetRenderDeviceID(const std::string& deviceID) override;
    bool   SetANSOption(const AUDIO_ANS_OPTION& ansOption) override;
    void   GetAudioInfo(UINT64 audioID, AUDIO_INFO* info, AUDIO_INFO_CMD cmd = AUDIO_INFO_CMD::AUDIO_INFO_NONE) override;
    bool   FindAudioByID(UINT64 audioID) override;
    bool   StartListenAudio(const std::string& audioID) override;
    bool   StopListenAudio(const std::string& audioID) override;
    UINT64 CreateEraseAudio(const ERASE_AUDIO_INFO* eraseAudioInfo) override;
    bool   DeleteEraseAudio(UINT64 eraseAudioID) override;
    bool   FindEraseAudioByID(UINT64 eraseAudioID) override;
    void   GetEraseAudioInfo(UINT64 eraseAudioID, ERASE_AUDIO_INFO* info) override;
    bool   EraseAudioEnableAutoMute(UINT64 eraseAudioID, bool enable) override;
    bool   EraseAudioSetOriginAudio(UINT64 eraseAudioID, UINT64 originAudioID) override;
    bool   EraseAudioSetEraseRange(UINT64 eraseAudioID, INT64 beginPTSMs, INT64 endPTSMs, bool* hit, INT64* bufferBeginPTSMs, INT64* bufferEndPTSMs) override;
    bool   ChangeAudioTrack(UINT64 audioID, UINT32 oriTrack, UINT32 tarTrack) override;
    bool   EnableAudioInputEchoDetection(UINT64 audioID, const int interval);
    bool   SetAudioInputRenderDeviceID(UINT64 audioID, const std::string& deviceID);

    // layer && audio filter
    void   AddFilter(UINT64 mediaID, UINT64 filterID) override;
    void   RemoveFilter(UINT64 mediaID, UINT64 filterID) override;
    void   ResetFilterOrder(UINT64 layerID, const std::vector<std::string>& filterIDs) override;
    // filter
    UINT64 CreateFilter(FILTER* filterInfo) override;
    void   DeleteFilter(UINT64 filterID) override;
    void   ControlFilter(UINT64 filterID, const FILTER& info, FILTER_CONTROL_CMD cmd) override;
    void   GetFilterInfo(UINT64 filterID, FILTER* info, FILTER_INFO_CMD cmd = FILTER_INFO_CMD::FILTER_INFO_NONE) override;
    void   SetFilterInfo(UINT64 filterID, const FILTER* info) override;
    bool   FindFilterByID(UINT64 filterID) override;
    //////////Mix operation
    void   SetMixParameter(UINT32 videoModel, VIDEO_MIX_PARAM param) override;
    void   OutputThumbnail(LIVE_MODE mode, UINT64 sceneID, UINT64 canvasID, const std::string& path, IMAGE_FORMAT format, void* cbparam) override;
    //////////Codec operation
    void   EnumVideoEnc(std::vector<VIDEO_ENCODER_INFO>* oVideoEncoders) override;
    void   GetVideoCodecParam(const std::string& codecID, INT32* oErrCode, VIDEO_CODEC_PARAM* oParam) override;
    //////////Stream operation
	void   StartLive(std::vector<OUTPUT_INFO> infos) override;
	void   StopLive(const std::vector<STOP_STREAM_PARAM>& streamParams) override;
	void   SetStreamRoomID(const std::string& roomID) override;
	void   SetMultipleIFrameSEI(std::vector<SEI_INFO> infos) override;
	void   StartBwProbeStream(OUTPUT_INFO info, bool* running) override;
	void   StopBwProbeStream(const std::string& streamID, bool* fallback) override;
	void   ABRConfig(const std::string& streamID, UINT32 offset) override;
	void   GetActiveStatistic(const std::string& streamID, ACTIVE_STATISTIC* statistic) override;
	void   IsStreamInProcess(const std::string& streamID, bool* is_streaming) override;
    //////////RTC operation
	bool   StartRTC(RTC_LINK link) override;
	bool   StopRTC() override;
	bool   StartScreenShare(UINT32 videoModel, const CLIP_AREA_INFO& info, bool enableAudio, UINT32 audioTrack) override;
	bool   StopScreenShare(UINT32 videoModel) override;
	bool   UpdateScreenShareClipInfo(const CLIP_AREA_INFO& info) override;
	bool   StartLiveTranscoding(const std::string& args) override;
	bool   UpdateLiveTranscoding(const std::string& args) override;
	bool   StopLiveTranscoding(const std::string& args) override;
	bool   StartForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result) override;
	bool   UpdateForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result) override;
	bool   StopForwardStreamToRooms() override;
	bool   PublishVideoStream(MEDIA_STREAM_TYPE type) override;
	bool   UnPublishVideoStream(MEDIA_STREAM_TYPE type) override;
	bool   SendRTCUserMessage(const std::string& uid, const std::string& msg) override;
	bool   SendRTCRoomMessage(const std::string& msg) override;
	bool   SubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type) override;
	bool   UnSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type) override;
	bool   UpdateClipScaleInfo(const CLIP_AREA_INFO& info) override;
	bool   SetAudioOutput(const std::string& deviceID) override;
	bool   SetAudioPropertiesReport(UINT32 interval) override;
	bool   MuteRemoteAudio(const std::string& userID, UINT32 streamIdx, bool enable) override;
	bool   EnableLocalAudio(bool enable) override;
	bool   EnableLocalVideo(bool enable) override;
    //////////Camera operation
    void   CameraSourceIsUsingRealCamera(UINT64 layerID, bool* value) override;
    //////////Performance
	void   GetCPUUsage(float* processUsage, float* sysUsage1, float* sysUsage2) override;
    void   GetMemUsage(float* processUsage, float* sysUsage, UINT64* totalSize, float* pageFault, float* commitMemoryUsage) override;
	void   GetActiveFPS(float* fps) override;
	void   GetNotReadyFPS(float* fps) override;
    std::vector<VIDEO_ADAPTER_INFO> GetGPUInfo() override;
    CPU_INFO GetCpuInfo() override;
    std::vector<MONITOR_INFO> GetMonitorsInfo() override;
	HMONITOR GetCurrentMonitor() override;
	UINT   GetHAGS() override;
	void   StartRenderProfiler() override;
	void   StartCollectPerformanceMatrics(const std::vector<PERFORMANCE_MATRICS>& params) override;
	void   SetTTNtpMS(UINT64 ntpMs, UINT64 localMs) override;
	void   GetAudioPerformance(const std::string& audioID, AUDIO_PERFORMANCE_INFO* info) override;
    //////////Timer operation
    void   HandleTimer(UINT64 id) override;
    //////////Keyboard Event
    void   OnKey(WPARAM wParam, LPARAM lParam) override;
    //////////MediaMgr Impl
    void*  GetMediaMgrImpl() override;

    /// ///// AiSdk IPC
    void   StartAiIpc() override;
    void   StopAiIpc() override;

private:
    void                SetupTimer(void* param);
    void                CloseTimer();
    void                OutputThumbnailInternal(void* param);
    INT                 GetCpuDeviceInfoExt(CPU_INFO& cpuinfo);
    std::vector<FILTER> ResetLayerFilters(std::vector<FILTER> filters, std::vector<std::string> filterIDs);

protected:
    UINT64 m_timer = 0;
    HWND   m_bottomWnd = nullptr;

    UINT64               m_timer_thumbnail = 0;
    THUMBNAIL_INFO_CACHE m_thumbnailInfo{};

    std::unordered_map<int, std::pair<UINT64, UINT64>> m_canvasResources{}; // (videoModel : <layerID, sourceID>)
    std::map<std::string, OUTPUT_INFO>                 m_streamIDMap{};

    // TODO: @xuwanhui videoModel map to canvasIDs
    std::map<UINT32, std::vector<UINT64>> m_videoModelCanvasIDs{};
    std::map<UINT64, UINT64> m_previewIDMapCanvasID{};
    std::map<UINT32, bool> m_modeExisted{};
};