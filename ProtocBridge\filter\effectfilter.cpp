#include "stdafx.h"
#include "effectfilter.h"

namespace LS
{
EffectFilter::RequestList EffectFilter::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<EffectFilter::Create>());
    list.push_back(std::make_unique<EffectFilter::Remove>());

    list.push_back(std::make_unique<EffectFilter::AddComposers>());
    list.push_back(std::make_unique<EffectFilter::ResetComposers>());
    list.push_back(std::make_unique<EffectFilter::RemoveComposers>());
    list.push_back(std::make_unique<EffectFilter::UpdateComposer>());
    list.push_back(std::make_unique<EffectFilter::ReplaceComposers>());

    list.push_back(std::make_unique<EffectFilter::SetText>());
    list.push_back(std::make_unique<EffectFilter::SetBackgroundImage>());
    list.push_back(std::make_unique<EffectFilter::SetPicQualityBrightness>());
    list.push_back(std::make_unique<EffectFilter::SetEffectFilterMsg>());

    list.push_back(std::make_unique<EffectFilter::GetExclusion>());
    list.push_back(std::make_unique<EffectFilter::GetEffectFilter>());
    return list;
}

bool EffectFilter::Create::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    FILTER filterInfo{};
    filterInfo.type = FILTER_EFFECT;

    EFFECT_FILTER effectFilterInfo{};
    ls_effectfilter::EffectFilter effect_filter = req.effect_filter();
    for (int i = 0; i < effect_filter.composers_size(); ++i)
    {
        ls_effectfilter::Composer composer = effect_filter.composers(i);
        EFFECT_COMPOSER           comp{};
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag())
        {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.composers.push_back(comp);
    }

    if (effect_filter.has_key_path())
    {
		effectFilterInfo.keyPath.key = effect_filter.key_path().key();
		effectFilterInfo.keyPath.val = effect_filter.key_path().val();
    }
    
    if (effect_filter.has_bright_config())
    {
        ls_effectfilter::PicQualityBrightness config = effect_filter.bright_config();
        if (config.has_enable())
        {
            effectFilterInfo.brightConfig.enable = config.enable();
        }
        if (config.has_is_auto())
        {
            effectFilterInfo.brightConfig.isAuto = config.is_auto();
        }
        if (config.has_asset_path())
        {
            effectFilterInfo.brightConfig.assetPath = config.asset_path();
        }
        if (config.has_key_val())
        {
            effectFilterInfo.brightConfig.keyVal.key = config.key_val().key();
            effectFilterInfo.brightConfig.keyVal.val = config.key_val().val();
        }
    }

    filterInfo.filter = effectFilterInfo;
    UINT64 filterID = controller->CreateFilter(&filterInfo);
    std::string filter_id = "";
    Util::NumToString(filterID, &filter_id);
    rsp.set_filter_id(filter_id);

    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::Create] CreateFilter failed, filterID: " << filterID;
        return false;
    }
    return true;
}

bool EffectFilter::Remove::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::Remove] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->DeleteFilter(filterID);
    return true;
}

bool EffectFilter::AddComposers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::AddComposers] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    effectFilterInfo.addComposers.clear();
    for (int i = 0; i < req.composers_size(); ++i)
    {
        ls_effectfilter::Composer composer = req.composers(i);
        EFFECT_COMPOSER comp;
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag())
        {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.addComposers.push_back(comp);

        auto it = std::find_if(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end(),
                              [&comp](const EFFECT_COMPOSER& effectComposer) {
                                  return effectComposer.effectPath == comp.effectPath && effectComposer.keyVal.key == comp.keyVal.key;
                              });
        if (it != effectFilterInfo.composers.end())
        {
            it->keyVal.val = comp.keyVal.val;
        }
        else
        {
            effectFilterInfo.composers.push_back(comp);
        }
    }

    filterInfo.filter = effectFilterInfo;
    controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_ADD_COMPOSER);
    return true;
}

bool EffectFilter::RemoveComposers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::RemoveComposers] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    effectFilterInfo.removeComposers.clear();
    for (int i = 0; i < req.composers_size(); ++i)
    {
        ls_effectfilter::Composer composer = req.composers(i);
        EFFECT_COMPOSER           comp;
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag())
        {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.removeComposers.push_back(comp);

        effectFilterInfo.composers.erase(
            std::remove_if(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end(), [comp](const EFFECT_COMPOSER& effectComposer) {
                return effectComposer.effectPath == comp.effectPath && effectComposer.keyVal.key == comp.keyVal.key && effectComposer.keyVal.val == comp.keyVal.val;
            }),
            effectFilterInfo.composers.end());
    }

    filterInfo.filter = effectFilterInfo;
    controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_REMOVE_COMPOSER);
    return true;
}

bool EffectFilter::ResetComposers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::ResetComposers] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    bool composersIsEqual = true;
    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    std::vector<EFFECT_COMPOSER> composers(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end());
    effectFilterInfo.composers.clear();
    for (int i = 0; i < req.composers_size(); ++i)
    {
        ls_effectfilter::Composer composer = req.composers(i);
        EFFECT_COMPOSER           comp;
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag())
        {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.composers.push_back(comp);

        auto it = std::find_if(composers.begin(), composers.end(),
                               [&comp](const EFFECT_COMPOSER& existingComp) {
                                   return existingComp.effectPath == comp.effectPath &&
                                          existingComp.keyVal.key == comp.keyVal.key &&
                                          existingComp.keyVal.val == comp.keyVal.val;
                               });
        if (it != composers.end())
            composers.erase(it);
        else
            composersIsEqual = false;
    }
    
    if (!composers.empty())
        composersIsEqual = false;

    if (!composersIsEqual)
    {
        filterInfo.filter = effectFilterInfo;
        controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_SET_COMPOSER);
    }
    
    return true;
}

bool EffectFilter::UpdateComposer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::UpdateComposer] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    ls_effectfilter::Composer composer = req.composer();

    EFFECT_COMPOSER comp = effectFilterInfo.updateComposer;
    comp.effectPath = composer.effect_path();
    comp.keyVal.key = composer.key_val().key();
    comp.keyVal.val = composer.key_val().val();
    if (composer.has_composer_tag())
    {
        comp.composerTag = composer.composer_tag();
    }
    
    auto it = std::find_if(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end(), [comp](EFFECT_COMPOSER effectComposer) {
        return comp.effectPath == effectComposer.effectPath && comp.keyVal.key == effectComposer.keyVal.key;
    });

    if (it != effectFilterInfo.composers.end())
    {
        it->keyVal.key = comp.keyVal.key;
        it->keyVal.val = comp.keyVal.val;
        it->composerTag = comp.composerTag;
    }

    filterInfo.filter = effectFilterInfo;
    controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_UPDATE_COMPOSER);
    return true;
}

bool EffectFilter::ReplaceComposers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::ReplaceComposers] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);
    
    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    effectFilterInfo.oldComposers.clear();
    for (int i = 0; i < req.oldcomposers_size(); ++i)
    {
        ls_effectfilter::Composer composer = req.oldcomposers(i);
        EFFECT_COMPOSER comp;
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag()) {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.oldComposers.push_back(comp);

        auto it = std::find_if(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end(),
            [&comp](const EFFECT_COMPOSER& effectComposer) {
                return effectComposer.effectPath == comp.effectPath && effectComposer.keyVal.key == comp.keyVal.key && effectComposer.keyVal.val == comp.keyVal.val;
            });
        if (it != effectFilterInfo.composers.end())
        {
            effectFilterInfo.composers.erase(it);
        }
    }

    effectFilterInfo.newComposers.clear();
    for (int i = 0; i < req.newcomposers_size(); ++i)
    {
        ls_effectfilter::Composer composer = req.newcomposers(i);
        EFFECT_COMPOSER comp;
        comp.effectPath = composer.effect_path();
        comp.keyVal.key = composer.key_val().key();
        comp.keyVal.val = composer.key_val().val();
        if (composer.has_composer_tag())
        {
            comp.composerTag = composer.composer_tag();
        }
        effectFilterInfo.newComposers.push_back(comp);

        auto it = std::find_if(effectFilterInfo.composers.begin(), effectFilterInfo.composers.end(),
                               [&comp](const EFFECT_COMPOSER& effectComposer) {
                                   return effectComposer.effectPath == comp.effectPath && effectComposer.keyVal.key == comp.keyVal.key;
                               });
        if (it != effectFilterInfo.composers.end())
        {
            it->keyVal.val = comp.keyVal.val;
            it->composerTag = comp.composerTag;
        }
        else
        {
            effectFilterInfo.composers.push_back(comp);
        }
    }
    filterInfo.filter = effectFilterInfo;

    controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_REPLACE_COMPOSERS);
    return true;
}

bool EffectFilter::SetText::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::SetText] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER filterInfo{};
    controller->GetFilterInfo(filterID, &filterInfo);

    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
    effectFilterInfo.keyText.key = req.key_text().key();
    effectFilterInfo.keyText.val = req.key_text().val();

    filterInfo.filter = effectFilterInfo;
    controller->ControlFilter(filterID, filterInfo, FILTER_CONTROL_SET_COMPOSER_TEXT);
    return true;
}

bool EffectFilter::SetBackgroundImage::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::SetBackgroundImage] filter not exist, filterID: " << filterID;
        return false;
    }

    FILTER iFilterInfo{};
    controller->GetFilterInfo(filterID, &iFilterInfo);

    EFFECT_FILTER iEffectFilterInfo = std::get<EFFECT_FILTER>(iFilterInfo.filter);
    iEffectFilterInfo.keyPath.key = req.key_path().key();
    iEffectFilterInfo.keyPath.val = req.key_path().val();

    iFilterInfo.filter = iEffectFilterInfo;
    controller->ControlFilter(filterID, iFilterInfo, FILTER_CONTROL_SET_BACKGROUND_IMAGE);

    FILTER oFilterInfo{};
    controller->GetFilterInfo(filterID, &oFilterInfo);
    EFFECT_FILTER oEffectFilterInfo = std::get<EFFECT_FILTER>(oFilterInfo.filter);
    rsp.set_success(oEffectFilterInfo.result);

    return true;
}

bool EffectFilter::SetPicQualityBrightness::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
	if (!controller->FindFilterByID(filterID))
	{
		LOG(ERROR) << "[EffectFilter::SetBackgroundImage] filter not exist, filterID: " << filterID;
		return false;
	}

	FILTER iFilterInfo{};
	controller->GetFilterInfo(filterID, &iFilterInfo);
	EFFECT_FILTER iEffectFilterInfo = std::get<EFFECT_FILTER>(iFilterInfo.filter);
	if (req.has_bright_config())
	{
		ls_effectfilter::PicQualityBrightness config = req.bright_config();
		if (config.has_enable())
		{
            iEffectFilterInfo.brightConfig.enable = config.enable();
		}
		if (config.has_is_auto())
		{
            iEffectFilterInfo.brightConfig.isAuto = config.is_auto();
		}
        if (config.has_asset_path())
        {
            iEffectFilterInfo.brightConfig.assetPath = config.asset_path();
        }
		if (config.has_key_val())
		{
            iEffectFilterInfo.brightConfig.keyVal.key = config.key_val().key();
            iEffectFilterInfo.brightConfig.keyVal.val = config.key_val().val();
		}
	}
	iFilterInfo.filter = iEffectFilterInfo;
	controller->ControlFilter(filterID, iFilterInfo, FILTER_CONTROL_SET_BRIGHT_CONFIG);

	return true;
}

bool EffectFilter::SetEffectFilterMsg::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
	if (!controller->FindFilterByID(filterID))
	{
		LOG(ERROR) << "[EffectFilter::SetText] filter not exist, filterID: " << filterID;
		return false;
	}

	FILTER iFilterInfo{};
	controller->GetFilterInfo(filterID, &iFilterInfo);
	EFFECT_FILTER iEffectFilterInfo = std::get<EFFECT_FILTER>(iFilterInfo.filter);
	iEffectFilterInfo.effectMsg.msgID = req.msg_id();
    iEffectFilterInfo.effectMsg.arg1 = req.arg1();
    iEffectFilterInfo.effectMsg.arg2 = req.arg2();
    iEffectFilterInfo.effectMsg.arg3 = req.arg3();
	iFilterInfo.filter = iEffectFilterInfo;
	controller->ControlFilter(filterID, iFilterInfo, FILTER_CONTROL_SET_EFFECT_MSG);

	return true;
}

bool EffectFilter::GetExclusion::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::GetExclusion] filter not exist, filterID: " << filterID;
        return false;
    }

	FILTER filterInfo{};
	controller->GetFilterInfo(filterID, &filterInfo);

	EFFECT_FILTER iEffectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
	iEffectFilterInfo.pathTag.key = req.path_tag().key();
	iEffectFilterInfo.pathTag.val = req.path_tag().val();
	filterInfo.filter = iEffectFilterInfo;
    controller->SetFilterInfo(filterID, &filterInfo);

    filterInfo = {};
	FILTER_INFO_CMD cmd = FILTER_INFO_COMPOSER_EXCLUSION;
	controller->GetFilterInfo(filterID, &filterInfo, cmd);

	EFFECT_FILTER oEffectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);
	rsp.set_exclusion(oEffectFilterInfo.exclusion);

	return true;
}

bool EffectFilter::GetEffectFilter::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	UINT64 filterID = 0;
	Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[EffectFilter::GetEffectFilter] filter not exist, filterID: " << filterID;
        return false;
    }

	FILTER filterInfo{};
	controller->GetFilterInfo(filterID, &filterInfo);
    EFFECT_FILTER effectFilterInfo = std::get<EFFECT_FILTER>(filterInfo.filter);

    ls_effectfilter::EffectFilter effect_filter{};
    for (const auto& comp : effectFilterInfo.composers)
    {
        auto composer = effect_filter.add_composers();
        composer->set_effect_path(comp.effectPath);

        ls_effectfilter::MapInfo map_info;
        map_info.set_key(comp.keyVal.key);
        map_info.set_val(comp.keyVal.val);
        composer->mutable_key_val()->CopyFrom(map_info);
    }

    ls_effectfilter::MapInfo key_path{};
    key_path.set_key(effectFilterInfo.keyPath.key);
    key_path.set_val(effectFilterInfo.keyPath.val);
    effect_filter.mutable_key_path()->CopyFrom(key_path);

    ls_effectfilter::PicQualityBrightness bright_config{};
    bright_config.set_enable(effectFilterInfo.brightConfig.enable);
    bright_config.set_is_auto(effectFilterInfo.brightConfig.isAuto);
    bright_config.set_asset_path(effectFilterInfo.brightConfig.assetPath);
    ls_effectfilter::MapInfo path_val{};
    path_val.set_key(effectFilterInfo.brightConfig.keyVal.key);
    path_val.set_val(effectFilterInfo.brightConfig.keyVal.val);
    bright_config.mutable_key_val()->CopyFrom(path_val);
    effect_filter.mutable_bright_config()->CopyFrom(bright_config);

    rsp.mutable_effect_filter()->CopyFrom(effect_filter);
	return true;
}
} // namespace MediaSDK