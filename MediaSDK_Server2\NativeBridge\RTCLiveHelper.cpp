#include "RTCLiveHelper.h"
#include "../BaseLib/json11.hpp"
#include "../mediasdk_v2_header/nlohmann/json.hpp"
RTCLiveHelper::RTCLiveHelper(sdk_helper::MediaSDKV2API* sdk_api, sdk_helper::CallHelper& call_helper)
    : sdk_api_(sdk_api), call_helper_(call_helper)
{
}

RTCLiveHelper::~RTCLiveHelper()
{
}

extern int g_video_fps;
bool RTCLiveHelper::StartRTC(const RTC_LINK& param)
{
    if (!sdk_api_)
        return false;
    nlohmann::json json_params;
    bool success = call_helper_.SyncCall<bool>(sdk_api_->CreateEngine, param.appID.c_str(), param.tncParams.c_str());
    if (!success)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC] Step1: CreateEngine error!";
        return false;
    }
    int ret = call_helper_.SyncCall<bool>(sdk_api_->SetBusinessId, param.businessID.c_str());
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC] Step2 SetBusinessId error! code = " << ret;
        return false;
    }

    ret = call_helper_.SyncCall<int>(sdk_api_->EnableLocalVideo, mediasdk::StreamIndex::kStreamIndexMain, true, param.videoModel);  // todo StreamIndex ��Դ����Ҫǰ�˴���
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC]  Step3 EnableLocalVideo error! code = " << ret << "param.track_id = " << param.videoModel;
        return false;
    }

    ret = call_helper_.SyncCall<bool>(sdk_api_->SetAudioProfile, mediasdk::kAudioProfileTypeHD);
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC]  Step4 SetAudioProfile error! code = " << ret;
        return false;
    }
    
    ret = call_helper_.SyncCall<bool>(sdk_api_->EnableAudioPropertiesReport, 1000);
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC] Step5 EnableAudioPropertiesReport error!  code = " << ret;
        return false;
    }

    ret = call_helper_.SyncCall<bool>(sdk_api_->EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexMain, true, 2); // todo StreamIndex ��Դ����Ҫǰ�˴���
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC] Step5 EnableLocalAudio error!  code = " << ret;
        return false;
    }

    json_params.clear();
    json_params["framerate"] = param.clipAreaInfo.fps > 0 ? param.clipAreaInfo.fps : g_video_fps;
    json_params["region"] = { param.clipAreaInfo.clip.X, param.clipAreaInfo.clip.Y,
                             param.clipAreaInfo.clip.Width, param.clipAreaInfo.clip.Height };
    json_params["scale"] = param.clipAreaInfo.scale;
    json_params["intermediate_output_scale"] = param.clipAreaInfo.outputScale;
    json_params["max_bitrate"] = param.clipAreaInfo.maxBitrate;
    json_params["min_bitrate"] = param.clipAreaInfo.minBitrate;
    json_params["enable_simulcast"] = false;
    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerUpdateClipScaleInfo] UpdateCropAndScale  json_params.dump().c_str() = " << json_params.dump();
    ret = call_helper_.SyncCall<bool>(sdk_api_->UpdateCropAndScale, mediasdk::StreamIndex::kStreamIndexMain, json_params.dump().c_str()); // todo StreamIndex ��Դ����Ҫǰ�˴���
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC]  Step8 UpdateCropAndScale error! code = " << ret;
        return false;
    }
    
    LOG(INFO) << "[StartRTC] JoinRoom param.room_id = " << param.roomInfo.roomID;
    ret = call_helper_.SyncCall<bool>(sdk_api_->JoinRoom, param.roomInfo.roomID.c_str(), param.userID.c_str(), param.roomInfo.token.c_str(), "");
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCLiveHelper::StartRTC]  Step9 JoinRoom error! code = " << ret;
        return false;
    }
    return true;
}

bool RTCLiveHelper::StartLiveTranscoding(const std::string& jsonArgs)
{
    LOG(INFO) << "[StartLiveTranscoding]  jsonArgs = " << jsonArgs;
    auto params = ParseLiveTranscodeingTasks(jsonArgs);
    int ret = 0;
    for (auto& kp : params)
    {
        ret = call_helper_.SyncCall<int>(sdk_api_->StartLiveTranscoding, kp.first.c_str(), kp.second.c_str());
        if (ret != 0)
        {
            LOG(ERROR) << "[StartLiveTranscoding] error task_id: " << kp.first;
            break;
        }
    }
    return ret == 0;
}

bool RTCLiveHelper::UpdateLiveTranscoding(const std::string& jsonArgs)
{
    LOG(INFO) << "[UpdateLiveTranscoding]  jsonArgs = " << jsonArgs;
    auto params = ParseLiveTranscodeingTasks(jsonArgs);
    int ret = 0;
    for (auto& kp : params)
    {
        ret = call_helper_.SyncCall<int>(sdk_api_->UpdateLiveTranscoding, kp.first.c_str(), kp.second.c_str());
        if (ret != 0)
        {
            LOG(ERROR) << "[UpdateLiveTranscoding] error task_id: " << kp.first;
            break;
        }
    }
    return ret == 0;
}

bool RTCLiveHelper::StopLiveTranscoding(const std::string& jsonArgs)
{
    std::string err;
    int ret = false;
    bool success = false;
    try
    {
        do
        {
            auto        configs = json11::Json::parse(jsonArgs, err).array_items();
            if (!err.empty())
            {
                LOG_LINE(ERROR) << "[RTCLiveHelper] Stop parse error" << err;
                break;
            }

            if (sdk_api_)
            {
                for (auto taskID : configs)
                {
                    ret = call_helper_.SyncCall<int>(sdk_api_->StopLiveTranscoding, taskID.string_value().c_str());
                    if (ret != 0)
                    {
                        LOG(ERROR) << "[StopLiveTranscoding] error task_id: " << taskID.string_value();
                        break;
                    }
                }
            }
            success = true;
        } while (0);
    }
    catch (...){}

    return success;
}

std::map<std::string, std::string> RTCLiveHelper::ParseLiveTranscodeingTasks(const std::string& taskConfigs)
{
    std::map<std::string, std::string> ret;

    if (taskConfigs.empty())
    {
        LOG_LINE(ERROR) << "[RTCLiveHelper] empty taskConfigs";
        return ret;
    }

    std::string err;
    auto        configs = json11::Json::parse(taskConfigs, err).object_items();
    if (!err.empty())
    {
        LOG_LINE(ERROR) << "[RTCLiveHelper] taskConfigs parse failed" << err;
        return ret;
    }

    for (auto& kp : configs)
    {
        auto value = json11::Json(kp.second).dump();
        LOG_LINE(INFO) << "[RTCLiveHelper] LiveTranscode Task ID: " << kp.first << " config:" << value;
        ret[kp.first] = std::move(value);
    }

    return ret;
}


