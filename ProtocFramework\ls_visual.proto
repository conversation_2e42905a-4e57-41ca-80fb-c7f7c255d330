// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_visual;
import "ls_base.proto";
import "ls_basicenum.proto";

enum VISUAL_TYPE
{
    VISUAL_NONE = 0;
    VISUAL_WINDOW = 1;
    VISUAL_GAME = 2;
	VISUAL_MONITOR = 3;
	VISUAL_FAV = 4;
    VISUAL_IMAGE = 5;
    VISUAL_CAMERA = 6;
    VISUAL_ANALOG = 7;
    VISUAL_BROWSER = 8;
	VISUAL_BYTELINK = 9;
    VISUAL_GRAFFITI = 10;
	VISUAL_RTC = 11;
	VISUAL_VIRTUAL_CAMERA = 12;
};

enum VISUAL_PREVIEW_FILL
{
    PREVIEW_RATIO_FILL = 0;
    PREVIEW_TILE_FILL = 1;
    PREVIEW_CLIP_FILL = 2;
};

enum VISUAL_FIXED_EDGE
{
	FIXED_EDGE_NONE = 0;
	FIXED_EDGE_WIDTH = 1;
	FIXED_EDGE_HEIGHT = 2;
	FIXED_EDGE_LONGEST = 3;
	FIXED_EDGE_SHORTEST = 4;
}

enum VISUAL_FLAG
{
    VISUAL_FLAG_NO_FLAG = 0;
    VISUAL_FLAG_SUCCESS_STATE = 1;
    VISUAL_FLAG_VISIBLE_STATE = 2;
    VISUAL_FLAG_ACTIVE_STATE = 3;
    VISUAL_FLAG_LOCK_STATE = 4;
    VISUAL_FLAG_SELECT_STATE = 5;
    VISUAL_FLAG_HITTEST_STATE = 6;
    VISUAL_FLAG_FLAG_SYNC = 7;
    VISUAL_FLAG_FLAG_ASYNC = 8;
    VISUAL_FLAG_OUTPUT_FILTER = 9;
    VISUAL_FLAG_RENDER_FILTER = 10;
    VISUAL_FLAG_RTC_SCREEN_FILTER = 11;
    VISUAL_FLAG_RTC_OUTPUT = 12;
    VISUAL_FLAG_FLAG_NOT_PREVIEW_BUT_OUTPUT = 13;
    VISUAL_FLAG_ALWAYS_TOP = 14;
    VISUAL_FLAG_AVOID_DESTROY_ALL = 15;
    VISUAL_FLAG_RTC_NOT_OUTPUT = 16;
    VISUAL_FLAG_RTC_ADD_TO_OUTPUT = 17;
	VISUAL_FLAG_RTC_NOT_OUTPUT_TO_SCREEN = 18;
	VISUAL_FLAG_RTC_ADD_TO_OUTPUT_SCREEN = 19;
}

enum FILTER_TYPE 
{
	FILTER_NONE = 0;
	FILTER_AUDIO = 1;
	FILTER_VISUAL = 2;
	FILTER_EFFECT = 3;
}

message FILTER_INFO {
	string filter_id = 1;
	FILTER_TYPE type = 2;
}

message VisualUniAttr
{
	optional VISUAL_TYPE type = 1;
	optional ls_base.SizeF size = 2;
	optional ls_base.SizeF canvas_size = 3;
	optional ls_base.Transform transform = 4;
	optional ls_base.ClipF prepare_clip = 5;
	optional ls_basicenum.VISUAL_LAYOUT layout = 6;
	optional float fps = 7;
	optional bool visible = 8;
	optional bool locked = 9;
	optional VISUAL_FIXED_EDGE fixed_edge = 10;
	optional ls_base.ClipF move_range = 11;
	optional ls_base.ClipF ref_layout = 12;
	repeated VISUAL_FLAG visual_flags = 13;
}

message Remove {
	message Request {
		repeated string visual_ids = 1;
	}
}

message IsEmpty {
	message Request {
		string visual_id = 1;
	}

	message Response {
		bool empty = 1;
	}
}

message VisualIsReady {
	message Request {
		string visual_id = 1;
	}

	message Response {
		bool is_ready = 1;
	}
}

message SetVisualUniAttr {
	message Request {
		string visual_id = 1;
		VisualUniAttr visual_uni_attr = 2;
	}

	message Response {
		VisualUniAttr visual_uni_attr = 1;
	}
}

message GetVisualUniAttr {
	message Request {
		string visual_id = 1;
	}

	message Response {
		VisualUniAttr visual_uni_attr = 1;
	}
}

message Select {
	message Request {
		string visual_id = 1;
	}
}

message UnSelect {
	message Request {}
}

message MoveOrder {
	message Request {
		string visual_id = 1;
		ls_basicenum.MOVE_ORDER move_order = 2;
	}
}

message ClipVisual {
	message Request {
		string visual_id = 1;
	}
}

message GetVisualSnapshot {
	message Request {
		string visual_id = 1;
		string path = 2;
	}
}

message GetVisualSnapshot2 {
	message Request {
		string visual_id = 1;
		string path = 2;
	}
}

message AddFilter {
	message Request {
		string visual_id = 1;
		string filter_id = 2;
	}
}

message RemoveFilter {
	message Request {
		string visual_id = 1;
		string filter_id = 2;
	}
}

message ResetFilterOrder {
	message Request {
		string visual_id = 1;
		repeated string filter_ids = 2;
	}
}

message GetEffectProfiler {
	message Request {
		string visual_id = 1;
	}

	message Response {
		sint32 before_effect_fps = 1;
		sint32 after_effect_fps = 2;
		float effect_achieve_rate = 3;
		float tick_achieve_rate = 4;
	}
}

message VisualPreviewAttr
{
	optional ls_base.SizeF size = 1;
	optional ls_base.Transform transform = 2;
	optional int32 bk_color = 3; // function rgbToInt(r, g, b) { return ((r << 16) | (g << 8) | b); }
	optional VISUAL_PREVIEW_FILL fill_type = 4;
}

message StartVisualPreview {
	message Request {
		string visual_id = 1;
		uint64 parent_hwnd = 2;
		VisualPreviewAttr preview_attr = 3;
		bool is_popUp = 4;
		float top_border_radius = 5;
		float bottom_border_radius = 6;
		float opacity = 7;
	}
	message Response {
		sint32 error_code = 1;
		string visual_preview_id = 2;
	}
}

message StopVisualPreview {
	message Request {
		string visual_id = 1;
		string visual_preview_id = 2;
	}
	message Response {
		sint32 error_code = 1;
	}
}

message SetVisualPreviewParams {
    message Request {
		string visual_id = 1;
		string visual_preview_id = 2;
		float top_border_radius = 3;
		float bottom_border_radius = 4;
		float opacity = 5;
    }
	message Response {
		sint32 error_code = 1;
	}
}

message SetVisualPreviewLayout {
	message Request {
		string visual_id = 1;
		string visual_preview_id = 2;
		VisualPreviewAttr preview_attr = 3;
	}
	message Response {
		sint32 error_code = 1;
	}
}

message CreateFrame {
	message Request {
		string media_id = 1;
		ls_base.SizeF resize = 2;
		ls_base.ClipF clip = 3;
		ls_basicenum.OBJECT_FIT_MODE fit_mode = 4;
	}

	message Response {
		string frame_id = 1;
	}
}