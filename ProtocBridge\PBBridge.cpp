﻿#include "stdafx.h"
#include "PBBridge.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "layout/canvas.h"
#include "layout/preview.h"
#include "visual/layer.h"
#include "visual/source.h"
#include "layout/layout.h"
#include "output/rtmp.h"
#include "output/rtc.h"
#include "audio/audio.h"
#include "visual/visual.h"
#include "visual/analog.h"
#include "visual/browser.h"
#include "visual/bytelink.h"
#include "visual/camera.h"
#include "visual/virtualcamera.h"
#include "visual/fav.h"
#include "visual/game.h"
#include "visual/graffiti.h"
#include "visual/image.h"
#include "visual/monitor.h"
#include "visual/window.h"
#include "visual/phonecamera.h"
#include "filter/audiofilter.h"
#include "filter/visualfilter.h"
#include "filter/effectfilter.h"
#include "filter/canvasfilter.h"
#include "state/state.h"
#include "utils/config.h"
#include "utils/watchdog.h"
#include "vqm/VQMPBModule.h"
#include "proxycall/proxycall.h"
#include "aiengine/aiengine.h"
#include "visual/rawinputdetection.h"

namespace LS
{
IMPLEMENT_INSTANCE(PBBridge)

void PBBridge::Open(IIPCMgr* ipcMgr, IController* controller)
{
    m_ipcMgr = ipcMgr;
    m_controller = controller;

	PB::Router::GetInstance().RegisterModule(new LSCanvas());
	PB::Router::GetInstance().RegisterModule(new LSLayer());
	PB::Router::GetInstance().RegisterModule(new LSSource());
    PB::Router::GetInstance().RegisterModule(new Preview());
    PB::Router::GetInstance().RegisterModule(new Layout());
    PB::Router::GetInstance().RegisterModule(new RTMP());
    PB::Router::GetInstance().RegisterModule(new RTC());
    PB::Router::GetInstance().RegisterModule(new Visual());
    PB::Router::GetInstance().RegisterModule(new Analog());
    PB::Router::GetInstance().RegisterModule(new Audio());
    PB::Router::GetInstance().RegisterModule(new Browser());
    PB::Router::GetInstance().RegisterModule(new Bytelink());
    PB::Router::GetInstance().RegisterModule(new Camera());
    PB::Router::GetInstance().RegisterModule(new VirtualCamera());
    PB::Router::GetInstance().RegisterModule(new Fav());
    PB::Router::GetInstance().RegisterModule(new Game());
    PB::Router::GetInstance().RegisterModule(new Graffiti());
    PB::Router::GetInstance().RegisterModule(new Image());
    PB::Router::GetInstance().RegisterModule(new Monitor());
    PB::Router::GetInstance().RegisterModule(new Window());
    PB::Router::GetInstance().RegisterModule(new PhoneCamera());
    PB::Router::GetInstance().RegisterModule(new AudioFilter());
    PB::Router::GetInstance().RegisterModule(new VisualFilter());
    PB::Router::GetInstance().RegisterModule(new EffectFilter());
    PB::Router::GetInstance().RegisterModule(new CanvasFilter());
    PB::Router::GetInstance().RegisterModule(new State());
    PB::Router::GetInstance().RegisterModule(new Config());
    PB::Router::GetInstance().RegisterModule(new WatchDog());
    PB::Router::GetInstance().RegisterModule(new VQMPBModule());
    PB::Router::GetInstance().RegisterModule(new ProxyCallModule());
    PB::Router::GetInstance().RegisterModule(new AIEngine());
    PB::Router::GetInstance().RegisterModule(new RawInputDeviceDecection());

#if defined(_WIN64)
    // PB::Router::GetInstance().RegisterModule(new Mi7());
#endif // _WIN64
    PB::Sender::GetInstance().MESSAGE_RESPONSE += Delegate<void(const std::string&, const std::string&)>(this, &PBBridge::RoutePBMessage);
    PB::Router::GetInstance().ROUTE_RESPONSE += Delegate<void(const std::string&, const std::string&)>(this, &PBBridge::RoutePBMessage);
}

void PBBridge::Close()
{
    m_controller = nullptr;
}

void PBBridge::OnPBRequest(const std::string& messageID, const std::string& body)
{
    PB::Router::GetInstance().Route(messageID, body);
}

void PBBridge::RoutePBMessage(const std::string& header, const std::string& body)
{
    if (m_ipcMgr)
    {
        m_ipcMgr->SendPBMessage(header, body);
    }
}

bool PBBridge::IsClosed()
{
    return m_controller == nullptr;
}

IIPCMgr* PBBridge::GetIPCMgr()
{
	return m_ipcMgr;
}

IController* PBBridge::GetController()
{
    return m_controller;
}

}
